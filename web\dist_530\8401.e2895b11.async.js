!(function(){"use strict";var he=Object.defineProperty,be=Object.defineProperties;var me=Object.getOwnPropertyDescriptors;var ie=Object.getOwnPropertySymbols;var _e=Object.prototype.hasOwnProperty,we=Object.prototype.propertyIsEnumerable;var ne=(k,_,b)=>_ in k?he(k,_,{enumerable:!0,configurable:!0,writable:!0,value:b}):k[_]=b,ae=(k,_)=>{for(var b in _||(_={}))_e.call(_,b)&&ne(k,b,_[b]);if(ie)for(var b of ie(_))we.call(_,b)&&ne(k,b,_[b]);return k},oe=(k,_)=>be(k,me(_));var m=(k,_,b)=>new Promise((N,O)=>{var R=x=>{try{F(b.next(x))}catch(C){O(C)}},K=x=>{try{F(b.throw(x))}catch(C){O(C)}},F=x=>x.done?N(x.value):Promise.resolve(x.value).then(R,K);F((b=b.apply(k,_)).next())});(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8401],{78401:function(k,_,b){var p;b.r(_),b.d(_,{Adapter:function(){return S},CodeActionAdaptor:function(){return q},DefinitionAdapter:function(){return G},DiagnosticsAdapter:function(){return V},DocumentHighlightAdapter:function(){return J},FormatAdapter:function(){return Y},FormatHelper:function(){return A},FormatOnTypeAdapter:function(){return Z},InlayHintsAdapter:function(){return te},Kind:function(){return f},LibFiles:function(){return j},OutlineAdapter:function(){return X},QuickInfoAdapter:function(){return z},ReferenceAdapter:function(){return Q},RenameAdapter:function(){return ee},SignatureHelpAdapter:function(){return $},SuggestAdapter:function(){return B},WorkerManager:function(){return W},flattenDiagnosticMessageText:function(){return P},getJavaScriptWorker:function(){return de},getTypeScriptWorker:function(){return pe},setupJavaScript:function(){return ge},setupTypeScript:function(){return le}});var N=b(89732),O=b(39585);var R=Object.defineProperty,K=Object.getOwnPropertyDescriptor,F=Object.getOwnPropertyNames,x=Object.prototype.hasOwnProperty,C=(e,t,o,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let c of F(t))!x.call(e,c)&&c!==o&&R(e,c,{get:()=>t[c],enumerable:!(r=K(t,c))||r.enumerable});return e},ue=(e,t,o)=>(C(e,t,"default"),o&&C(o,t,"default")),i={};ue(i,N);var W=class{constructor(e,t){this._modeId=e,this._defaults=t,this._worker=null,this._client=null,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker()),this._updateExtraLibsToken=0,this._extraLibsChangeListener=this._defaults.onDidExtraLibsChange(()=>this._updateExtraLibs())}dispose(){this._configChangeListener.dispose(),this._extraLibsChangeListener.dispose(),this._stopWorker()}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}_updateExtraLibs(){return m(this,null,function*(){if(!this._worker)return;const e=++this._updateExtraLibsToken,t=yield this._worker.getProxy();this._updateExtraLibsToken===e&&t.updateExtraLibs(this._defaults.getExtraLibs())})}_getClient(){return this._client||(this._client=m(this,null,function*(){return this._worker=i.editor.createWebWorker({moduleId:"vs/language/typescript/tsWorker",label:this._modeId,keepIdleModels:!0,createData:{compilerOptions:this._defaults.getCompilerOptions(),extraLibs:this._defaults.getExtraLibs(),customWorkerPath:this._defaults.workerOptions.customWorkerPath,inlayHintsOptions:this._defaults.inlayHintsOptions}}),this._defaults.getEagerModelSync()?yield this._worker.withSyncedResources(i.editor.getModels().filter(e=>e.getLanguageId()===this._modeId).map(e=>e.uri)):yield this._worker.getProxy()})),this._client}getLanguageServiceWorker(...e){return m(this,null,function*(){const t=yield this._getClient();return this._worker&&(yield this._worker.withSyncedResources(e)),t})}},s={};s["lib.d.ts"]=!0,s["lib.decorators.d.ts"]=!0,s["lib.decorators.legacy.d.ts"]=!0,s["lib.dom.asynciterable.d.ts"]=!0,s["lib.dom.d.ts"]=!0,s["lib.dom.iterable.d.ts"]=!0,s["lib.es2015.collection.d.ts"]=!0,s["lib.es2015.core.d.ts"]=!0,s["lib.es2015.d.ts"]=!0,s["lib.es2015.generator.d.ts"]=!0,s["lib.es2015.iterable.d.ts"]=!0,s["lib.es2015.promise.d.ts"]=!0,s["lib.es2015.proxy.d.ts"]=!0,s["lib.es2015.reflect.d.ts"]=!0,s["lib.es2015.symbol.d.ts"]=!0,s["lib.es2015.symbol.wellknown.d.ts"]=!0,s["lib.es2016.array.include.d.ts"]=!0,s["lib.es2016.d.ts"]=!0,s["lib.es2016.full.d.ts"]=!0,s["lib.es2016.intl.d.ts"]=!0,s["lib.es2017.d.ts"]=!0,s["lib.es2017.date.d.ts"]=!0,s["lib.es2017.full.d.ts"]=!0,s["lib.es2017.intl.d.ts"]=!0,s["lib.es2017.object.d.ts"]=!0,s["lib.es2017.sharedmemory.d.ts"]=!0,s["lib.es2017.string.d.ts"]=!0,s["lib.es2017.typedarrays.d.ts"]=!0,s["lib.es2018.asyncgenerator.d.ts"]=!0,s["lib.es2018.asynciterable.d.ts"]=!0,s["lib.es2018.d.ts"]=!0,s["lib.es2018.full.d.ts"]=!0,s["lib.es2018.intl.d.ts"]=!0,s["lib.es2018.promise.d.ts"]=!0,s["lib.es2018.regexp.d.ts"]=!0,s["lib.es2019.array.d.ts"]=!0,s["lib.es2019.d.ts"]=!0,s["lib.es2019.full.d.ts"]=!0,s["lib.es2019.intl.d.ts"]=!0,s["lib.es2019.object.d.ts"]=!0,s["lib.es2019.string.d.ts"]=!0,s["lib.es2019.symbol.d.ts"]=!0,s["lib.es2020.bigint.d.ts"]=!0,s["lib.es2020.d.ts"]=!0,s["lib.es2020.date.d.ts"]=!0,s["lib.es2020.full.d.ts"]=!0,s["lib.es2020.intl.d.ts"]=!0,s["lib.es2020.number.d.ts"]=!0,s["lib.es2020.promise.d.ts"]=!0,s["lib.es2020.sharedmemory.d.ts"]=!0,s["lib.es2020.string.d.ts"]=!0,s["lib.es2020.symbol.wellknown.d.ts"]=!0,s["lib.es2021.d.ts"]=!0,s["lib.es2021.full.d.ts"]=!0,s["lib.es2021.intl.d.ts"]=!0,s["lib.es2021.promise.d.ts"]=!0,s["lib.es2021.string.d.ts"]=!0,s["lib.es2021.weakref.d.ts"]=!0,s["lib.es2022.array.d.ts"]=!0,s["lib.es2022.d.ts"]=!0,s["lib.es2022.error.d.ts"]=!0,s["lib.es2022.full.d.ts"]=!0,s["lib.es2022.intl.d.ts"]=!0,s["lib.es2022.object.d.ts"]=!0,s["lib.es2022.regexp.d.ts"]=!0,s["lib.es2022.sharedmemory.d.ts"]=!0,s["lib.es2022.string.d.ts"]=!0,s["lib.es2023.array.d.ts"]=!0,s["lib.es2023.collection.d.ts"]=!0,s["lib.es2023.d.ts"]=!0,s["lib.es2023.full.d.ts"]=!0,s["lib.es5.d.ts"]=!0,s["lib.es6.d.ts"]=!0,s["lib.esnext.collection.d.ts"]=!0,s["lib.esnext.d.ts"]=!0,s["lib.esnext.decorators.d.ts"]=!0,s["lib.esnext.disposable.d.ts"]=!0,s["lib.esnext.full.d.ts"]=!0,s["lib.esnext.intl.d.ts"]=!0,s["lib.esnext.object.d.ts"]=!0,s["lib.esnext.promise.d.ts"]=!0,s["lib.scripthost.d.ts"]=!0,s["lib.webworker.asynciterable.d.ts"]=!0,s["lib.webworker.d.ts"]=!0,s["lib.webworker.importscripts.d.ts"]=!0,s["lib.webworker.iterable.d.ts"]=!0;function P(e,t,o=0){if(typeof e=="string")return e;if(e===void 0)return"";let r="";if(o){r+=t;for(let c=0;c<o;c++)r+="  "}if(r+=e.messageText,o++,e.next)for(const c of e.next)r+=P(c,t,o);return r}function v(e){return e?e.map(t=>t.text).join(""):""}var S=class{constructor(e){this._worker=e}_textSpanToRange(e,t){let o=e.getPositionAt(t.start),r=e.getPositionAt(t.start+t.length),{lineNumber:c,column:u}=o,{lineNumber:l,column:a}=r;return{startLineNumber:c,startColumn:u,endLineNumber:l,endColumn:a}}},j=class{constructor(e){this._worker=e,this._libFiles={},this._hasFetchedLibFiles=!1,this._fetchLibFilesPromise=null}isLibFile(e){return e&&e.path.indexOf("/lib.")===0?!!s[e.path.slice(1)]:!1}getOrCreateModel(e){const t=i.Uri.parse(e),o=i.editor.getModel(t);if(o)return o;if(this.isLibFile(t)&&this._hasFetchedLibFiles)return i.editor.createModel(this._libFiles[t.path.slice(1)],"typescript",t);const r=O.TG.getExtraLibs()[e];return r?i.editor.createModel(r.content,"typescript",t):null}_containsLibFile(e){for(let t of e)if(this.isLibFile(t))return!0;return!1}fetchLibFilesIfNecessary(e){return m(this,null,function*(){this._containsLibFile(e)&&(yield this._fetchLibFiles())})}_fetchLibFiles(){return this._fetchLibFilesPromise||(this._fetchLibFilesPromise=this._worker().then(e=>e.getLibFiles()).then(e=>{this._hasFetchedLibFiles=!0,this._libFiles=e})),this._fetchLibFilesPromise}},V=class extends S{constructor(e,t,o,r){super(r),this._libFiles=e,this._defaults=t,this._selector=o,this._disposables=[],this._listener=Object.create(null);const c=a=>{if(a.getLanguageId()!==o)return;const n=()=>{const{onlyVisible:h}=this._defaults.getDiagnosticsOptions();h?a.isAttachedToEditor()&&this._doValidate(a):this._doValidate(a)};let g;const d=a.onDidChangeContent(()=>{clearTimeout(g),g=window.setTimeout(n,500)}),w=a.onDidChangeAttached(()=>{const{onlyVisible:h}=this._defaults.getDiagnosticsOptions();h&&(a.isAttachedToEditor()?n():i.editor.setModelMarkers(a,this._selector,[]))});this._listener[a.uri.toString()]={dispose(){d.dispose(),w.dispose(),clearTimeout(g)}},n()},u=a=>{i.editor.setModelMarkers(a,this._selector,[]);const n=a.uri.toString();this._listener[n]&&(this._listener[n].dispose(),delete this._listener[n])};this._disposables.push(i.editor.onDidCreateModel(a=>c(a))),this._disposables.push(i.editor.onWillDisposeModel(u)),this._disposables.push(i.editor.onDidChangeModelLanguage(a=>{u(a.model),c(a.model)})),this._disposables.push({dispose(){for(const a of i.editor.getModels())u(a)}});const l=()=>{for(const a of i.editor.getModels())u(a),c(a)};this._disposables.push(this._defaults.onDidChange(l)),this._disposables.push(this._defaults.onDidExtraLibsChange(l)),i.editor.getModels().forEach(a=>c(a))}dispose(){this._disposables.forEach(e=>e&&e.dispose()),this._disposables=[]}_doValidate(e){return m(this,null,function*(){const t=yield this._worker(e.uri);if(e.isDisposed())return;const o=[],{noSyntaxValidation:r,noSemanticValidation:c,noSuggestionDiagnostics:u}=this._defaults.getDiagnosticsOptions();r||o.push(t.getSyntacticDiagnostics(e.uri.toString())),c||o.push(t.getSemanticDiagnostics(e.uri.toString())),u||o.push(t.getSuggestionDiagnostics(e.uri.toString()));const l=yield Promise.all(o);if(!l||e.isDisposed())return;const a=l.reduce((g,d)=>d.concat(g),[]).filter(g=>(this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore||[]).indexOf(g.code)===-1),n=a.map(g=>g.relatedInformation||[]).reduce((g,d)=>d.concat(g),[]).map(g=>g.file?i.Uri.parse(g.file.fileName):null);yield this._libFiles.fetchLibFilesIfNecessary(n),!e.isDisposed()&&i.editor.setModelMarkers(e,this._selector,a.map(g=>this._convertDiagnostics(e,g)))})}_convertDiagnostics(e,t){const o=t.start||0,r=t.length||1,{lineNumber:c,column:u}=e.getPositionAt(o),{lineNumber:l,column:a}=e.getPositionAt(o+r),n=[];return t.reportsUnnecessary&&n.push(i.MarkerTag.Unnecessary),t.reportsDeprecated&&n.push(i.MarkerTag.Deprecated),{severity:this._tsDiagnosticCategoryToMarkerSeverity(t.category),startLineNumber:c,startColumn:u,endLineNumber:l,endColumn:a,message:P(t.messageText,`
`),code:t.code.toString(),tags:n,relatedInformation:this._convertRelatedInformation(e,t.relatedInformation)}}_convertRelatedInformation(e,t){if(!t)return[];const o=[];return t.forEach(r=>{let c=e;if(r.file&&(c=this._libFiles.getOrCreateModel(r.file.fileName)),!c)return;const u=r.start||0,l=r.length||1,{lineNumber:a,column:n}=c.getPositionAt(u),{lineNumber:g,column:d}=c.getPositionAt(u+l);o.push({resource:c.uri,startLineNumber:a,startColumn:n,endLineNumber:g,endColumn:d,message:P(r.messageText,`
`)})}),o}_tsDiagnosticCategoryToMarkerSeverity(e){switch(e){case 1:return i.MarkerSeverity.Error;case 3:return i.MarkerSeverity.Info;case 0:return i.MarkerSeverity.Warning;case 2:return i.MarkerSeverity.Hint}return i.MarkerSeverity.Info}},B=class M extends S{get triggerCharacters(){return["."]}provideCompletionItems(t,o,r,c){return m(this,null,function*(){const u=t.getWordUntilPosition(o),l=new i.Range(o.lineNumber,u.startColumn,o.lineNumber,u.endColumn),a=t.uri,n=t.getOffsetAt(o),g=yield this._worker(a);if(t.isDisposed())return;const d=yield g.getCompletionsAtPosition(a.toString(),n);return!d||t.isDisposed()?void 0:{suggestions:d.entries.map(h=>{let D=l;if(h.replacementSpan){const T=t.getPositionAt(h.replacementSpan.start),I=t.getPositionAt(h.replacementSpan.start+h.replacementSpan.length);D=new i.Range(T.lineNumber,T.column,I.lineNumber,I.column)}const L=[];return h.kindModifiers!==void 0&&h.kindModifiers.indexOf("deprecated")!==-1&&L.push(i.languages.CompletionItemTag.Deprecated),{uri:a,position:o,offset:n,range:D,label:h.name,insertText:h.name,sortText:h.sortText,kind:M.convertKind(h.kind),tags:L}})}})}resolveCompletionItem(t,o){return m(this,null,function*(){const r=t,c=r.uri,u=r.position,l=r.offset,n=yield(yield this._worker(c)).getCompletionEntryDetails(c.toString(),l,r.label);return n?{uri:c,position:u,label:n.name,kind:M.convertKind(n.kind),detail:v(n.displayParts),documentation:{value:M.createDocumentationString(n)}}:r})}static convertKind(t){switch(t){case f.primitiveType:case f.keyword:return i.languages.CompletionItemKind.Keyword;case f.variable:case f.localVariable:return i.languages.CompletionItemKind.Variable;case f.memberVariable:case f.memberGetAccessor:case f.memberSetAccessor:return i.languages.CompletionItemKind.Field;case f.function:case f.memberFunction:case f.constructSignature:case f.callSignature:case f.indexSignature:return i.languages.CompletionItemKind.Function;case f.enum:return i.languages.CompletionItemKind.Enum;case f.module:return i.languages.CompletionItemKind.Module;case f.class:return i.languages.CompletionItemKind.Class;case f.interface:return i.languages.CompletionItemKind.Interface;case f.warning:return i.languages.CompletionItemKind.File}return i.languages.CompletionItemKind.Property}static createDocumentationString(t){let o=v(t.documentation);if(t.tags)for(const r of t.tags)o+=`

${U(r)}`;return o}};function U(e){let t=`*@${e.name}*`;if(e.name==="param"&&e.text){const[o,...r]=e.text;t+=`\`${o.text}\``,r.length>0&&(t+=` \u2014 ${r.map(c=>c.text).join(" ")}`)}else Array.isArray(e.text)?t+=` \u2014 ${e.text.map(o=>o.text).join(" ")}`:e.text&&(t+=` \u2014 ${e.text}`);return t}var $=class ce extends S{constructor(){super(...arguments),this.signatureHelpTriggerCharacters=["(",","]}static _toSignatureHelpTriggerReason(t){switch(t.triggerKind){case i.languages.SignatureHelpTriggerKind.TriggerCharacter:return t.triggerCharacter?t.isRetrigger?{kind:"retrigger",triggerCharacter:t.triggerCharacter}:{kind:"characterTyped",triggerCharacter:t.triggerCharacter}:{kind:"invoked"};case i.languages.SignatureHelpTriggerKind.ContentChange:return t.isRetrigger?{kind:"retrigger"}:{kind:"invoked"};case i.languages.SignatureHelpTriggerKind.Invoke:default:return{kind:"invoked"}}}provideSignatureHelp(t,o,r,c){return m(this,null,function*(){const u=t.uri,l=t.getOffsetAt(o),a=yield this._worker(u);if(t.isDisposed())return;const n=yield a.getSignatureHelpItems(u.toString(),l,{triggerReason:ce._toSignatureHelpTriggerReason(c)});if(!n||t.isDisposed())return;const g={activeSignature:n.selectedItemIndex,activeParameter:n.argumentIndex,signatures:[]};return n.items.forEach(d=>{const w={label:"",parameters:[]};w.documentation={value:v(d.documentation)},w.label+=v(d.prefixDisplayParts),d.parameters.forEach((h,D,L)=>{const T=v(h.displayParts),I={label:T,documentation:{value:v(h.documentation)}};w.label+=T,w.parameters.push(I),D<L.length-1&&(w.label+=v(d.separatorDisplayParts))}),w.label+=v(d.suffixDisplayParts),g.signatures.push(w)}),{value:g,dispose(){}}})}},z=class extends S{provideHover(e,t,o){return m(this,null,function*(){const r=e.uri,c=e.getOffsetAt(t),u=yield this._worker(r);if(e.isDisposed())return;const l=yield u.getQuickInfoAtPosition(r.toString(),c);if(!l||e.isDisposed())return;const a=v(l.documentation),n=l.tags?l.tags.map(d=>U(d)).join(`  

`):"",g=v(l.displayParts);return{range:this._textSpanToRange(e,l.textSpan),contents:[{value:"```typescript\n"+g+"\n```\n"},{value:a+(n?`

`+n:"")}]}})}},J=class extends S{provideDocumentHighlights(e,t,o){return m(this,null,function*(){const r=e.uri,c=e.getOffsetAt(t),u=yield this._worker(r);if(e.isDisposed())return;const l=yield u.getDocumentHighlights(r.toString(),c,[r.toString()]);if(!(!l||e.isDisposed()))return l.flatMap(a=>a.highlightSpans.map(n=>({range:this._textSpanToRange(e,n.textSpan),kind:n.kind==="writtenReference"?i.languages.DocumentHighlightKind.Write:i.languages.DocumentHighlightKind.Text})))})}},G=class extends S{constructor(e,t){super(t),this._libFiles=e}provideDefinition(e,t,o){return m(this,null,function*(){const r=e.uri,c=e.getOffsetAt(t),u=yield this._worker(r);if(e.isDisposed())return;const l=yield u.getDefinitionAtPosition(r.toString(),c);if(!l||e.isDisposed()||(yield this._libFiles.fetchLibFilesIfNecessary(l.map(n=>i.Uri.parse(n.fileName))),e.isDisposed()))return;const a=[];for(let n of l){const g=this._libFiles.getOrCreateModel(n.fileName);g&&a.push({uri:g.uri,range:this._textSpanToRange(g,n.textSpan)})}return a})}},Q=class extends S{constructor(e,t){super(t),this._libFiles=e}provideReferences(e,t,o,r){return m(this,null,function*(){const c=e.uri,u=e.getOffsetAt(t),l=yield this._worker(c);if(e.isDisposed())return;const a=yield l.getReferencesAtPosition(c.toString(),u);if(!a||e.isDisposed()||(yield this._libFiles.fetchLibFilesIfNecessary(a.map(g=>i.Uri.parse(g.fileName))),e.isDisposed()))return;const n=[];for(let g of a){const d=this._libFiles.getOrCreateModel(g.fileName);d&&n.push({uri:d.uri,range:this._textSpanToRange(d,g.textSpan)})}return n})}},X=class extends S{provideDocumentSymbols(e,t){return m(this,null,function*(){const o=e.uri,r=yield this._worker(o);if(e.isDisposed())return;const c=yield r.getNavigationTree(o.toString());if(!c||e.isDisposed())return;const u=(a,n)=>{var d;return{name:a.text,detail:"",kind:y[a.kind]||i.languages.SymbolKind.Variable,range:this._textSpanToRange(e,a.spans[0]),selectionRange:this._textSpanToRange(e,a.spans[0]),tags:[],children:(d=a.childItems)==null?void 0:d.map(w=>u(w,a.text)),containerName:n}};return c.childItems?c.childItems.map(a=>u(a)):[]})}},f=(p=class{},p.unknown="",p.keyword="keyword",p.script="script",p.module="module",p.class="class",p.interface="interface",p.type="type",p.enum="enum",p.variable="var",p.localVariable="local var",p.function="function",p.localFunction="local function",p.memberFunction="method",p.memberGetAccessor="getter",p.memberSetAccessor="setter",p.memberVariable="property",p.constructorImplementation="constructor",p.callSignature="call",p.indexSignature="index",p.constructSignature="construct",p.parameter="parameter",p.typeParameter="type parameter",p.primitiveType="primitive type",p.label="label",p.alias="alias",p.const="const",p.let="let",p.warning="warning",p),y=Object.create(null);y[f.module]=i.languages.SymbolKind.Module,y[f.class]=i.languages.SymbolKind.Class,y[f.enum]=i.languages.SymbolKind.Enum,y[f.interface]=i.languages.SymbolKind.Interface,y[f.memberFunction]=i.languages.SymbolKind.Method,y[f.memberVariable]=i.languages.SymbolKind.Property,y[f.memberGetAccessor]=i.languages.SymbolKind.Property,y[f.memberSetAccessor]=i.languages.SymbolKind.Property,y[f.variable]=i.languages.SymbolKind.Variable,y[f.const]=i.languages.SymbolKind.Variable,y[f.localVariable]=i.languages.SymbolKind.Variable,y[f.variable]=i.languages.SymbolKind.Variable,y[f.function]=i.languages.SymbolKind.Function,y[f.localFunction]=i.languages.SymbolKind.Function;var A=class extends S{static _convertOptions(e){return{ConvertTabsToSpaces:e.insertSpaces,TabSize:e.tabSize,IndentSize:e.tabSize,IndentStyle:2,NewLineCharacter:`
`,InsertSpaceAfterCommaDelimiter:!0,InsertSpaceAfterSemicolonInForStatements:!0,InsertSpaceBeforeAndAfterBinaryOperators:!0,InsertSpaceAfterKeywordsInControlFlowStatements:!0,InsertSpaceAfterFunctionKeywordForAnonymousFunctions:!0,InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis:!1,InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets:!1,InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces:!1,PlaceOpenBraceOnNewLineForControlBlocks:!1,PlaceOpenBraceOnNewLineForFunctions:!1}}_convertTextChanges(e,t){return{text:t.newText,range:this._textSpanToRange(e,t.span)}}},Y=class extends A{constructor(){super(...arguments),this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(e,t,o,r){return m(this,null,function*(){const c=e.uri,u=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),l=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),a=yield this._worker(c);if(e.isDisposed())return;const n=yield a.getFormattingEditsForRange(c.toString(),u,l,A._convertOptions(o));if(!(!n||e.isDisposed()))return n.map(g=>this._convertTextChanges(e,g))})}},Z=class extends A{get autoFormatTriggerCharacters(){return[";","}",`
`]}provideOnTypeFormattingEdits(e,t,o,r,c){return m(this,null,function*(){const u=e.uri,l=e.getOffsetAt(t),a=yield this._worker(u);if(e.isDisposed())return;const n=yield a.getFormattingEditsAfterKeystroke(u.toString(),l,o,A._convertOptions(r));if(!(!n||e.isDisposed()))return n.map(g=>this._convertTextChanges(e,g))})}},q=class extends A{provideCodeActions(e,t,o,r){return m(this,null,function*(){const c=e.uri,u=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),l=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),a=A._convertOptions(e.getOptions()),n=o.markers.filter(h=>h.code).map(h=>h.code).map(Number),g=yield this._worker(c);if(e.isDisposed())return;const d=yield g.getCodeFixesAtPosition(c.toString(),u,l,n,a);return!d||e.isDisposed()?{actions:[],dispose:()=>{}}:{actions:d.filter(h=>h.changes.filter(D=>D.isNewFile).length===0).map(h=>this._tsCodeFixActionToMonacoCodeAction(e,o,h)),dispose:()=>{}}})}_tsCodeFixActionToMonacoCodeAction(e,t,o){const r=[];for(const u of o.changes)for(const l of u.textChanges)r.push({resource:e.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(e,l.span),text:l.newText}});return{title:o.description,edit:{edits:r},diagnostics:t.markers,kind:"quickfix"}}},ee=class extends S{constructor(e,t){super(t),this._libFiles=e}provideRenameEdits(e,t,o,r){return m(this,null,function*(){const c=e.uri,u=c.toString(),l=e.getOffsetAt(t),a=yield this._worker(c);if(e.isDisposed())return;const n=yield a.getRenameInfo(u,l,{allowRenameOfImportPath:!1});if(n.canRename===!1)return{edits:[],rejectReason:n.localizedErrorMessage};if(n.fileToRename!==void 0)throw new Error("Renaming files is not supported.");const g=yield a.findRenameLocations(u,l,!1,!1,!1);if(!g||e.isDisposed())return;const d=[];for(const w of g){const h=this._libFiles.getOrCreateModel(w.fileName);if(h)d.push({resource:h.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(h,w.textSpan),text:o}});else throw new Error(`Unknown file ${w.fileName}.`)}return{edits:d}})}},te=class extends S{provideInlayHints(e,t,o){return m(this,null,function*(){const r=e.uri,c=r.toString(),u=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),l=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),a=yield this._worker(r);return e.isDisposed()?null:{hints:(yield a.provideInlayHints(c,u,l)).map(d=>oe(ae({},d),{label:d.text,position:e.getPositionAt(d.position),kind:this._convertHintKind(d.kind)})),dispose:()=>{}}})}_convertHintKind(e){switch(e){case"Parameter":return i.languages.InlayHintKind.Parameter;case"Type":return i.languages.InlayHintKind.Type;default:return i.languages.InlayHintKind.Type}}},E,H;function le(e){H=se(e,"typescript")}function ge(e){E=se(e,"javascript")}function de(){return new Promise((e,t)=>{if(!E)return t("JavaScript not registered!");e(E)})}function pe(){return new Promise((e,t)=>{if(!H)return t("TypeScript not registered!");e(H)})}function se(e,t){const o=[],r=[],c=new W(t,e);o.push(c);const u=(...n)=>c.getLanguageServiceWorker(...n),l=new j(u);function a(){const{modeConfiguration:n}=e;re(r),n.completionItems&&r.push(i.languages.registerCompletionItemProvider(t,new B(u))),n.signatureHelp&&r.push(i.languages.registerSignatureHelpProvider(t,new $(u))),n.hovers&&r.push(i.languages.registerHoverProvider(t,new z(u))),n.documentHighlights&&r.push(i.languages.registerDocumentHighlightProvider(t,new J(u))),n.definitions&&r.push(i.languages.registerDefinitionProvider(t,new G(l,u))),n.references&&r.push(i.languages.registerReferenceProvider(t,new Q(l,u))),n.documentSymbols&&r.push(i.languages.registerDocumentSymbolProvider(t,new X(u))),n.rename&&r.push(i.languages.registerRenameProvider(t,new ee(l,u))),n.documentRangeFormattingEdits&&r.push(i.languages.registerDocumentRangeFormattingEditProvider(t,new Y(u))),n.onTypeFormattingEdits&&r.push(i.languages.registerOnTypeFormattingEditProvider(t,new Z(u))),n.codeActions&&r.push(i.languages.registerCodeActionProvider(t,new q(u))),n.inlayHints&&r.push(i.languages.registerInlayHintsProvider(t,new te(u))),n.diagnostics&&r.push(new V(l,e,t,u))}return a(),o.push(fe(r)),u}function fe(e){return{dispose:()=>re(e)}}function re(e){for(;e.length;)e.pop().dispose()}}}]);
}());