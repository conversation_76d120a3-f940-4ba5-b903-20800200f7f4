"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5744],{47046:function(H,E){var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"};E.Z=e},49495:function(H,E){var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};E.Z=e},39055:function(H,E){var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};E.Z=e},85175:function(H,E,e){var o=e(1413),i=e(67294),M=e(48820),j=e(91146),x=function(C,O){return i.createElement(j.Z,(0,o.Z)((0,o.Z)({},C),{},{ref:O,icon:M.Z}))},h=i.forwardRef(x);E.Z=h},82061:function(H,E,e){var o=e(1413),i=e(67294),M=e(47046),j=e(91146),x=function(C,O){return i.createElement(j.Z,(0,o.Z)((0,o.Z)({},C),{},{ref:O,icon:M.Z}))},h=i.forwardRef(x);E.Z=h},50336:function(H,E,e){e.d(E,{Z:function(){return C}});var o=e(1413),i=e(67294),M={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M476 399.1c0-3.9-3.1-7.1-7-7.1h-42c-3.8 0-7 3.2-7 7.1V484h-84.5c-4.1 0-7.5 3.1-7.5 7v42c0 3.8 3.4 7 7.5 7H420v84.9c0 3.9 3.2 7.1 7 7.1h42c3.9 0 7-3.2 7-7.1V540h84.5c4.1 0 7.5-3.2 7.5-7v-42c0-3.9-3.4-7-7.5-7H476v-84.9zM560.5 704h-225c-4.1 0-7.5 3.2-7.5 7v42c0 3.8 3.4 7 7.5 7h225c4.1 0 7.5-3.2 7.5-7v-42c0-3.8-3.4-7-7.5-7zm-7.1-502.6c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v704c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V397.3c0-8.5-3.4-16.6-9.4-22.6L553.4 201.4zM664 888H232V264h282.2L664 413.8V888zm190.2-581.4L611.3 72.9c-6-5.7-13.9-8.9-22.2-8.9H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h277l219 210.6V824c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V329.6c0-8.7-3.5-17-9.8-23z"}}]},name:"diff",theme:"outlined"},j=M,x=e(91146),h=function(q,B){return i.createElement(x.Z,(0,o.Z)((0,o.Z)({},q),{},{ref:B,icon:j}))},N=i.forwardRef(h),C=N},34804:function(H,E,e){var o=e(1413),i=e(67294),M=e(66023),j=e(91146),x=function(C,O){return i.createElement(j.Z,(0,o.Z)((0,o.Z)({},C),{},{ref:O,icon:M.Z}))},h=i.forwardRef(x);E.Z=h},69753:function(H,E,e){var o=e(1413),i=e(67294),M=e(49495),j=e(91146),x=function(C,O){return i.createElement(j.Z,(0,o.Z)((0,o.Z)({},C),{},{ref:O,icon:M.Z}))},h=i.forwardRef(x);E.Z=h},51042:function(H,E,e){var o=e(1413),i=e(67294),M=e(42110),j=e(91146),x=function(C,O){return i.createElement(j.Z,(0,o.Z)((0,o.Z)({},C),{},{ref:O,icon:M.Z}))},h=i.forwardRef(x);E.Z=h},40110:function(H,E,e){var o=e(1413),i=e(67294),M=e(509),j=e(91146),x=function(C,O){return i.createElement(j.Z,(0,o.Z)((0,o.Z)({},C),{},{ref:O,icon:M.Z}))},h=i.forwardRef(x);E.Z=h},41441:function(H,E,e){var o=e(1413),i=e(67294),M=e(39055),j=e(91146),x=function(C,O){return i.createElement(j.Z,(0,o.Z)((0,o.Z)({},C),{},{ref:O,icon:M.Z}))},h=i.forwardRef(x);E.Z=h},7569:function(H,E,e){e.r(E),e.d(E,{default:function(){return ae}});var o=e(15009),i=e.n(o),M=e(19632),j=e.n(M),x=e(99289),h=e.n(x),N=e(5574),C=e.n(N),O=e(7528),q=e(40110),B=e(82061),ne=e(51042),J=e(84017),F=e(11941),R=e(47019),u=e(2453),A=e(17788),l=e(55102),s=e(83622),S=e(78957),v=e(96074),b=e(2487),$=e(67294),z=e(20057),U=e(85425),Z=e(57945),re=e(44394),n=e(85893),X=F.Z.TabPane,ae=function(){var T=(0,z.TH)(),t=(0,z.s0)(),m=(0,$.useState)(void 0),P=C()(m,2),r=P[0],a=P[1],g=(0,$.useState)("tab1"),I=C()(g,2),L=I[0],K=I[1],Q=(0,$.useState)([]),G=C()(Q,2),ee=G[0],_=G[1],le=(0,$.useState)([]),ie=C()(le,2),w=ie[0],te=ie[1];(0,$.useEffect)(function(){var D=function(){var f=h()(i()().mark(function d(){var p,c,y;return i()().wrap(function(V){for(;;)switch(V.prev=V.next){case 0:if(!(T.state&&T.state.proId)){V.next=4;break}a(T.state.proId),V.next=14;break;case 4:return V.prev=4,V.next=7,(0,O.hW)();case 7:p=V.sent,p&&p.length>0&&(c=j()(p).sort(function(Oe,De){var $e=new Date(Oe.last_activity_at||0),Se=new Date(De.last_activity_at||0);return Se.getTime()-$e.getTime()}),y=c[0],a(y.id)),V.next=14;break;case 11:V.prev=11,V.t0=V.catch(4),console.error("Failed to fetch project information:",V.t0);case 14:case"end":return V.stop()}},d,null,[[4,11]])}));return function(){return f.apply(this,arguments)}}();D()},[T.state]);var ue=(0,$.useState)(!1),Be=C()(ue,2),Ie=Be[0],de=Be[1],he=(0,$.useState)(""),xe=C()(he,2),Y=xe[0],Ke=xe[1],Ve=(0,$.useState)(!1),Ze=C()(Ve,2),He=Ze[0],fe=Ze[1],Ge=(0,$.useState)(!1),Te=C()(Ge,2),Fe=Te[0],ve=Te[1],Re=R.Z.useForm(),ce=C()(Re,1),Ae=ce[0];(0,$.useEffect)(function(){var D=function(){var f=h()(i()().mark(function d(){var p,c;return i()().wrap(function(W){for(;;)switch(W.prev=W.next){case 0:if(r){W.next=2;break}return W.abrupt("return");case 2:return p={id:r},W.prev=3,W.next=6,(0,O.$y)(p);case 6:c=W.sent,_(c),te(c),W.next=14;break;case 11:W.prev=11,W.t0=W.catch(3),console.error("Failed to fetch branch:",W.t0);case 14:case"end":return W.stop()}},d,null,[[3,11]])}));return function(){return f.apply(this,arguments)}}();D()},[r]);var oe=(0,$.useState)(!1),me=C()(oe,2),ge=me[0],Je=me[1],ke=function(){var D=h()(i()().mark(function f(d){var p;return i()().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:if(r){y.next=3;break}return u.ZP.error("\u9879\u76EEID\u4E0D\u5B58\u5728"),y.abrupt("return");case 3:if(d.name){y.next=6;break}return u.ZP.error("\u8BF7\u8F93\u5165\u5206\u652F\u540D\u79F0"),y.abrupt("return");case 6:if(d.ref){y.next=9;break}return u.ZP.error("\u8BF7\u8F93\u5165\u521B\u5EFA\u6765\u6E90"),y.abrupt("return");case 9:return y.prev=9,Je(!0),y.next=13,(0,O.Qj)({id:r,name:d.name,ref:d.ref});case 13:return u.ZP.success("\u5206\u652F\u521B\u5EFA\u6210\u529F"),ve(!1),Ae.resetFields(),y.next=18,(0,O.$y)({id:r});case 18:p=y.sent,_(p),te(p),y.next=27;break;case 23:y.prev=23,y.t0=y.catch(9),console.error("\u521B\u5EFA\u5206\u652F\u5931\u8D25:",y.t0),u.ZP.error("\u521B\u5EFA\u5206\u652F\u5931\u8D25: ".concat((y.t0===null||y.t0===void 0?void 0:y.t0.message)||"\u672A\u77E5\u9519\u8BEF"));case 27:return y.prev=27,Je(!1),y.finish(27);case 30:case"end":return y.stop()}},f,null,[[9,23,27,30]])}));return function(d){return D.apply(this,arguments)}}(),pe=function(f){var d=w.find(function(p){return p.name===f});if(d!=null&&d.protected){u.ZP.warning("\u53D7\u4FDD\u62A4\u5206\u652F\u4E0D\u80FD\u5220\u9664");return}Ke(f),de(!0)},qe=function(){var D=h()(i()().mark(function f(){var d;return i()().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:if(r){c.next=3;break}return u.ZP.error("\u9879\u76EEID\u4E0D\u5B58\u5728"),c.abrupt("return");case 3:return c.prev=3,c.next=6,(0,O.G8)({id:r,name:Y});case 6:return u.ZP.success("\u5206\u652F\u5220\u9664\u6210\u529F"),de(!1),c.next=10,(0,O.$y)({id:r});case 10:d=c.sent,_(d),te(d),c.next=19;break;case 15:c.prev=15,c.t0=c.catch(3),u.ZP.error("\u5220\u9664\u5206\u652F\u5931\u8D25"),console.error("Failed to delete branch:",c.t0);case 19:case"end":return c.stop()}},f,null,[[3,15]])}));return function(){return D.apply(this,arguments)}}(),et=function(){var D=h()(i()().mark(function f(){var d;return i()().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:if(r){c.next=3;break}return u.ZP.error("\u9879\u76EEID\u4E0D\u5B58\u5728"),c.abrupt("return");case 3:return c.prev=3,c.next=6,(0,O.Zo)({id:r});case 6:return u.ZP.success("\u5DF2\u5408\u5E76\u5206\u652F\u5220\u9664\u6210\u529F"),fe(!1),c.next=10,(0,O.$y)({id:r});case 10:d=c.sent,_(d),te(d),c.next=19;break;case 15:c.prev=15,c.t0=c.catch(3),u.ZP.error("\u5220\u9664\u5DF2\u5408\u5E76\u5206\u652F\u5931\u8D25"),console.error("Failed to delete merged branches:",c.t0);case 19:case"end":return c.stop()}},f,null,[[3,15]])}));return function(){return D.apply(this,arguments)}}(),tt=function(f){if(f){var d=ee.filter(function(p){return p.name.toLowerCase().includes(f.toLowerCase())});te(d)}else te(ee)},Ce=function(f){navigator.clipboard.writeText(f).then(function(){u.ZP.success("\u5DF2\u590D\u5236\u5206\u652F\u540D\u79F0: ".concat(f))}).catch(function(d){console.error("Failed to copy branch name:",d),u.ZP.error("\u590D\u5236\u5931\u8D25")})},be=function(){var D=h()(i()().mark(function f(d){var p,c,y,W,V,Oe,De,$e,Se,Pe,je,Me,We,Le,Ne,Ue,Ye,se,Xe;return i()().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:if(Se=w.find(function(_e){return _e.name===d}),Se){k.next=4;break}return u.ZP.error("\u627E\u4E0D\u5230\u5206\u652F: ".concat(d)),k.abrupt("return");case 4:if(Pe=(p=T.state)===null||p===void 0?void 0:p.proName,je=(c=T.state)===null||c===void 0?void 0:c.ownerName,Me=(y=T.state)===null||y===void 0?void 0:y.visibility,We=(W=T.state)===null||W===void 0?void 0:W.starCount,Le=(V=T.state)===null||V===void 0?void 0:V.forkCount,Ne=(Oe=T.state)===null||Oe===void 0?void 0:Oe.sshUrl,Ue=(De=T.state)===null||De===void 0?void 0:De.httpUrl,!(!Pe||!je||!Me)){k.next=23;break}return k.prev=12,k.next=15,(0,O.hW)();case 15:Ye=k.sent,se=Ye.find(function(_e){return _e.id===r}),se&&(Pe=Pe||se.name,je=je||((Xe=se.owner)===null||Xe===void 0?void 0:Xe.name),Me=Me||se.visibility,We=We||se.star_count,Le=Le||se.forks_count,Ne=Ne||se.ssh_url_to_repo,Ue=Ue||se.http_url_to_repo),k.next=23;break;case 20:k.prev=20,k.t0=k.catch(12),console.error("Failed to fetch project details:",k.t0);case 23:console.log("\u5C06\u8DF3\u8F6C\u5230files\u9875\u9762\uFF0C\u5E76\u4F7F\u7528\u5206\u652F: ".concat(d)),t("/tools/repo/files",{state:{proId:r,defaultBranch:d,proName:Pe||"\u9879\u76EE",ownerName:je||"\u7528\u6237",update:($e=Se.commit)===null||$e===void 0?void 0:$e.created_at,visibility:Me||"private",starCount:We||0,forkCount:Le||0,sshUrl:Ne,httpUrl:Ue},replace:!0});case 25:case"end":return k.stop()}},f,null,[[12,20]])}));return function(d){return D.apply(this,arguments)}}(),ye=function(f){var d,p,c=w.find(function(W){return W.default===!0}),y=c?c.name:"main";t("/tools/repo/compare/compare_result",{state:{sourceProjectId:r,targetProjectId:r,sourceProject:((d=T.state)===null||d===void 0?void 0:d.proName)||"\u9879\u76EE",targetProject:((p=T.state)===null||p===void 0?void 0:p.proName)||"\u9879\u76EE",sourceBranch:f,targetBranch:y}})},Ee=function(f){var d,p=w.find(function(y){return y.default===!0}),c=p?p.name:"main";t("/tools/repo/newMergeRequest",{state:{proId:r,proName:((d=T.state)===null||d===void 0?void 0:d.proName)||"\u9879\u76EE",sourceBranch:f,targetBranch:c,check:"mergeRequests"}})},Qe=(0,$.useMemo)(function(){var D=w.filter(function(d){var p;return(0,re.E_)((p=d.commit)===null||p===void 0?void 0:p.created_at)}),f=w.filter(function(d){var p;return!(0,re.E_)((p=d.commit)===null||p===void 0?void 0:p.created_at)});return{activeBranches:D.map(Z.y_),inactiveBranches:f.map(Z.y_)}},[w]),we=Qe.activeBranches,ze=Qe.inactiveBranches,nt=(0,$.useMemo)(function(){return we.slice(0,5)},[we]),rt=(0,$.useMemo)(function(){return ze.slice(0,5)},[ze]);return(0,n.jsxs)(J._z,{header:{title:"",breadcrumb:{}},children:[(0,n.jsx)(U.Z,{customItems:[{path:"/tools",breadcrumbName:"\u5DE5\u5177\u5957\u4EF6"},{path:"/tools/repo",breadcrumbName:"\u4EE3\u7801"},{path:"/tools/repo/branch",breadcrumbName:"\u5206\u652F\u7BA1\u7406"}]}),(0,n.jsxs)(A.Z,{title:"\u5220\u9664\u5206\u652F",open:Ie,onOk:qe,onCancel:function(){return de(!1)},okText:"\u786E\u8BA4\u5220\u9664",cancelText:"\u53D6\u6D88",children:[(0,n.jsxs)("p",{children:['\u786E\u5B9A\u8981\u5220\u9664\u5206\u652F "',Y,'" \u5417\uFF1F']}),(0,n.jsx)("p",{children:"\u6B64\u64CD\u4F5C\u4E0D\u53EF\u6062\u590D\u3002"})]}),(0,n.jsxs)(A.Z,{title:"\u5220\u9664\u5DF2\u5408\u5E76\u5206\u652F",open:He,onOk:et,onCancel:function(){return fe(!1)},okText:"\u786E\u8BA4\u5220\u9664",cancelText:"\u53D6\u6D88",children:[(0,n.jsx)("p",{children:"\u786E\u5B9A\u8981\u5220\u9664\u6240\u6709\u5DF2\u5408\u5E76\u5206\u652F\u5417\uFF1F"}),(0,n.jsx)("p",{children:"\u6B64\u64CD\u4F5C\u5C06\u5220\u9664\u6240\u6709\u5DF2\u5408\u5E76\u5230\u9ED8\u8BA4\u5206\u652F\u7684\u5206\u652F\uFF0C\u4E14\u4E0D\u53EF\u6062\u590D\u3002"})]}),(0,n.jsx)(A.Z,{title:"\u65B0\u5EFA\u5206\u652F",open:Fe,onCancel:function(){return ve(!1)},footer:null,children:(0,n.jsxs)(R.Z,{form:Ae,layout:"vertical",onFinish:ke,children:[(0,n.jsx)(R.Z.Item,{name:"name",label:"\u5206\u652F\u540D\u79F0",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u5206\u652F\u540D\u79F0"}],children:(0,n.jsx)(l.Z,{placeholder:"\u8F93\u5165\u5206\u652F\u540D\u79F0"})}),(0,n.jsx)(R.Z.Item,{name:"ref",label:"\u521B\u5EFA\u81EA",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u521B\u5EFA\u6765\u6E90"}],children:(0,n.jsx)(l.Z,{placeholder:"\u8F93\u5165\u521B\u5EFA\u6765\u6E90\uFF08\u5206\u652F\u540D\u6216\u63D0\u4EA4SHA\uFF09"})}),(0,n.jsxs)(R.Z.Item,{children:[(0,n.jsx)(s.ZP,{type:"primary",htmlType:"submit",style:{marginRight:8},loading:ge,children:"\u521B\u5EFA"}),(0,n.jsx)(s.ZP,{onClick:function(){return ve(!1)},children:"\u53D6\u6D88"})]})]})}),(0,n.jsxs)("div",{style:{padding:"0",borderRadius:"4px"},children:[(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"0 16px"},children:[(0,n.jsx)("div",{style:{display:"flex",flex:1},children:(0,n.jsxs)(F.Z,{activeKey:L,onChange:function(f){return K(f)},style:{width:"100%"},children:[(0,n.jsx)(X,{tab:"\u6982\u89C8"},"tab1"),(0,n.jsx)(X,{tab:"\u6D3B\u8DC3"},"tab2"),(0,n.jsx)(X,{tab:"\u975E\u6D3B\u8DC3"},"tab3"),(0,n.jsx)(X,{tab:"\u5168\u90E8"},"tab4")]})}),(0,n.jsx)("div",{children:(0,n.jsxs)(S.Z,{size:12,children:[(0,n.jsx)(l.Z,{placeholder:"\u641C\u7D22\u5206\u652F",prefix:(0,n.jsx)(q.Z,{}),style:{width:200},onChange:function(f){return tt(f.target.value)},allowClear:!0}),(0,n.jsx)(s.ZP,{icon:(0,n.jsx)(B.Z,{}),danger:!0,onClick:function(){return fe(!0)},children:"\u5220\u9664\u5DF2\u5408\u5E76\u5206\u652F"}),(0,n.jsx)(s.ZP,{type:"primary",icon:(0,n.jsx)(ne.Z,{}),style:{background:"#007bff"},onClick:function(){return ve(!0)},children:"\u65B0\u5EFA\u5206\u652F"})]})})]}),(0,n.jsxs)("div",{style:{padding:"0 16px"},children:[L==="tab1"&&(0,n.jsxs)(n.Fragment,{children:[we.length>0&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("h3",{children:"\u6D3B\u8DC3\u5206\u652F"}),(0,n.jsx)(v.Z,{style:{margin:0}}),(0,n.jsx)(b.Z,{size:"large",rowKey:"id",dataSource:nt,renderItem:function(f){return(0,n.jsx)(Z.$E,{branches:[f],onCopy:Ce,onDelete:pe,onNavigate:be,onCompare:ye,onMergeRequest:Ee})}}),(0,n.jsx)(v.Z,{style:{margin:0}}),(0,n.jsx)("div",{style:{marginTop:"12px"},children:(0,n.jsx)("a",{onClick:function(){return K("tab2")},style:{cursor:"pointer"},children:"\u67E5\u770B\u66F4\u591A\u6D3B\u8DC3\u5206\u652F"})}),(0,n.jsx)(v.Z,{})]}),ze.length>0&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("h3",{children:"\u975E\u6D3B\u8DC3\u5206\u652F"}),(0,n.jsx)(v.Z,{style:{margin:0}}),(0,n.jsx)(b.Z,{size:"large",rowKey:"name",dataSource:rt,renderItem:function(f){return(0,n.jsx)(Z.$E,{branches:[f],onCopy:Ce,onDelete:pe,onNavigate:be,onCompare:ye,onMergeRequest:Ee})}}),(0,n.jsx)(v.Z,{style:{margin:0}}),(0,n.jsx)("div",{style:{margin:"12px 0"},children:(0,n.jsx)("a",{onClick:function(){return K("tab3")},style:{cursor:"pointer"},children:"\u67E5\u770B\u66F4\u591A\u975E\u6D3B\u8DC3\u5206\u652F"})})]})]}),L==="tab2"&&(0,n.jsx)(b.Z,{size:"large",rowKey:"name",dataSource:we,renderItem:function(f){return(0,n.jsx)(Z.$E,{branches:[f],onCopy:Ce,onDelete:pe,onNavigate:be,onCompare:ye,onMergeRequest:Ee})}}),L==="tab3"&&(0,n.jsx)(b.Z,{size:"large",rowKey:"name",dataSource:ze,renderItem:function(f){return(0,n.jsx)(Z.$E,{branches:[f],onCopy:Ce,onDelete:pe,onNavigate:be,onCompare:ye,onMergeRequest:Ee})}}),L==="tab4"&&(0,n.jsx)(b.Z,{size:"large",rowKey:"name",dataSource:w.map(Z.y_),renderItem:function(f){return(0,n.jsx)(Z.$E,{branches:[f],onCopy:Ce,onDelete:pe,onNavigate:be,onCompare:ye,onMergeRequest:Ee})}})]})]})]})}},96074:function(H,E,e){e.d(E,{Z:function(){return A}});var o=e(67294),i=e(93967),M=e.n(i),j=e(53124),x=e(98675),h=e(11568),N=e(14747),C=e(83559),O=e(83262);const q=l=>{const{componentCls:s}=l;return{[s]:{"&-horizontal":{[`&${s}`]:{"&-sm":{marginBlock:l.marginXS},"&-md":{marginBlock:l.margin}}}}}},B=l=>{const{componentCls:s,sizePaddingEdgeHorizontal:S,colorSplit:v,lineWidth:b,textPaddingInline:$,orientationMargin:z,verticalMarginInline:U}=l;return{[s]:Object.assign(Object.assign({},(0,N.Wf)(l)),{borderBlockStart:`${(0,h.bf)(b)} solid ${v}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:U,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,h.bf)(b)} solid ${v}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,h.bf)(l.marginLG)} 0`},[`&-horizontal${s}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,h.bf)(l.dividerHorizontalWithTextGutterMargin)} 0`,color:l.colorTextHeading,fontWeight:500,fontSize:l.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${v}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,h.bf)(b)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${s}-with-text-start`]:{"&::before":{width:`calc(${z} * 100%)`},"&::after":{width:`calc(100% - ${z} * 100%)`}},[`&-horizontal${s}-with-text-end`]:{"&::before":{width:`calc(100% - ${z} * 100%)`},"&::after":{width:`calc(${z} * 100%)`}},[`${s}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:$},"&-dashed":{background:"none",borderColor:v,borderStyle:"dashed",borderWidth:`${(0,h.bf)(b)} 0 0`},[`&-horizontal${s}-with-text${s}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${s}-dashed`]:{borderInlineStartWidth:b,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:v,borderStyle:"dotted",borderWidth:`${(0,h.bf)(b)} 0 0`},[`&-horizontal${s}-with-text${s}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${s}-dotted`]:{borderInlineStartWidth:b,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${s}-with-text`]:{color:l.colorText,fontWeight:"normal",fontSize:l.fontSize},[`&-horizontal${s}-with-text-start${s}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${s}-inner-text`]:{paddingInlineStart:S}},[`&-horizontal${s}-with-text-end${s}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${s}-inner-text`]:{paddingInlineEnd:S}}})}},ne=l=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:l.marginXS});var J=(0,C.I$)("Divider",l=>{const s=(0,O.IX)(l,{dividerHorizontalWithTextGutterMargin:l.margin,sizePaddingEdgeHorizontal:0});return[B(s),q(s)]},ne,{unitless:{orientationMargin:!0}}),F=function(l,s){var S={};for(var v in l)Object.prototype.hasOwnProperty.call(l,v)&&s.indexOf(v)<0&&(S[v]=l[v]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function")for(var b=0,v=Object.getOwnPropertySymbols(l);b<v.length;b++)s.indexOf(v[b])<0&&Object.prototype.propertyIsEnumerable.call(l,v[b])&&(S[v[b]]=l[v[b]]);return S};const R={small:"sm",middle:"md"};var A=l=>{const{getPrefixCls:s,direction:S,className:v,style:b}=(0,j.dj)("divider"),{prefixCls:$,type:z="horizontal",orientation:U="center",orientationMargin:Z,className:re,rootClassName:n,children:X,dashed:ae,variant:T="solid",plain:t,style:m,size:P}=l,r=F(l,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),a=s("divider",$),[g,I,L]=J(a),K=(0,x.Z)(P),Q=R[K],G=!!X,ee=o.useMemo(()=>U==="left"?S==="rtl"?"end":"start":U==="right"?S==="rtl"?"start":"end":U,[S,U]),_=ee==="start"&&Z!=null,le=ee==="end"&&Z!=null,ie=M()(a,v,I,L,`${a}-${z}`,{[`${a}-with-text`]:G,[`${a}-with-text-${ee}`]:G,[`${a}-dashed`]:!!ae,[`${a}-${T}`]:T!=="solid",[`${a}-plain`]:!!t,[`${a}-rtl`]:S==="rtl",[`${a}-no-default-orientation-margin-start`]:_,[`${a}-no-default-orientation-margin-end`]:le,[`${a}-${Q}`]:!!Q},re,n),w=o.useMemo(()=>typeof Z=="number"?Z:/^\d+$/.test(Z)?Number(Z):Z,[Z]),te={marginInlineStart:_?w:void 0,marginInlineEnd:le?w:void 0};return g(o.createElement("div",Object.assign({className:ie,style:Object.assign(Object.assign({},b),m)},r,{role:"separator"}),X&&z!=="vertical"&&o.createElement("span",{className:`${a}-inner-text`,style:te},X)))}},99134:function(H,E,e){var o=e(67294);const i=(0,o.createContext)({});E.Z=i},21584:function(H,E,e){var o=e(67294),i=e(93967),M=e.n(i),j=e(53124),x=e(99134),h=e(6999),N=function(B,ne){var J={};for(var F in B)Object.prototype.hasOwnProperty.call(B,F)&&ne.indexOf(F)<0&&(J[F]=B[F]);if(B!=null&&typeof Object.getOwnPropertySymbols=="function")for(var R=0,F=Object.getOwnPropertySymbols(B);R<F.length;R++)ne.indexOf(F[R])<0&&Object.prototype.propertyIsEnumerable.call(B,F[R])&&(J[F[R]]=B[F[R]]);return J};function C(B){return typeof B=="number"?`${B} ${B} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(B)?`0 0 ${B}`:B}const O=["xs","sm","md","lg","xl","xxl"],q=o.forwardRef((B,ne)=>{const{getPrefixCls:J,direction:F}=o.useContext(j.E_),{gutter:R,wrap:u}=o.useContext(x.Z),{prefixCls:A,span:l,order:s,offset:S,push:v,pull:b,className:$,children:z,flex:U,style:Z}=B,re=N(B,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),n=J("col",A),[X,ae,T]=(0,h.cG)(n),t={};let m={};O.forEach(a=>{let g={};const I=B[a];typeof I=="number"?g.span=I:typeof I=="object"&&(g=I||{}),delete re[a],m=Object.assign(Object.assign({},m),{[`${n}-${a}-${g.span}`]:g.span!==void 0,[`${n}-${a}-order-${g.order}`]:g.order||g.order===0,[`${n}-${a}-offset-${g.offset}`]:g.offset||g.offset===0,[`${n}-${a}-push-${g.push}`]:g.push||g.push===0,[`${n}-${a}-pull-${g.pull}`]:g.pull||g.pull===0,[`${n}-rtl`]:F==="rtl"}),g.flex&&(m[`${n}-${a}-flex`]=!0,t[`--${n}-${a}-flex`]=C(g.flex))});const P=M()(n,{[`${n}-${l}`]:l!==void 0,[`${n}-order-${s}`]:s,[`${n}-offset-${S}`]:S,[`${n}-push-${v}`]:v,[`${n}-pull-${b}`]:b},$,m,ae,T),r={};if(R&&R[0]>0){const a=R[0]/2;r.paddingLeft=a,r.paddingRight=a}return U&&(r.flex=C(U),u===!1&&!r.minWidth&&(r.minWidth=0)),X(o.createElement("div",Object.assign({},re,{style:Object.assign(Object.assign(Object.assign({},r),Z),t),className:P,ref:ne}),z))});E.Z=q},17621:function(H,E,e){e.d(E,{Z:function(){return R}});var o=e(67294),i=e(93967),M=e.n(i),j=e(74443),x=e(53124),h=e(25378);function N(u,A){const l=[void 0,void 0],s=Array.isArray(u)?u:[u,void 0],S=A||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return s.forEach((v,b)=>{if(typeof v=="object"&&v!==null)for(let $=0;$<j.c4.length;$++){const z=j.c4[$];if(S[z]&&v[z]!==void 0){l[b]=v[z];break}}else l[b]=v}),l}var C=e(99134),O=e(6999),q=function(u,A){var l={};for(var s in u)Object.prototype.hasOwnProperty.call(u,s)&&A.indexOf(s)<0&&(l[s]=u[s]);if(u!=null&&typeof Object.getOwnPropertySymbols=="function")for(var S=0,s=Object.getOwnPropertySymbols(u);S<s.length;S++)A.indexOf(s[S])<0&&Object.prototype.propertyIsEnumerable.call(u,s[S])&&(l[s[S]]=u[s[S]]);return l};const B=null,ne=null;function J(u,A){const[l,s]=o.useState(typeof u=="string"?u:""),S=()=>{if(typeof u=="string"&&s(u),typeof u=="object")for(let v=0;v<j.c4.length;v++){const b=j.c4[v];if(!A||!A[b])continue;const $=u[b];if($!==void 0){s($);return}}};return o.useEffect(()=>{S()},[JSON.stringify(u),A]),l}var R=o.forwardRef((u,A)=>{const{prefixCls:l,justify:s,align:S,className:v,style:b,children:$,gutter:z=0,wrap:U}=u,Z=q(u,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:re,direction:n}=o.useContext(x.E_),X=(0,h.Z)(!0,null),ae=J(S,X),T=J(s,X),t=re("row",l),[m,P,r]=(0,O.VM)(t),a=N(z,X),g=M()(t,{[`${t}-no-wrap`]:U===!1,[`${t}-${T}`]:T,[`${t}-${ae}`]:ae,[`${t}-rtl`]:n==="rtl"},v,P,r),I={},L=a[0]!=null&&a[0]>0?a[0]/-2:void 0;L&&(I.marginLeft=L,I.marginRight=L);const[K,Q]=a;I.rowGap=Q;const G=o.useMemo(()=>({gutter:[K,Q],wrap:U}),[K,Q,U]);return m(o.createElement(C.Z.Provider,{value:G},o.createElement("div",Object.assign({},Z,{className:g,style:Object.assign(Object.assign({},I),b),ref:A}),$)))})},66309:function(H,E,e){e.d(E,{Z:function(){return T}});var o=e(67294),i=e(93967),M=e.n(i),j=e(98423),x=e(98787),h=e(69760),N=e(96159),C=e(45353),O=e(53124),q=e(11568),B=e(15063),ne=e(14747),J=e(83262),F=e(83559);const R=t=>{const{paddingXXS:m,lineWidth:P,tagPaddingHorizontal:r,componentCls:a,calc:g}=t,I=g(r).sub(P).equal(),L=g(m).sub(P).equal();return{[a]:Object.assign(Object.assign({},(0,ne.Wf)(t)),{display:"inline-block",height:"auto",marginInlineEnd:t.marginXS,paddingInline:I,fontSize:t.tagFontSize,lineHeight:t.tagLineHeight,whiteSpace:"nowrap",background:t.defaultBg,border:`${(0,q.bf)(t.lineWidth)} ${t.lineType} ${t.colorBorder}`,borderRadius:t.borderRadiusSM,opacity:1,transition:`all ${t.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:t.defaultColor},[`${a}-close-icon`]:{marginInlineStart:L,fontSize:t.tagIconSize,color:t.colorIcon,cursor:"pointer",transition:`all ${t.motionDurationMid}`,"&:hover":{color:t.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${t.iconCls}-close, ${t.iconCls}-close:hover`]:{color:t.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:t.colorPrimary,backgroundColor:t.colorFillSecondary},"&:active, &-checked":{color:t.colorTextLightSolid},"&-checked":{backgroundColor:t.colorPrimary,"&:hover":{backgroundColor:t.colorPrimaryHover}},"&:active":{backgroundColor:t.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${t.iconCls} + span, > span + ${t.iconCls}`]:{marginInlineStart:I}}),[`${a}-borderless`]:{borderColor:"transparent",background:t.tagBorderlessBg}}},u=t=>{const{lineWidth:m,fontSizeIcon:P,calc:r}=t,a=t.fontSizeSM;return(0,J.IX)(t,{tagFontSize:a,tagLineHeight:(0,q.bf)(r(t.lineHeightSM).mul(a).equal()),tagIconSize:r(P).sub(r(m).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:t.defaultBg})},A=t=>({defaultBg:new B.t(t.colorFillQuaternary).onBackground(t.colorBgContainer).toHexString(),defaultColor:t.colorText});var l=(0,F.I$)("Tag",t=>{const m=u(t);return R(m)},A),s=function(t,m){var P={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&m.indexOf(r)<0&&(P[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(t);a<r.length;a++)m.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(t,r[a])&&(P[r[a]]=t[r[a]]);return P},v=o.forwardRef((t,m)=>{const{prefixCls:P,style:r,className:a,checked:g,onChange:I,onClick:L}=t,K=s(t,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:Q,tag:G}=o.useContext(O.E_),ee=ue=>{I==null||I(!g),L==null||L(ue)},_=Q("tag",P),[le,ie,w]=l(_),te=M()(_,`${_}-checkable`,{[`${_}-checkable-checked`]:g},G==null?void 0:G.className,a,ie,w);return le(o.createElement("span",Object.assign({},K,{ref:m,style:Object.assign(Object.assign({},r),G==null?void 0:G.style),className:te,onClick:ee})))}),b=e(98719);const $=t=>(0,b.Z)(t,(m,{textColor:P,lightBorderColor:r,lightColor:a,darkColor:g})=>({[`${t.componentCls}${t.componentCls}-${m}`]:{color:P,background:a,borderColor:r,"&-inverse":{color:t.colorTextLightSolid,background:g,borderColor:g},[`&${t.componentCls}-borderless`]:{borderColor:"transparent"}}}));var z=(0,F.bk)(["Tag","preset"],t=>{const m=u(t);return $(m)},A);function U(t){return typeof t!="string"?t:t.charAt(0).toUpperCase()+t.slice(1)}const Z=(t,m,P)=>{const r=U(P);return{[`${t.componentCls}${t.componentCls}-${m}`]:{color:t[`color${P}`],background:t[`color${r}Bg`],borderColor:t[`color${r}Border`],[`&${t.componentCls}-borderless`]:{borderColor:"transparent"}}}};var re=(0,F.bk)(["Tag","status"],t=>{const m=u(t);return[Z(m,"success","Success"),Z(m,"processing","Info"),Z(m,"error","Error"),Z(m,"warning","Warning")]},A),n=function(t,m){var P={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&m.indexOf(r)<0&&(P[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(t);a<r.length;a++)m.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(t,r[a])&&(P[r[a]]=t[r[a]]);return P};const ae=o.forwardRef((t,m)=>{const{prefixCls:P,className:r,rootClassName:a,style:g,children:I,icon:L,color:K,onClose:Q,bordered:G=!0,visible:ee}=t,_=n(t,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:le,direction:ie,tag:w}=o.useContext(O.E_),[te,ue]=o.useState(!0),Be=(0,j.Z)(_,["closeIcon","closable"]);o.useEffect(()=>{ee!==void 0&&ue(ee)},[ee]);const Ie=(0,x.o2)(K),de=(0,x.yT)(K),he=Ie||de,xe=Object.assign(Object.assign({backgroundColor:K&&!he?K:void 0},w==null?void 0:w.style),g),Y=le("tag",P),[Ke,Ve,Ze]=l(Y),He=M()(Y,w==null?void 0:w.className,{[`${Y}-${K}`]:he,[`${Y}-has-color`]:K&&!he,[`${Y}-hidden`]:!te,[`${Y}-rtl`]:ie==="rtl",[`${Y}-borderless`]:!G},r,a,Ve,Ze),fe=ce=>{ce.stopPropagation(),Q==null||Q(ce),!ce.defaultPrevented&&ue(!1)},[,Ge]=(0,h.Z)((0,h.w)(t),(0,h.w)(w),{closable:!1,closeIconRender:ce=>{const Ae=o.createElement("span",{className:`${Y}-close-icon`,onClick:fe},ce);return(0,N.wm)(ce,Ae,oe=>({onClick:me=>{var ge;(ge=oe==null?void 0:oe.onClick)===null||ge===void 0||ge.call(oe,me),fe(me)},className:M()(oe==null?void 0:oe.className,`${Y}-close-icon`)}))}}),Te=typeof _.onClick=="function"||I&&I.type==="a",Fe=L||null,ve=Fe?o.createElement(o.Fragment,null,Fe,I&&o.createElement("span",null,I)):I,Re=o.createElement("span",Object.assign({},Be,{ref:m,className:He,style:xe}),ve,Ge,Ie&&o.createElement(z,{key:"preset",prefixCls:Y}),de&&o.createElement(re,{key:"status",prefixCls:Y}));return Ke(Te?o.createElement(C.Z,{component:"Tag"},Re):Re)});ae.CheckableTag=v;var T=ae}}]);
