import type { TooltipProps } from '../../tooltip';
declare const useTooltipProps: (tooltip: React.ReactNode | TooltipProps, editConfigText: React.ReactNode, children: React.ReactNode) => {
    [Symbol.iterator](): Iterator<React.ReactNode, any, any>;
    title: React.ReactNode;
} | {
    then<TResult1 = string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined, TResult2 = never>(onfulfilled?: ((value: string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null | undefined): Promise<TResult1 | TResult2>;
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null | undefined): Promise<(string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined) | TResult>;
    finally(onfinally?: (() => void) | null | undefined): Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined>;
    [Symbol.toStringTag]: string;
    title: React.ReactNode;
} | {
    title: React.ReactNode | import("../../_util/getRenderPropValue").RenderFunction;
    overlay?: React.ReactNode | import("../../_util/getRenderPropValue").RenderFunction;
    styles?: Partial<Record<"body" | "root", React.CSSProperties>>;
    classNames?: Partial<Record<"body" | "root", string>>;
    style?: React.CSSProperties;
    className?: string;
    rootClassName?: string;
    color?: import("../../_util/type").LiteralUnion<import("../../_util/colors").PresetColorType>;
    placement?: import("../../tooltip").TooltipPlacement;
    builtinPlacements?: typeof import("rc-tooltip/lib/placements").placements;
    openClassName?: string;
    arrowPointAtCenter?: boolean;
    arrow?: boolean | {
        arrowPointAtCenter?: boolean;
        pointAtCenter?: boolean;
    };
    autoAdjustOverflow?: boolean | import("../../tooltip").AdjustOverflow;
    getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
    children?: React.ReactNode;
    destroyTooltipOnHide?: boolean | {
        keepParent?: boolean;
    };
    destroyOnHidden?: boolean;
    open?: import("rc-tooltip/lib/Tooltip").TooltipProps["visible"];
    defaultOpen?: import("rc-tooltip/lib/Tooltip").TooltipProps["defaultVisible"];
    onOpenChange?: import("rc-tooltip/lib/Tooltip").TooltipProps["onVisibleChange"];
    afterOpenChange?: import("rc-tooltip/lib/Tooltip").TooltipProps["afterVisibleChange"];
    visible?: import("rc-tooltip/lib/Tooltip").TooltipProps["visible"];
    defaultVisible?: import("rc-tooltip/lib/Tooltip").TooltipProps["defaultVisible"];
    onVisibleChange?: import("rc-tooltip/lib/Tooltip").TooltipProps["onVisibleChange"];
    afterVisibleChange?: import("rc-tooltip/lib/Tooltip").TooltipProps["afterVisibleChange"];
    zIndex?: number | undefined;
    animation?: import("@rc-component/trigger/lib/interface").AnimationType | undefined;
    motion?: import("rc-motion").CSSMotionProps | undefined;
    prefixCls?: string | undefined;
    align?: import("@rc-component/trigger").AlignType | undefined;
    id?: string | undefined;
    transitionName?: string | undefined;
    showArrow?: (boolean | import("@rc-component/trigger").ArrowType) | undefined;
    forceRender?: boolean | undefined;
    popupVisible?: boolean | undefined;
    onPopupAlign?: ((element: HTMLElement, align: import("@rc-component/trigger").AlignType) => void) | undefined;
    fresh?: boolean | undefined;
    mouseLeaveDelay?: number | undefined;
    mouseEnterDelay?: number | undefined;
    trigger?: (import("@rc-component/trigger").ActionType | import("@rc-component/trigger").ActionType[]) | undefined;
    overlayStyle?: React.CSSProperties | undefined;
    overlayClassName?: string | undefined;
    getTooltipContainer?: ((node: HTMLElement) => HTMLElement) | undefined;
    arrowContent?: React.ReactNode;
    overlayInnerStyle?: React.CSSProperties | undefined;
};
export default useTooltipProps;
