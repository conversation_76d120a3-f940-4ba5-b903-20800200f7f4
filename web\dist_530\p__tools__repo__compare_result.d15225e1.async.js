"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2609],{85425:function(X,C,t){var O=t(5574),T=t.n(O),R=t(20057),B=t(85673),m=t(84226),d=t(31622),h=t(67294),p=t(85893),Y=function(N){var F=N.proName,W=N.currentPath,g=N.customItems,L=(0,R.TH)(),M=(0,R.s0)(),z=(0,m.useIntl)(),E=(0,h.useState)((0,d.gh)()||"dark"),k=T()(E,2),Z=k[0],c=k[1];(0,h.useEffect)(function(){c((0,d.gh)());var S=new MutationObserver(function(_){_.forEach(function(v){v.attributeName==="data-theme"&&c((0,d.gh)())})});return S.observe(document.body,{attributes:!0}),function(){S.disconnect()}},[]);var G=function(){if(g)return g;var _=L.pathname,v=[{path:"/tools",breadcrumbName:"\u5DE5\u5177\u5957\u4EF6"},{path:"/tools/repo",breadcrumbName:"\u4EE3\u7801\u4ED3\u5E93"}];if(_.startsWith("/tools/repo/newProject"))return[].concat(v,[{path:"/tools/repo/newProject",breadcrumbName:"\u65B0\u5EFA\u9879\u76EE"}]);if(_.startsWith("/tools/repo/files/newFile"))return[].concat(v,[{path:"/tools/repo/files",breadcrumbName:"\u6587\u4EF6\u7BA1\u7406"},{path:"/tools/repo/files/newFile",breadcrumbName:"\u65B0\u5EFA\u6587\u4EF6"}]);if(_.startsWith("/tools/repo/files")){var b=[].concat(v,[{path:"/tools/repo/files",breadcrumbName:"\u6587\u4EF6\u7BA1\u7406"}]);if(F&&b.push({path:"/tools/repo/files",breadcrumbName:F}),W){var $=W.split("/").filter(Boolean),A="";$.forEach(function(V,q){A+="/".concat(V),b.push({path:"/tools/repo/files?path=".concat(A),breadcrumbName:V})})}return b}else{if(_.startsWith("/tools/repo/branch"))return[].concat(v,[{path:"/tools/repo/branch",breadcrumbName:"\u5206\u652F\u7BA1\u7406"}]);if(_.startsWith("/tools/repo/commits"))return[].concat(v,[{path:"/tools/repo/commits",breadcrumbName:"\u63D0\u4EA4\u7BA1\u7406"}]);if(_.startsWith("/tools/repo/compare"))return[].concat(v,[{path:"/tools/repo/compare",breadcrumbName:"\u6BD4\u8F83\u4FEE\u8BA2\u7248\u672C"}]);if(_.startsWith("/tools/repo/newTag"))return[].concat(v,[{path:"/tools/repo/tags",breadcrumbName:"\u6807\u7B7E"},{path:"/tools/repo/newTag",breadcrumbName:"\u65B0\u5EFA\u6807\u7B7E"}]);if(_.startsWith("/tools/repo/tags"))return[].concat(v,[{path:"/tools/repo/tags",breadcrumbName:"\u6807\u7B7E"}])}return v};return(0,p.jsx)(B.Z,{separator:"/",style:{fontSize:"14px",marginBottom:"16px",color:Z==="light"?"#666":"#aaa"},items:G(),itemRender:function(_,v,b){var $=b.indexOf(_)===b.length-1;return $?(0,p.jsx)("span",{style:{color:Z==="light"?"#333":"#fff"},children:_.breadcrumbName}):(0,p.jsx)("a",{onClick:function(){return _.path?M(_.path):null},style:{color:Z==="light"?"#666":"#aaa"},children:_.breadcrumbName})}})};C.Z=Y},50204:function(X,C,t){t.r(C);var O=t(9783),T=t.n(O),R=t(97857),B=t.n(R),m=t(15009),d=t.n(m),h=t(99289),p=t.n(h),Y=t(5574),j=t.n(Y),N=t(84017),F=t(71471),W=t(34041),g=t(2453),L=t(71230),M=t(15746),z=t(78957),E=t(83622),k=t(96074),Z=t(83062),c=t(67294),G=t(20057),S=t(85175),_=t(64029),v=t(34804),b=t(7528),$=t(85425),A=t(44394),V=t(44438),q=t(31622),e=t(85893),ee=F.Z.Title,x=F.Z.Text,J=W.Z.Option;C.default=function(){var re,te,ye=(0,G.TH)(),se=(0,G.s0)(),U=ye.state||{},D=U.sourceProjectId,I=U.targetProjectId,w=U.sourceProject,K=U.targetProject,Ce=U.sourceBranch,Be=U.targetBranch,Me=(0,c.useState)((0,A.gz)()),oe=j()(Me,2),f=oe[0],le=oe[1];(0,c.useEffect)(function(){le((0,q.gh)());var r=new MutationObserver(function(a){a.forEach(function(s){s.attributeName==="data-theme"&&le((0,q.gh)())})});return r.observe(document.body,{attributes:!0}),function(){r.disconnect()}},[]);var Oe=(0,c.useState)("side-by-side"),ie=j()(Oe,2),rr=ie[0],tr=ie[1],Te=(0,c.useState)([]),ue=j()(Te,2),Ae=ue[0],Re=ue[1],We=(0,c.useState)([]),de=j()(We,2),Ie=de[0],Se=de[1],we=(0,c.useState)(Ce||""),ce=j()(we,2),P=ce[0],_e=ce[1],Le=(0,c.useState)(Be||""),he=j()(Le,2),y=he[0],pe=he[1],Ze=(0,c.useState)(!1),fe=j()(Ze,2),Ue=fe[0],me=fe[1],Ke=(0,c.useState)(!1),ve=j()(Ke,2),Ne=ve[0],ge=ve[1],Fe=(0,c.useState)(null),Ee=j()(Fe,2),l=Ee[0],je=Ee[1],ze=(0,c.useState)(!1),be=j()(ze,2),nr=be[0],Q=be[1];(0,c.useEffect)(function(){if(D){var r=function(){var a=p()(d()().mark(function s(){var o;return d()().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return me(!0),n.prev=1,n.next=4,(0,b.$y)({id:D});case 4:o=n.sent,Array.isArray(o)&&o.length>0&&Re(o),n.next=12;break;case 8:n.prev=8,n.t0=n.catch(1),console.error("\u83B7\u53D6\u6E90\u9879\u76EE\u5206\u652F\u5217\u8868\u5931\u8D25:",n.t0),g.ZP.error("\u83B7\u53D6\u6E90\u9879\u76EE\u5206\u652F\u5217\u8868\u5931\u8D25");case 12:return n.prev=12,me(!1),n.finish(12);case 15:case"end":return n.stop()}},s,null,[[1,8,12,15]])}));return function(){return a.apply(this,arguments)}}();r()}},[D]),(0,c.useEffect)(function(){if(I){var r=function(){var a=p()(d()().mark(function s(){var o;return d()().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return ge(!0),n.prev=1,n.next=4,(0,b.$y)({id:I});case 4:o=n.sent,Array.isArray(o)&&o.length>0&&Se(o),n.next=12;break;case 8:n.prev=8,n.t0=n.catch(1),console.error("\u83B7\u53D6\u76EE\u6807\u9879\u76EE\u5206\u652F\u5217\u8868\u5931\u8D25:",n.t0),g.ZP.error("\u83B7\u53D6\u76EE\u6807\u9879\u76EE\u5206\u652F\u5217\u8868\u5931\u8D25");case 12:return n.prev=12,ge(!1),n.finish(12);case 15:case"end":return n.stop()}},s,null,[[1,8,12,15]])}));return function(){return a.apply(this,arguments)}}();r()}},[I]),(0,c.useEffect)(function(){if(!(!D||!I||!P||!y)){var r=function(){var a=p()(d()().mark(function s(){var o;return d()().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return Q(!0),n.prev=1,n.next=4,(0,b._$)({id:D,from:P,to:y});case 4:o=n.sent,o&&(je(o),console.log("Compare result:",o)),n.next=12;break;case 8:n.prev=8,n.t0=n.catch(1),console.error("\u83B7\u53D6\u6BD4\u8F83\u7ED3\u679C\u5931\u8D25:",n.t0),g.ZP.error("\u83B7\u53D6\u6BD4\u8F83\u7ED3\u679C\u5931\u8D25");case 12:return n.prev=12,Q(!1),n.finish(12);case 15:case"end":return n.stop()}},s,null,[[1,8,12,15]])}));return function(){return a.apply(this,arguments)}}();r()}},[D,I,P,y]);var $e=function(){var a=P,s=y;_e(s),pe(a);var o=w,u=K,n=D,i=I;se("/tools/repo/compare/compare_result",{state:{sourceProjectId:i,targetProjectId:n,sourceProject:u,targetProject:o,sourceBranch:s,targetBranch:a},replace:!0})},He=function(){var r=p()(d()().mark(function a(){var s;return d()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:if(console.log("Comparing",w,P,"with",K,y),!(!D||!I||!P||!y)){u.next=4;break}return g.ZP.error("\u8BF7\u9009\u62E9\u8981\u6BD4\u8F83\u7684\u9879\u76EE\u548C\u5206\u652F"),u.abrupt("return");case 4:return Q(!0),u.prev=5,u.next=8,(0,b._$)({id:D,from:P,to:y});case 8:s=u.sent,s&&(je(s),console.log("Compare result:",s)),u.next=16;break;case 12:u.prev=12,u.t0=u.catch(5),console.error("\u83B7\u53D6\u6BD4\u8F83\u7ED3\u679C\u5931\u8D25:",u.t0),g.ZP.error("\u83B7\u53D6\u6BD4\u8F83\u7ED3\u679C\u5931\u8D25");case 16:return u.prev=16,Q(!1),u.finish(16);case 19:case"end":return u.stop()}},a,null,[[5,12,16,19]])}));return function(){return r.apply(this,arguments)}}(),Xe=function(){if(!D||!I||!P||!y){g.ZP.error("\u8BF7\u9009\u62E9\u8981\u5408\u5E76\u7684\u9879\u76EE\u548C\u5206\u652F");return}console.log("Creating merge request from",w,P,"to",K,y),se("/tools/repo/newMergeRequest",{state:{proId:D,proName:w,sourceBranch:P,targetBranch:y}})},ke=(0,c.useState)({}),xe=j()(ke,2),De=xe[0],ne=xe[1];(0,c.useEffect)(function(){if(l!=null&&l.diffs&&l.diffs.length>0){var r={};l.diffs.forEach(function(a){a.new_path&&(r[a.new_path]=!0)}),ne(r)}},[l==null?void 0:l.diffs]);var Ge=function(a){ne(function(s){return B()(B()({},s),{},T()({},a,!s[a]))})},Pe=function(a){if(l!=null&&l.diffs){var s={};l.diffs.forEach(function(o){o.new_path&&(s[o.new_path]=a)}),ne(s)}},Ve=function(a,s){if(!a)return a;var o=a;return(s==="add"&&a.startsWith("+")||s==="delete"&&a.startsWith("-")||a.startsWith(" "))&&(o=a.substring(1)),(0,e.jsx)("span",{children:o})},Je=function(a){if(!a)return"";var s=a.split(`
`),o=0,u=0,n=s.map(function(i){if(i.startsWith("@@")){var H=i.match(/@@ -(\d+),\d+ \+(\d+),\d+ @@/);return H&&(o=parseInt(H[1],10),u=parseInt(H[2],10)),{content:i,type:"header",oldLineNumber:null,newLineNumber:null}}else if(i.startsWith("-")&&!i.startsWith("---")){var Ye={content:i,type:"delete",oldLineNumber:o,newLineNumber:null};return o++,Ye}else if(i.startsWith("+")&&!i.startsWith("+++")){var qe={content:i,type:"add",oldLineNumber:null,newLineNumber:u};return u++,qe}else{if(i.startsWith("---")||i.startsWith("+++"))return{content:i,type:"meta",oldLineNumber:null,newLineNumber:null};var er={content:i,type:"context",oldLineNumber:o,newLineNumber:u};return o++,u++,er}});return(0,e.jsx)("table",{style:{width:"100%",borderCollapse:"collapse"},children:(0,e.jsx)("tbody",{children:n.map(function(i,H){return(0,e.jsxs)("tr",{style:{lineHeight:"1.5",backgroundColor:i.type==="add"?f==="light"?"#ecfdf0":"#1a4b2c":i.type==="delete"?f==="light"?"#fbe9eb":"#4b1a1a":""},children:[(0,e.jsx)("td",{style:{width:"50px",minWidth:"50px",maxWidth:"50px",textAlign:"right",color:"#6e7781",padding:"4px 8px",userSelect:"none",borderRight:"1px solid #d0d7de",backgroundColor:i.type==="add"?f==="light"?"#ddfbe6":"#143321":i.type==="delete"?f==="light"?"#f9d7dc":"#3c1414":"inherit"},children:i.oldLineNumber}),(0,e.jsx)("td",{style:{width:"50px",minWidth:"50px",maxWidth:"50px",textAlign:"right",color:"#6e7781",padding:"4px 8px",userSelect:"none",borderRight:"1px solid #d0d7de",backgroundColor:i.type==="add"?f==="light"?"#ddfbe6":"#143321":i.type==="delete"?f==="light"?"#f9d7dc":"#3c1414":"inherit"},children:i.newLineNumber}),(0,e.jsxs)("td",{style:{width:"20px",textAlign:"center",padding:"4px 0",userSelect:"none"},children:[i.type==="add"&&(0,e.jsx)("span",{style:{color:"#00a000",padding:"0 8px"},children:"+"}),i.type==="delete"&&(0,e.jsx)("span",{style:{color:"#ff0000",padding:"0 8px"},children:"-"})]}),(0,e.jsx)("td",{style:B()({padding:"4px 0 4px 8px",whiteSpace:"pre",fontFamily:'"Consolas", "Monaco", "Andale Mono", "Ubuntu Mono", monospace',fontSize:"14px",lineHeight:"1.5"},i.type==="header"?{color:"#6e7781"}:{}),children:Ve(i.content,i.type||"")})]},H)})})})},Qe=function(){if(!l||!l.diffs)return{changedFiles:0,additions:0,deletions:0};var a=l.diffs,s=0,o=0;return a.forEach(function(u){if(u.diff){var n=u.diff.split(`
`);n.forEach(function(i){i.startsWith("+")&&!i.startsWith("+++")&&s++,i.startsWith("-")&&!i.startsWith("---")&&o++})}}),{changedFiles:a.length,additions:s,deletions:o}},ae=Qe();return(0,e.jsxs)(N._z,{header:{title:"",breadcrumb:{}},children:[(0,e.jsx)($.Z,{customItems:[{path:"/tools",breadcrumbName:"\u5DE5\u5177\u5957\u4EF6"},{path:"/tools/repo",breadcrumbName:"\u4EE3\u7801"},{path:"/tools/repo/compare",breadcrumbName:"\u6BD4\u8F83\u4FEE\u8BA2\u7248\u672C"},{path:"",breadcrumbName:"\u6BD4\u8F83\u7ED3\u679C"}]}),(0,e.jsxs)("div",{children:[(0,e.jsxs)(L.Z,{gutter:[24,24],align:"middle",children:[(0,e.jsxs)(M.Z,{span:11,children:[(0,e.jsx)(ee,{level:4,style:{marginBottom:12,fontWeight:"normal"},children:"\u6765\u6E90"}),(0,e.jsxs)(L.Z,{gutter:[8,0],children:[(0,e.jsx)(M.Z,{span:12,children:(0,e.jsx)(W.Z,{style:{width:"100%"},value:w,disabled:!0,dropdownStyle:(0,A.XE)(f),suffixIcon:(0,e.jsx)("span",{style:{color:"#666"},children:"\u25BC"}),children:(0,e.jsx)(J,{value:w,children:w})})}),(0,e.jsx)(M.Z,{span:12,children:(0,e.jsx)(W.Z,{style:{width:"100%"},value:P,onChange:_e,dropdownStyle:(0,A.XE)(f),loading:Ue,suffixIcon:(0,e.jsx)("span",{style:{color:"#666"},children:"\u25BC"}),children:Ae.map(function(r){return(0,e.jsx)(J,{value:r.name,children:r.name},r.name)})})})]})]}),(0,e.jsx)(M.Z,{span:2,style:{display:"flex",justifyContent:"center",alignItems:"center"},children:(0,e.jsx)("div",{style:{fontSize:16,color:"#999"},children:"..."})}),(0,e.jsxs)(M.Z,{span:11,children:[(0,e.jsx)(ee,{level:4,style:{marginBottom:12,fontWeight:"normal"},children:"\u76EE\u6807"}),(0,e.jsxs)(L.Z,{gutter:[8,0],children:[(0,e.jsx)(M.Z,{span:12,children:(0,e.jsx)(W.Z,{style:{width:"100%"},value:K,disabled:!0,dropdownStyle:(0,A.XE)(f),suffixIcon:(0,e.jsx)("span",{style:{color:"#666"},children:"\u25BC"}),children:(0,e.jsx)(J,{value:K,children:K})})}),(0,e.jsx)(M.Z,{span:12,children:(0,e.jsx)(W.Z,{style:{width:"100%"},value:y,onChange:pe,dropdownStyle:(0,A.XE)(f),loading:Ne,suffixIcon:(0,e.jsx)("span",{style:{color:"#666"},children:"\u25BC"}),children:Ie.map(function(r){return(0,e.jsx)(J,{value:r.name,children:r.name},r.name)})})})]})]})]}),(0,e.jsx)(L.Z,{style:{marginTop:32},children:(0,e.jsx)(M.Z,{children:(0,e.jsxs)(z.Z,{size:8,children:[(0,e.jsx)(E.ZP,{type:"primary",onClick:He,style:{height:"32px"},children:"\u6BD4\u8F83"}),(0,e.jsx)(E.ZP,{onClick:$e,style:{height:"32px"},children:"\u4EA4\u6362\u7248\u672C"}),(0,e.jsx)(E.ZP,{onClick:Xe,style:{height:"32px"},children:"\u521B\u5EFA\u5408\u5E76\u8BF7\u6C42"})]})})}),(0,e.jsxs)("div",{style:{marginTop:36,border:"1px solid ".concat(f==="light"?"#f0f0f0":"#393B3C"),borderRadius:2},children:[(0,e.jsx)("div",{style:{padding:"16px 24px",backgroundColor:f==="light"?"#fafafa":"#2c2c2c"},children:(0,e.jsxs)(ee,{level:5,style:{margin:0},children:["\u63D0\u4EA4 (",(l==null||(re=l.commits)===null||re===void 0?void 0:re.length)||0,")"]})}),(0,e.jsx)(k.Z,{style:{margin:0,borderColor:f==="light"?"#f0f0f0":"#393B3C"}}),l!=null&&l.commits&&l.commits.length>0?(0,e.jsx)("div",{children:l.commits.map(function(r,a){return(0,e.jsxs)("div",{style:{padding:"16px 24px",display:"flex",alignItems:"center",borderBottom:a<l.commits.length-1?"1px solid ".concat(f==="light"?"#f0f0f0":"#393B3C"):"none"},children:[(0,e.jsx)("img",{src:"https://via.placeholder.com/24",alt:(r==null?void 0:r.author_name)||"",style:{width:24,height:24,borderRadius:"50%",marginRight:8}}),(0,e.jsxs)("div",{children:[(0,e.jsx)("div",{children:(0,e.jsx)(x,{strong:!0,children:(r==null?void 0:r.title)||""})}),(0,e.jsx)("div",{children:(0,e.jsxs)(x,{type:"secondary",children:[(r==null?void 0:r.author_name)||""," authored ",(0,V.ct)((r==null?void 0:r.authored_date)||"")]})})]}),(0,e.jsxs)("div",{style:{marginLeft:"auto",display:"flex",alignItems:"center"},children:[(0,e.jsx)(x,{code:!0,style:{marginRight:8},children:(r==null?void 0:r.short_id)||""}),(0,e.jsx)(Z.Z,{title:"\u590D\u5236\u63D0\u4EA4SHA",children:(0,e.jsx)(E.ZP,{icon:(0,e.jsx)(S.Z,{}),type:"text",onClick:function(){return navigator.clipboard.writeText((r==null?void 0:r.short_id)||"").then(function(){g.ZP.success("\u63D0\u4EA4SHA\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F")}).catch(function(o){console.error("\u590D\u5236\u5931\u8D25:",o),g.ZP.error("\u590D\u5236\u5931\u8D25")})}})},"copy2")]})]},a)})}):(0,e.jsx)("div",{style:{padding:"16px 24px",textAlign:"center"},children:(0,e.jsx)(x,{type:"secondary",children:"\u6CA1\u6709\u627E\u5230\u63D0\u4EA4\u4FE1\u606F"})})]}),(0,e.jsxs)("div",{style:{marginTop:36},children:[(0,e.jsxs)(z.Z,{children:[(0,e.jsx)(x,{children:"\u663E\u793A"}),(0,e.jsxs)(E.ZP,{type:"link",style:{padding:0},children:[ae.changedFiles," \u4E2A\u66F4\u6539\u7684\u6587\u4EF6"]}),(0,e.jsx)(x,{children:"\u5305\u542B"}),(0,e.jsxs)(x,{style:{color:"green"},children:[ae.additions," \u4E2A\u6DFB\u52A0"]}),(0,e.jsx)(x,{children:"\u548C"}),(0,e.jsxs)(x,{style:{color:"red"},children:[ae.deletions," \u4E2A\u5220\u9664"]})]}),(0,e.jsx)("div",{style:{float:"right"},children:(0,e.jsxs)(z.Z,{children:[(0,e.jsx)(E.ZP,{onClick:function(){return Pe(!0)},children:"\u5C55\u5F00\u5168\u90E8"}),(0,e.jsx)(E.ZP,{onClick:function(){return Pe(!1)},children:"\u6536\u8D77\u5168\u90E8"}),(0,e.jsx)(E.ZP,{children:"\u9690\u85CF\u7A7A\u767D\u66F4\u6539"})]})})]}),(0,e.jsxs)("div",{style:{marginTop:24,clear:"both"},children:[l==null||(te=l.diffs)===null||te===void 0?void 0:te.map(function(r,a){var s;return(0,e.jsxs)("div",{style:{marginBottom:16,border:"1px solid ".concat(f==="light"?"#f0f0f0":"#393B3C"),borderRadius:2},children:[(0,e.jsxs)("div",{style:{padding:"8px 16px",display:"flex",alignItems:"center",backgroundColor:f==="light"?"#fafafa":"#2c2c2c"},children:[(0,e.jsx)(E.ZP,{type:"text",icon:De[r.new_path]?(0,e.jsx)(_.Z,{style:{fontSize:"12px"}}):(0,e.jsx)(v.Z,{style:{fontSize:"12px"}}),style:{marginRight:8},onClick:function(){return Ge(r.new_path)}}),(0,e.jsxs)(x,{style:{flex:1},children:[r.new_file?(0,e.jsx)("span",{style:{color:"green"},children:"[\u65B0\u6587\u4EF6] "}):r.deleted_file?(0,e.jsx)("span",{style:{color:"red"},children:"[\u5DF2\u5220\u9664] "}):r.renamed_file?(0,e.jsx)("span",{style:{color:"blue"},children:"[\u5DF2\u91CD\u547D\u540D] "}):null,(0,e.jsx)("strong",{children:r.new_path})]}),(0,e.jsxs)(z.Z,{children:[(0,e.jsx)(Z.Z,{title:"\u590D\u5236\u6587\u4EF6\u8DEF\u5F84",children:(0,e.jsx)(E.ZP,{type:"text",icon:(0,e.jsx)(S.Z,{}),onClick:function(){navigator.clipboard.writeText(r.new_path).then(function(){g.ZP.success("\u6587\u4EF6\u8DEF\u5F84\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F")}).catch(function(u){console.error("\u590D\u5236\u5931\u8D25:",u),g.ZP.error("\u590D\u5236\u5931\u8D25")})}})},"copy"),(0,e.jsxs)(E.ZP,{children:["\u67E5\u770B\u6587\u4EF6 @",(l==null||(s=l.commits)===null||s===void 0||(s=s[0])===null||s===void 0?void 0:s.short_id)||"latest"]})]})]}),De[r.new_path]&&(0,e.jsx)("div",{style:{padding:"16px"},children:(0,e.jsx)("pre",{style:{margin:0,overflow:"auto"},children:(0,e.jsx)("code",{children:Je(r.diff||"\u65E0\u5DEE\u5F02\u5185\u5BB9")})})})]},a)}),(!(l!=null&&l.diffs)||l.diffs.length<=0)&&(0,e.jsx)("div",{style:{textAlign:"center",padding:"24px"},children:(0,e.jsx)(x,{type:"secondary",children:"\u6CA1\u6709\u627E\u5230\u6587\u4EF6\u5DEE\u5F02"})})]})]})]})}},44438:function(X,C,t){t.d(C,{E_:function(){return R},ct:function(){return O}});var O=function(m){if(!m)return"";var d=new Date,h=new Date(m),p=Math.abs(d.getTime()-h.getTime())/(1e3*60*60);return p>=24?"".concat(Math.round(p/24),"\u5929\u524D"):"".concat(Math.round(p),"\u5C0F\u65F6\u524D")},T=function(m){if(!m)return!1;var d=new Date,h=new Date(m),p=(d.getTime()-h.getTime())/(1e3*60*60*24);return p<=5},R=function(m){if(!m)return!1;var d=new Date,h=new Date(m),p=Math.abs(d.getTime()-h.getTime())/(1e3*60*60);return p<720}},44394:function(X,C,t){t.d(C,{E_:function(){return O.E_},XE:function(){return T.XE},gz:function(){return T.gz}});var O=t(44438),T=t(46671)},46671:function(X,C,t){t.d(C,{XE:function(){return m},gz:function(){return T}});var O=t(31622),T=function(){return(0,O.gh)()||"dark"},R=function(h){return h==="light"?"#fff":"#1a1c1e"},B=function(h){return h==="light"?"#1a1c1e":"#fff"},m=function(h){return{background:R(h),color:B(h)}}}}]);
