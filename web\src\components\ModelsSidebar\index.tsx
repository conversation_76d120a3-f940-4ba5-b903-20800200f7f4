import { getTheme } from '@/components/RightContent/themeSwitcher';
import {
  DatabaseOutlined,
  AppstoreOutlined,
  TableOutlined,
  CloudServerOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Menu } from 'antd';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

type MenuItem = Required<MenuProps>['items'][number];

interface ModelsSidebarProps {
  collapsed?: boolean;
  onCollapse?: () => void;
}

const ModelsSidebar: React.FC<ModelsSidebarProps> = ({ collapsed, onCollapse }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    const updateTheme = () => {
      setCurrentTheme(getTheme());
    };

    updateTheme();

    // 监听主题变化
    const observer = new MutationObserver(updateTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme'],
    });

    return () => observer.disconnect();
  }, []);

  const items: MenuItem[] = [
    {
      key: '/models',
      icon: <DatabaseOutlined />,
      label: 'Overview',
    },
    {
      key: '/models/applications',
      icon: <AppstoreOutlined />,
      label: 'Applications',
    },
    {
      key: '/models/datasets',
      icon: <TableOutlined />,
      label: 'Datasets',
    },
    {
      key: '/models/resources',
      icon: <CloudServerOutlined />,
      label: 'Resources',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key.startsWith('/')) {
      navigate(key, { state: location.state });
    }
  };

  // 获取当前应该高亮的菜单项
  const getSelectedMenuKey = () => {
    const { pathname } = location;

    // 精确匹配路径
    if (pathname === '/models') {
      return '/models';
    }
    if (pathname.startsWith('/models/applications')) {
      return '/models/applications';
    }
    if (pathname.startsWith('/models/datasets')) {
      return '/models/datasets';
    }
    if (pathname.startsWith('/models/resources')) {
      return '/models/resources';
    }

    // 默认返回overview
    return '/models';
  };

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      width: collapsed ? '64px' : '200px',
    }}>
      <div
        style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'space-between',
          paddingLeft: collapsed ? '0' : '24px',
          paddingRight: collapsed ? '0' : '16px',
          fontWeight: 'bold',
          fontSize: '18px',
          borderBottom: currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C',
          borderRight: currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C',
          backgroundColor: currentTheme === 'light' ? '#fff' : '#1a1c1e',
          color: currentTheme === 'light' ? '#1a1c1e' : '#fff',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <DatabaseOutlined
            style={{ fontSize: collapsed ? '22px' : '20px', marginRight: collapsed ? '0' : '10px' }}
          />
          {!collapsed && 'Software Factory'}
        </div>
        <div
          onClick={onCollapse}
          style={{
            cursor: 'pointer',
            padding: '4px',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: currentTheme === 'light' ? '#666' : '#ccc',
            opacity: collapsed ? 0 : 1,
            transition: 'opacity 0.2s',
          }}
        >
          {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        </div>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', flex: 1, overflow: 'hidden' }}>
        <Menu
          mode="inline"
          items={items}
          selectedKeys={[getSelectedMenuKey()]}
          onClick={handleMenuClick}
          style={{
            flex: 1,
            borderRight: currentTheme === 'light' ? '1px solid #D4D4D7' : '1px solid #393B3C',
            overflow: 'auto',
            backgroundColor: currentTheme === 'light' ? '#fff' : '#1a1c1e',
          }}
          theme={currentTheme === 'light' ? 'light' : 'dark'}
        />
      </div>
    </div>
  );
};

export default ModelsSidebar;
