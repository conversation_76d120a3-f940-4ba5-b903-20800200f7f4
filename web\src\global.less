@font-face {
  font-family: 'XinHe';
  src:local('XinHe'), url('./assets/font/font_xinhe.woff2') format('woff2');
}
@font-face {
  font-family: 'source serif 4';
  src:local('source serif 4'), url('./assets/font/source_serif.woff2') format('woff2');
}

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: Google Sans,"Helvetica Neue",sans-serif;
  // font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
  //   'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
  //   'Noto Color Emoji';

}

#root .ant-pro-layout .ant-pro-sider-logo{
  padding: 20px 12px;
}

#root .ant-pro-layout .ant-pro-sider-logo >a >h1{
  min-height: 24px;
  font-size: 17px;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

.ant-menu{
  font-size: 14px;
  line-height: 20px;
  font-weight: 550;
  border-radius: 8px;
}


//以下二者组合，实现菜单下边框
// .ant-menu-submenu{

//   border-bottom: 0 !important;
// }

// :where(.css-dev-only-do-not-override-cftkuk).ant-menu .ant-menu-item, :where(.css-dev-only-do-not-override-cftkuk).ant-menu .ant-menu-submenu, :where(.css-dev-only-do-not-override-cftkuk).ant-menu .ant-menu-submenu-title{
//     border-radius: 0;
//   border-bottom: 1px solid #393B3C;
// }


canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

#root .ant-card{
  border-radius:8px;
}

#root :where(.css-dev-only-do-not-override-19wldxz).ant-menu .ant-menu-item, :where(.css-dev-only-do-not-override-19wldxz).ant-menu .ant-menu-submenu, :where(.css-dev-only-do-not-override-19wldxz).ant-menu .ant-menu-submenu-title{
  border-radius: 8px;
}

#root .ant-pro-layout .ant-pro-layout-content{

  padding-block: 0;
  padding-inline: 0;
}

/* Override Ant Design Sider transition background only for repo sidebar */
.repo-sider.ant-layout-sider {
  transition: all 0.2s !important;
  background-color: inherit !important;
}

.repo-sider .ant-layout-sider-children {
  background-color: inherit !important;
}

/* Light theme */
[data-theme="light"] .repo-sider.ant-layout-sider,
[data-theme="light"] .repo-sider .ant-layout-sider-children {
  background-color: #fff !important;
}

/* Dark theme */
[data-theme="dark"] .repo-sider.ant-layout-sider,
[data-theme="dark"] .repo-sider .ant-layout-sider-children {
  background-color: #1a1c1e !important;
}

/* Ensure the transition is smooth without dark background for repo sidebar trigger */
.repo-sider .ant-layout-sider-trigger {
  background-color: inherit !important;
}

[data-theme="light"] .repo-sider .ant-layout-sider-trigger {
  background-color: #fff !important;
}

[data-theme="dark"] .repo-sider .ant-layout-sider-trigger {
  background-color: #1a1c1e !important;
}

#root .ant-divider-horizontal{
  margin: 4px 0 24px 0;
}
#root .ant-pro-layout .ant-pro-layout-container{
  min-height: 100vh;
}

/* Models Sidebar Styles */
.models-sider.ant-layout-sider {
  transition: all 0.2s !important;
  background-color: inherit !important;
}

.models-sider .ant-layout-sider-children {
  background-color: inherit !important;
}

/* Light theme for models sidebar */
[data-theme="light"] .models-sider.ant-layout-sider,
[data-theme="light"] .models-sider .ant-layout-sider-children {
  background-color: #fff !important;
}

/* Dark theme for models sidebar */
[data-theme="dark"] .models-sider.ant-layout-sider,
[data-theme="dark"] .models-sider .ant-layout-sider-children {
  background-color: #1a1c1e !important;
}

/* Models sidebar trigger */
.models-sider .ant-layout-sider-trigger {
  background-color: inherit !important;
}

[data-theme="light"] .models-sider .ant-layout-sider-trigger {
  background-color: #fff !important;
}

[data-theme="dark"] .models-sider .ant-layout-sider-trigger {
  background-color: #1a1c1e !important;
}

/* Light theme table styles */
[data-theme="light"] .ant-table {
  background-color: #fff;
}

[data-theme="light"] .ant-table-thead > tr > th {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

[data-theme="light"] .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
}

[data-theme="light"] .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* Table row alternating colors */
.table-row-light {
  background-color: #fff;
}

.table-row-dark {
  background-color: #fafafa;
}

[data-theme="light"] .table-row-light:hover {
  background-color: #f0f0f0 !important;
}

[data-theme="light"] .table-row-dark:hover {
  background-color: #f0f0f0 !important;
}