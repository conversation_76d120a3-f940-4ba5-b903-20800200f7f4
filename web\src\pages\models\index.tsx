import React, { useState } from 'react';
import { Table, Button, Tag, Space, Typography, Card } from 'antd';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;

interface ModelData {
  key: string;
  modelName: string;
  version: string;
  framework: string;
  status: 'Active' | 'Inactive';
  lastUpdated: string;
}

const Models: React.FC = () => {
  const [loading, setLoading] = useState(false);

  // 模拟数据
  const mockData: ModelData[] = [
    {
      key: '1',
      modelName: 'Predictive Maintenance Model',
      version: 'v1.2',
      framework: 'TensorFlow',
      status: 'Active',
      lastUpdated: '2024-01-15',
    },
    {
      key: '2',
      modelName: 'Customer Segmentation Model',
      version: 'v2.0',
      framework: 'PyTorch',
      status: 'Inactive',
      lastUpdated: '2023-12-20',
    },
    {
      key: '3',
      modelName: 'Fraud Detection Model',
      version: 'v1.5',
      framework: 'Scikit-learn',
      status: 'Active',
      lastUpdated: '2024-02-01',
    },
    {
      key: '4',
      modelName: 'Image Recognition Model',
      version: 'v1.0',
      framework: 'TensorFlow',
      status: 'Active',
      lastUpdated: '2024-02-10',
    },
    {
      key: '5',
      modelName: 'Natural Language Processing Model',
      version: 'v2.1',
      framework: 'PyTorch',
      status: 'Inactive',
      lastUpdated: '2024-01-25',
    },
  ];

  const columns: ColumnsType<ModelData> = [
    {
      title: 'Model Name',
      dataIndex: 'modelName',
      key: 'modelName',
      width: '30%',
      render: (text: string) => (
        <Text strong style={{ color: '#1890ff', cursor: 'pointer' }}>
          {text}
        </Text>
      ),
    },
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version',
      width: '15%',
      render: (text: string) => (
        <Tag color="blue">{text}</Tag>
      ),
    },
    {
      title: 'Framework',
      dataIndex: 'framework',
      key: 'framework',
      width: '20%',
      render: (text: string) => {
        let color = 'default';
        if (text === 'TensorFlow') color = 'orange';
        else if (text === 'PyTorch') color = 'red';
        else if (text === 'Scikit-learn') color = 'green';

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: '15%',
      render: (status: string) => (
        <Tag color={status === 'Active' ? 'green' : 'default'}>
          {status}
        </Tag>
      ),
    },
    {
      title: 'Last Updated',
      dataIndex: 'lastUpdated',
      key: 'lastUpdated',
      width: '20%',
      render: (text: string) => (
        <Text type="secondary">{text}</Text>
      ),
    },
  ];

  return (
    <div style={{
      padding: '24px',
      background: '#f5f5f5',
      minHeight: '100vh',
    }}>
      <Card
        style={{
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}
        bodyStyle={{ padding: '24px' }}
      >
        {/* 页面标题和描述 */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={2} style={{ margin: 0, color: '#262626' }}>
            Models
          </Title>
          <Text type="secondary" style={{ fontSize: '16px', marginTop: '8px', display: 'block' }}>
            Manage your machine learning models, evaluate performance, and deploy with ease.
          </Text>
        </div>

        {/* 操作按钮 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px'
        }}>
          <div>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                style={{ borderRadius: '6px' }}
              >
                New Model
              </Button>
              <Button
                icon={<UploadOutlined />}
                style={{ borderRadius: '6px' }}
              >
                Import Model
              </Button>
            </Space>
          </div>
        </div>

        {/* 模型列表表格 */}
        <Table
          columns={columns}
          dataSource={mockData}
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
            style: { marginTop: '16px' },
          }}
          style={{
            backgroundColor: '#fff',
          }}
          size="middle"
          bordered={false}
          rowClassName={(record, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
        />
      </Card>
    </div>
  );
};

export default Models;
