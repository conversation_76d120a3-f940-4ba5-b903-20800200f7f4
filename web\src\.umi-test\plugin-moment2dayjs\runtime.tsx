// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import dayjs from 'D:/project/web_app_0527v2/web/node_modules/dayjs';
import antdPlugin from 'D:/project/web_app_0527v2/web/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js';

import isSameOrBefore from 'D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isSameOrAfter';
import advancedFormat from 'D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/advancedFormat';
import customParseFormat from 'D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/customParseFormat';
import weekday from 'D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekday';
import weekYear from 'D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekYear';
import weekOfYear from 'D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekOfYear';
import isMoment from 'D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isMoment';
import localeData from 'D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/localeData';
import localizedFormat from 'D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/localizedFormat';
import duration from 'D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/duration';

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(advancedFormat);
dayjs.extend(customParseFormat);
dayjs.extend(weekday);
dayjs.extend(weekYear);
dayjs.extend(weekOfYear);
dayjs.extend(isMoment);
dayjs.extend(localeData);
dayjs.extend(localizedFormat);
dayjs.extend(duration);

dayjs.extend(antdPlugin);
