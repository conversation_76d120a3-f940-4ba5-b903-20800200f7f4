{"level":30,"time":1748595533827,"pid":17460,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] 默认使用 esbuild 作为 JavaScript 压缩工具，也可通过 jsMinifier 配置项切换到 terser 或 uglifyJs 等，详见 https://umijs.org/docs/api/config#jsminifier-webpack\u001b[39m"}
{"level":30,"time":1748595533884,"pid":17460,"hostname":"小丸犊子","msg":"generate files"}
{"level":30,"time":1748595535081,"pid":17460,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748595616945,"pid":4136,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] 如果想点击组件跳转至编辑器源码位置，可尝试新出的 clickToComponent 配置项，详见 https://umijs.org/docs/api/config#clicktocomponent\u001b[39m"}
{"level":30,"time":1748595616946,"pid":4136,"hostname":"小丸犊子","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748595618050,"pid":4136,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748595676107,"pid":4136,"hostname":"小丸犊子","msg":"Memory Usage: 857 MB (RSS: 1590.13 MB)"}
{"level":30,"time":1748595681288,"pid":4136,"hostname":"小丸犊子","msg":"File sizes after gzip:\n"}
{"level":32,"time":1748595681718,"pid":4136,"hostname":"小丸犊子","msg":"Build index.html"}
{"level":60,"time":1748597523097,"pid":22240,"hostname":"小丸犊子","err":{"type":"Error","message":"Cannot find module '../layouts/ModelsLayout' from 'D:/project/web_app_0527v2/web/src/pages'","stack":"Error: Cannot find module '../layouts/ModelsLayout' from 'D:/project/web_app_0527v2/web/src/pages'\n    at Function.resolveSync [as sync] (D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\utils\\compiled\\resolve\\index.js:1:12304)\n    at Object.onResolveComponent (D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:71:32)\n    at transformRoute (D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:72:44)\n    at D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:48:5\n    at Array.forEach (<anonymous>)\n    at transformRoutes (D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:47:15)\n    at getConfigRoutes (D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:38:3)\n    at Proxy.getRoutes (D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:61:46)\n    at Hook.fn (D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\preset-umi\\dist\\features\\appData\\appData.js:52:35)\n    at D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\core\\dist\\service\\service.js:136:38","code":"MODULE_NOT_FOUND"},"msg":"Cannot find module '../layouts/ModelsLayout' from 'D:/project/web_app_0527v2/web/src/pages'"}
{"level":60,"time":1748597523765,"pid":22240,"hostname":"小丸犊子","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1748597523779,"pid":22240,"hostname":"小丸犊子","msg":"D:\\project\\web_app_0527v2\\web\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1748597523794,"pid":22240,"hostname":"小丸犊子","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1748597848262,"pid":25680,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] 遇到难解的配置问题，试试从 Umi FAQ 中寻找答案，详见 https://umijs.org/docs/introduce/faq\u001b[39m"}
{"level":30,"time":1748597848266,"pid":25680,"hostname":"小丸犊子","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748597849515,"pid":25680,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":20,"time":1748597852024,"pid":25680,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":31,"time":1748597852071,"pid":25680,"hostname":"小丸犊子","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://*************:8088\u001b[39m              \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748597862472,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 10445 ms (711 modules)"}
{"level":30,"time":1748597862476,"pid":25680,"hostname":"小丸犊子","msg":"[MFSU] buildDeps since cacheDependency has changed"}
{"level":20,"time":1748597862476,"pid":25680,"hostname":"小丸犊子","msg":"D:/project/web_app_0527v2/web/node_modules/@umijs/renderer-react, D:/project/web_app_0527v2/web/node_modules/antd/dist/reset.css, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js, D:/project/web_app_0527v2/web/node_modules/react/jsx-dev-runtime, D:/project/web_app_0527v2/web/node_modules/antd, D:/project/web_app_0527v2/web/node_modules/umi/client/client/plugin.js, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/duration, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/localizedFormat, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/localeData, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isMoment, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekOfYear, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekYear, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/weekday, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/customParseFormat, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/advancedFormat, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isSameOrAfter, D:/project/web_app_0527v2/web/node_modules/dayjs/plugin/isSameOrBefore, D:/project/web_app_0527v2/web/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js, D:/project/web_app_0527v2/web/node_modules/dayjs, D:/project/web_app_0527v2/web/node_modules/@umijs/preset-umi/node_modules/regenerator-runtime/runtime.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url-search-params.size.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url-search-params.has.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url-search-params.delete.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.url.can-parse.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.structured-clone.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.self.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.immediate.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/web.dom-exception.stack.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-set.add-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.upsert.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.emplace.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.weak-map.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.to-hex.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.to-base64.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.from-hex.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.uint8-array.from-base64.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.unique-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.to-spliced.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.filter-reject.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.filter-out.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.typed-array.from-async.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.replace-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.pattern-match.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.observable.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.metadata-key.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.matcher.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-well-known.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-well-known-symbol.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-registered.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.is-registered-symbol.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.symbol.async-dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.dedent.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.code-points.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.cooked.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.string.at.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.union.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.union.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.symmetric-difference.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.symmetric-difference.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.join.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-superset-of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-superset-of.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-subset-of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-subset-of.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-disjoint-from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.is-disjoint-from.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.intersection.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.intersection.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.difference.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.difference.v2.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.set.add-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.regexp.escape.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.has-own-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.has-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-own-metadata-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-own-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-metadata-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.get-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.delete-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.reflect.define-metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.promise.try.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.observable.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.object.iterate-values.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.object.iterate-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.object.iterate-entries.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.number.range.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.number.from-string.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.umulh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.signbit.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.seeded-prng.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.scale.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.radians.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.rad-per-deg.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.isubh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.imulh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.iaddh.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.f16round.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.fscale.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.degrees.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.deg-per-rad.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.math.clamp.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.upsert.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.update-or-insert.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.update.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.merge.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.map-values.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.map-keys.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.key-of.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.key-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.includes.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.find-key.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.emplace.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.map.delete-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.json.raw-json.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.json.parse.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.json.is-raw-json.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.to-async.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.to-array.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.take.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.range.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.indexed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.for-each.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.flat-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.drop.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.as-indexed-pairs.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.iterator.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.un-this.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.metadata.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.is-constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.is-callable.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.function.demethodize.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.disposable-stack.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.set-uint8-clamped.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.set-float16.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.get-uint8-clamped.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.data-view.get-float16.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.composite-symbol.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.composite-key.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.bigint.range.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.to-array.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.take.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.some.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.indexed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.from.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.for-each.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.flat-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.find.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.filter.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.every.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.drop.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.async-dispose.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.as-indexed-pairs.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-iterator.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.async-disposable-stack.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array-buffer.transfer-to-fixed-length.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array-buffer.transfer.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array-buffer.detached.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.unique-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.last-item.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.last-index.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.is-template-object.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group-to-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group-by-to-map.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.group.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.filter-reject.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.filter-out.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.array.from-async.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/esnext.suppressed-error.constructor.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.with.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.to-sorted.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.to-reversed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.set.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.find-last-index.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.find-last.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.typed-array.at.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.to-well-formed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.replace-all.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.is-well-formed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.string.at-alternative.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.regexp.flags.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.reflect.to-string-tag.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.promise.with-resolvers.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.promise.any.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.object.has-own.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.object.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.map.group-by.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.with.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.to-spliced.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.to-sorted.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.to-reversed.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.reduce-right.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.reduce.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.push.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.find-last-index.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.find-last.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.array.at.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.aggregate-error.cause.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.aggregate-error.js, D:/project/web_app_0527v2/web/node_modules/core-js/modules/es.error.cause.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/typeof.js, D:/project/web_app_0527v2/web/node_modules/react, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js, D:/project/web_app_0527v2/web/node_modules/@ant-design/pro-components, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js, monaco-editor, @ant-design/pro-components, @ant-design/icons, D:/project/web_app_0527v2/web/node_modules/@umijs/preset-umi/node_modules/react-router-dom, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js, dayjs/plugin/relativeTime, dayjs, D:/project/web_app_0527v2/web/node_modules/fast-deep-equal/index.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/classCallCheck.js, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createClass.js, dayjs/locale/zh-cn, D:/project/web_app_0527v2/web/node_modules/antd/es/date-picker/locale/zh_CN, D:/project/web_app_0527v2/web/node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectWithoutProperties.js, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/DesktopOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/AppstoreOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/DeploymentUnitOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/LineChartOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/ToolOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/DatabaseOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/ProjectOutlined, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons/es/icons/ConsoleSqlOutlined, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/zh-tw, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/zh-cn, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/pt-br, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/ja, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/id, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/fa, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/en, D:/project/web_app_0527v2/web/node_modules/dayjs/locale/bn-bd, D:/project/web_app_0527v2/web/node_modules/@ant-design/icons, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/zh_TW, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/zh_CN, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/pt_BR, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/ja_JP, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/id_ID, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/fa_IR, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/en_US, D:/project/web_app_0527v2/web/node_modules/antd/es/locale/bn_BD, D:/project/web_app_0527v2/web/node_modules/@umijs/plugins/node_modules/react-intl, D:/project/web_app_0527v2/web/node_modules/warning, D:/project/web_app_0527v2/web/node_modules/event-emitter, D:/project/web_app_0527v2/web/node_modules/@umijs/plugins/node_modules/@ahooksjs/use-request, D:/project/web_app_0527v2/web/node_modules/axios, lodash, antd-style, keycloak-js, @monaco-editor/react, D:/project/web_app_0527v2/web/node_modules/react-dom, querystring, numeral, classnames"}
{"level":55,"time":1748597862660,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748597862661,"pid":25680,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748597863064,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 403 ms (607 modules)"}
{"level":55,"time":1748597863099,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748597863100,"pid":25680,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748597863222,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 122 ms (607 modules)"}
{"level":32,"time":1748597883669,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 19901 ms (13460 modules)"}
{"level":30,"time":1748597883754,"pid":25680,"hostname":"小丸犊子","msg":"[MFSU] write cache"}
{"level":30,"time":1748597883756,"pid":25680,"hostname":"小丸犊子","msg":"[MFSU] buildDepsAgain"}
{"level":30,"time":1748597883758,"pid":25680,"hostname":"小丸犊子","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748597947317,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748597947346,"pid":25680,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748597949375,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 2031 ms (607 modules)"}
{"level":30,"time":1748597949378,"pid":25680,"hostname":"小丸犊子","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748597963526,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748597963562,"pid":25680,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748597963783,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 222 ms (607 modules)"}
{"level":30,"time":1748597963785,"pid":25680,"hostname":"小丸犊子","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748597980346,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748597980368,"pid":25680,"hostname":"小丸犊子","msg":"webpack watched change"}
{"level":32,"time":1748597980574,"pid":25680,"hostname":"小丸犊子","msg":"[Webpack] Compiled in 206 ms (625 modules)"}
{"level":30,"time":1748597980576,"pid":25680,"hostname":"小丸犊子","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748598059695,"pid":26564,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] 全局样式、全局脚本写在哪里？创建 src/global.(ts|css) 轻松解决，详见 https://umijs.org/docs/guides/directory-structure#globaljtsx\u001b[39m"}
{"level":30,"time":1748598059857,"pid":26564,"hostname":"小丸犊子","msg":"generate files"}
{"level":30,"time":1748598061410,"pid":26564,"hostname":"小丸犊子","msg":"Preparing..."}
{"level":30,"time":1748598166897,"pid":16564,"hostname":"小丸犊子","msg":"\u001b[33m[你知道吗？] 编写 src/loading.(jsx|tsx) 可以自定义页面的加载动画。\u001b[39m"}
{"level":30,"time":1748598166958,"pid":16564,"hostname":"小丸犊子","msg":"generate files"}
{"level":30,"time":1748598168226,"pid":16564,"hostname":"小丸犊子","msg":"Preparing..."}
