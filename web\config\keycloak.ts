import Keycloak from 'keycloak-js';

const keycloakConfig = {
    url: 'http://localhost:8080',
    realm: 'dev_xh_key',
    clientId: 'sulei_01',
    redirectUri: window.location.origin + '/auth/callback',
    silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
    pkceMethod: 'S256',
    checkLoginIframe: false,
    onLoad: 'login-required'
};

const keycloak = new Keycloak(keycloakConfig);

export default keycloak;