(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1723],{48820:function(l,i){"use strict";var a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};i.Z=a},42110:function(l,i){"use strict";var a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};i.Z=a},47356:function(l,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};i.default=a},44149:function(l,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z"}}]},name:"arrow-right",theme:"outlined"};i.default=a},88372:function(l,i,a){"use strict";a.d(i,{f:function(){return S}});var n=a(4942),h=a(21532),o=a(93967),g=a.n(o),x=a(67294),R=a(76509),O=a(1413),Z=a(64847),se=function($){return(0,n.Z)({},$.componentCls,{width:"100%","&-wide":{maxWidth:1152,margin:"0 auto"}})};function G(U){return(0,Z.Xj)("ProLayoutGridContent",function($){var ce=(0,O.Z)((0,O.Z)({},$),{},{componentCls:".".concat(U)});return[se(ce)]})}var T=a(85893),S=function($){var ce=(0,x.useContext)(R.X),We=$.children,_=$.contentWidth,_e=$.className,W=$.style,z=(0,x.useContext)(h.ZP.ConfigContext),d=z.getPrefixCls,F=$.prefixCls||d("pro"),ne=_||ce.contentWidth,P="".concat(F,"-grid-content"),A=G(P),q=A.wrapSSR,L=A.hashId,ee=ne==="Fixed"&&ce.layout==="top";return q((0,T.jsx)("div",{className:g()(P,L,_e,(0,n.Z)({},"".concat(P,"-wide"),ee)),style:W,children:(0,T.jsx)("div",{className:"".concat(F,"-grid-content-children ").concat(L).trim(),children:We})}))}},84017:function(l,i,a){"use strict";a.d(i,{_z:function(){return Pe}});var n=a(4942),h=a(91),o=a(1413),g=a(71002),x=a(10915),R=a(11941),O=a(67159),Z=a(21532),se=a(64218),G=a(93967),T=a.n(G),S=a(67294),U=a(76509),$=a(12044),ce=a(98423),We=a(73935),_=a(64847),_e=function(r){return(0,n.Z)({},r.componentCls,{position:"fixed",insetInlineEnd:0,bottom:0,zIndex:99,display:"flex",alignItems:"center",width:"100%",paddingInline:24,paddingBlock:0,boxSizing:"border-box",lineHeight:"64px",backgroundColor:(0,_.uK)(r.colorBgElevated,.6),borderBlockStart:"1px solid ".concat(r.colorSplit),"-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)",color:r.colorText,transition:"all 0.2s ease 0s","&-left":{flex:1,color:r.colorText},"&-right":{color:r.colorText,"> *":{marginInlineEnd:8,"&:last-child":{marginBlock:0,marginInline:0}}}})};function W(H){return(0,_.Xj)("ProLayoutFooterToolbar",function(r){var f=(0,o.Z)((0,o.Z)({},r),{},{componentCls:".".concat(H)});return[_e(f)]})}function z(H,r){var f=r.stylish;return(0,_.Xj)("ProLayoutFooterToolbarStylish",function(E){var N=(0,o.Z)((0,o.Z)({},E),{},{componentCls:".".concat(H)});return f?[(0,n.Z)({},"".concat(N.componentCls),f==null?void 0:f(N))]:[]})}var d=a(85893),F=["children","className","extra","portalDom","style","renderContent"],ne=function(r){var f=r.children,E=r.className,N=r.extra,re=r.portalDom,oe=re===void 0?!0:re,ge=r.style,Be=r.renderContent,de=(0,h.Z)(r,F),Ce=(0,S.useContext)(Z.ZP.ConfigContext),be=Ce.getPrefixCls,Ee=Ce.getTargetContainer,Ie=r.prefixCls||be("pro"),ue="".concat(Ie,"-footer-bar"),Q=W(ue),$e=Q.wrapSSR,J=Q.hashId,C=(0,S.useContext)(U.X),we=(0,S.useMemo)(function(){var at=C.hasSiderMenu,ft=C.isMobile,Ot=C.siderWidth;if(at)return Ot?ft?"100%":"calc(100% - ".concat(Ot,"px)"):"100%"},[C.collapsed,C.hasSiderMenu,C.isMobile,C.siderWidth]),ke=(0,S.useMemo)(function(){return typeof window=="undefined"||typeof document=="undefined"?null:(Ee==null?void 0:Ee())||document.body},[]),Tt=z("".concat(ue,".").concat(ue,"-stylish"),{stylish:r.stylish}),xe=(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"".concat(ue,"-left ").concat(J).trim(),children:N}),(0,d.jsx)("div",{className:"".concat(ue,"-right ").concat(J).trim(),children:f})]});(0,S.useEffect)(function(){return!C||!(C!=null&&C.setHasFooterToolbar)?function(){}:(C==null||C.setHasFooterToolbar(!0),function(){var at;C==null||(at=C.setHasFooterToolbar)===null||at===void 0||at.call(C,!1)})},[]);var De=(0,d.jsx)("div",(0,o.Z)((0,o.Z)({className:T()(E,J,ue,(0,n.Z)({},"".concat(ue,"-stylish"),!!r.stylish)),style:(0,o.Z)({width:we},ge)},(0,ce.Z)(de,["prefixCls"])),{},{children:Be?Be((0,o.Z)((0,o.Z)((0,o.Z)({},r),C),{},{leftWidth:we}),xe):xe})),At=!(0,$.j)()||!oe||!ke?De:(0,We.createPortal)(De,ke,ue);return Tt.wrapSSR($e((0,d.jsx)(S.Fragment,{children:At},ue)))},P=a(88372),A=a(97685),q=a(3770),L=a.n(q),ee=a(77059),He=a.n(ee),Ct=a(85673),xt=a(85357),qe=a(78957),Xt=a(9220),gt=a(80334),c=function(){return{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}},M=function(r){var f;return(0,n.Z)({},r.componentCls,(0,o.Z)((0,o.Z)({},_.Wf===null||_.Wf===void 0?void 0:(0,_.Wf)(r)),{},(0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)({position:"relative",backgroundColor:r.colorWhite,paddingBlock:r.pageHeaderPaddingVertical+2,paddingInline:r.pageHeaderPadding,"&&-ghost":{backgroundColor:r.pageHeaderBgGhost},"&-no-children":{height:(f=r.layout)===null||f===void 0||(f=f.pageContainer)===null||f===void 0?void 0:f.paddingBlockPageContainerContent},"&&-has-breadcrumb":{paddingBlockStart:r.pageHeaderPaddingBreadCrumb},"&&-has-footer":{paddingBlockEnd:0},"& &-back":(0,n.Z)({marginInlineEnd:r.margin,fontSize:16,lineHeight:1,"&-button":(0,o.Z)((0,o.Z)({fontSize:16},_.Nd===null||_.Nd===void 0?void 0:(0,_.Nd)(r)),{},{color:r.pageHeaderColorBack,cursor:"pointer"})},"".concat(r.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:0})},"& ".concat("ant","-divider-vertical"),{height:14,marginBlock:0,marginInline:r.marginSM,verticalAlign:"middle"}),"& &-breadcrumb + &-heading",{marginBlockStart:r.marginXS}),"& &-heading",{display:"flex",justifyContent:"space-between","&-left":{display:"flex",alignItems:"center",marginBlock:r.marginXS/2,marginInlineEnd:0,marginInlineStart:0,overflow:"hidden"},"&-title":(0,o.Z)((0,o.Z)({marginInlineEnd:r.marginSM,marginBlockEnd:0,color:r.colorTextHeading,fontWeight:600,fontSize:r.pageHeaderFontSizeHeaderTitle,lineHeight:r.controlHeight+"px"},c()),{},(0,n.Z)({},"".concat(r.componentCls,"-rlt &"),{marginInlineEnd:0,marginInlineStart:r.marginSM})),"&-avatar":(0,n.Z)({marginInlineEnd:r.marginSM},"".concat(r.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:r.marginSM}),"&-tags":(0,n.Z)({},"".concat(r.componentCls,"-rlt &"),{float:"right"}),"&-sub-title":(0,o.Z)((0,o.Z)({marginInlineEnd:r.marginSM,color:r.colorTextSecondary,fontSize:r.pageHeaderFontSizeHeaderSubTitle,lineHeight:r.lineHeight},c()),{},(0,n.Z)({},"".concat(r.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:12})),"&-extra":(0,n.Z)((0,n.Z)({marginBlock:r.marginXS/2,marginInlineEnd:0,marginInlineStart:0,whiteSpace:"nowrap","> *":(0,n.Z)({"white-space":"unset"},"".concat(r.componentCls,"-rlt &"),{marginInlineEnd:r.marginSM,marginInlineStart:0})},"".concat(r.componentCls,"-rlt &"),{float:"left"}),"*:first-child",(0,n.Z)({},"".concat(r.componentCls,"-rlt &"),{marginInlineEnd:0}))}),"&-content",{paddingBlockStart:r.pageHeaderPaddingContentPadding}),"&-footer",{marginBlockStart:r.margin}),"&-compact &-heading",{flexWrap:"wrap"}),"&-wide",{maxWidth:1152,margin:"0 auto"}),"&-rtl",{direction:"rtl"})))};function j(H){return(0,_.Xj)("ProLayoutPageHeader",function(r){var f=(0,o.Z)((0,o.Z)({},r),{},{componentCls:".".concat(H),pageHeaderBgGhost:"transparent",pageHeaderPadding:16,pageHeaderPaddingVertical:4,pageHeaderPaddingBreadCrumb:r.paddingSM,pageHeaderColorBack:r.colorTextHeading,pageHeaderFontSizeHeaderTitle:r.fontSizeHeading4,pageHeaderFontSizeHeaderSubTitle:14,pageHeaderPaddingContentPadding:r.paddingSM});return[M(f)]})}var y=function(r,f,E,N){return!E||!N?null:(0,d.jsx)("div",{className:"".concat(r,"-back ").concat(f).trim(),children:(0,d.jsx)("div",{role:"button",onClick:function(oe){N==null||N(oe)},className:"".concat(r,"-back-button ").concat(f).trim(),"aria-label":"back",children:E})})},B=function(r,f){var E;return(E=r.items)!==null&&E!==void 0&&E.length?(0,d.jsx)(Ct.Z,(0,o.Z)((0,o.Z)({},r),{},{className:T()("".concat(f,"-breadcrumb"),r.className)})):null},Fe=function(r){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"ltr";return r.backIcon!==void 0?r.backIcon:f==="rtl"?(0,d.jsx)(He(),{}):(0,d.jsx)(L(),{})},Ue=function(r,f){var E=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"ltr",N=arguments.length>3?arguments[3]:void 0,re=f.title,oe=f.avatar,ge=f.subTitle,Be=f.tags,de=f.extra,Ce=f.onBack,be="".concat(r,"-heading"),Ee=re||ge||Be||de;if(!Ee)return null;var Ie=Fe(f,E),ue=y(r,N,Ie,Ce),Q=ue||oe||Ee;return(0,d.jsxs)("div",{className:be+" "+N,children:[Q&&(0,d.jsxs)("div",{className:"".concat(be,"-left ").concat(N).trim(),children:[ue,oe&&(0,d.jsx)(xt.Z,(0,o.Z)({className:T()("".concat(be,"-avatar"),N,oe.className)},oe)),re&&(0,d.jsx)("span",{className:"".concat(be,"-title ").concat(N).trim(),title:typeof re=="string"?re:void 0,children:re}),ge&&(0,d.jsx)("span",{className:"".concat(be,"-sub-title ").concat(N).trim(),title:typeof ge=="string"?ge:void 0,children:ge}),Be&&(0,d.jsx)("span",{className:"".concat(be,"-tags ").concat(N).trim(),children:Be})]}),de&&(0,d.jsx)("span",{className:"".concat(be,"-extra ").concat(N).trim(),children:(0,d.jsx)(qe.Z,{children:de})})]})},et=function(r,f,E){return f?(0,d.jsx)("div",{className:"".concat(r,"-footer ").concat(E).trim(),children:f}):null},Ve=function(r,f,E){return(0,d.jsx)("div",{className:"".concat(r,"-content ").concat(E).trim(),children:f})},Lt=function H(r){return r==null?void 0:r.map(function(f){var E;return(0,gt.ET)(!!f.breadcrumbName,"Route.breadcrumbName is deprecated, please use Route.title instead."),(0,o.Z)((0,o.Z)({},f),{},{breadcrumbName:void 0,children:void 0,title:f.title||f.breadcrumbName},(E=f.children)!==null&&E!==void 0&&E.length?{menu:{items:H(f.children)}}:{})})},wt=function(r){var f,E=S.useState(!1),N=(0,A.Z)(E,2),re=N[0],oe=N[1],ge=function(on){var ln=on.width;return oe(ln<768)},Be=S.useContext(Z.ZP.ConfigContext),de=Be.getPrefixCls,Ce=Be.direction,be=r.prefixCls,Ee=r.style,Ie=r.footer,ue=r.children,Q=r.breadcrumb,$e=r.breadcrumbRender,J=r.className,C=r.contentWidth,we=r.layout,ke=r.ghost,Tt=ke===void 0?!0:ke,xe=de("page-header",be),De=j(xe),At=De.wrapSSR,at=De.hashId,ft=function(){return Q&&!(Q!=null&&Q.items)&&Q!==null&&Q!==void 0&&Q.routes&&((0,gt.ET)(!1,"The routes of Breadcrumb is deprecated, please use items instead."),Q.items=Lt(Q.routes)),Q!=null&&Q.items?B(Q,xe):null},Ot=ft(),en=Q&&"props"in Q,Nt=(f=$e==null?void 0:$e((0,o.Z)((0,o.Z)({},r),{},{prefixCls:xe}),Ot))!==null&&f!==void 0?f:Ot,Ht=en?Q:Nt,kt=T()(xe,at,J,(0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)({},"".concat(xe,"-has-breadcrumb"),!!Ht),"".concat(xe,"-has-footer"),!!Ie),"".concat(xe,"-rtl"),Ce==="rtl"),"".concat(xe,"-compact"),re),"".concat(xe,"-wide"),C==="Fixed"&&we=="top"),"".concat(xe,"-ghost"),Tt)),Ut=Ue(xe,r,Ce,at),Yt=ue&&Ve(xe,ue,at),Je=et(xe,Ie,at);return!Ht&&!Ut&&!Je&&!Yt?(0,d.jsx)("div",{className:T()(at,["".concat(xe,"-no-children")])}):At((0,d.jsx)(Xt.Z,{onResize:ge,children:(0,d.jsxs)("div",{className:kt,style:Ee,children:[Ht,Ut,Yt,Je]})}))},zt=a(83832),St=function(r){if(!r)return 1;var f=r.backingStorePixelRatio||r.webkitBackingStorePixelRatio||r.mozBackingStorePixelRatio||r.msBackingStorePixelRatio||r.oBackingStorePixelRatio||1;return(window.devicePixelRatio||1)/f},Pt=function(r){var f=(0,_.dQ)(),E=f.token,N=r.children,re=r.style,oe=r.className,ge=r.markStyle,Be=r.markClassName,de=r.zIndex,Ce=de===void 0?9:de,be=r.gapX,Ee=be===void 0?212:be,Ie=r.gapY,ue=Ie===void 0?222:Ie,Q=r.width,$e=Q===void 0?120:Q,J=r.height,C=J===void 0?64:J,we=r.rotate,ke=we===void 0?-22:we,Tt=r.image,xe=r.offsetLeft,De=r.offsetTop,At=r.fontStyle,at=At===void 0?"normal":At,ft=r.fontWeight,Ot=ft===void 0?"normal":ft,en=r.fontColor,Nt=en===void 0?E.colorFill:en,Ht=r.fontSize,kt=Ht===void 0?16:Ht,Ut=r.fontFamily,Yt=Ut===void 0?"sans-serif":Ut,Je=r.prefixCls,Rt=(0,S.useContext)(Z.ZP.ConfigContext),on=Rt.getPrefixCls,ln=on("pro-layout-watermark",Je),Pn=T()("".concat(ln,"-wrapper"),oe),e=T()(ln,Be),t=(0,S.useState)(""),s=(0,A.Z)(t,2),u=s[0],v=s[1];return(0,S.useEffect)(function(){var b=document.createElement("canvas"),p=b.getContext("2d"),m=St(p),I="".concat((Ee+$e)*m,"px"),w="".concat((ue+C)*m,"px"),Y=xe||Ee/2,V=De||ue/2;if(b.setAttribute("width",I),b.setAttribute("height",w),!p){console.error("\u5F53\u524D\u73AF\u5883\u4E0D\u652F\u6301Canvas");return}p.translate(Y*m,V*m),p.rotate(Math.PI/180*Number(ke));var ie=$e*m,Se=C*m,Le=function(X){var k=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,fe=Number(kt)*m;p.font="".concat(at," normal ").concat(Ot," ").concat(fe,"px/").concat(Se,"px ").concat(Yt),p.fillStyle=Nt,Array.isArray(X)?X==null||X.forEach(function(Oe,Me){return p.fillText(Oe,0,Me*fe+k)}):p.fillText(X,0,k?k+fe:0),v(b.toDataURL())};if(Tt){var ae=new Image;ae.crossOrigin="anonymous",ae.referrerPolicy="no-referrer",ae.src=Tt,ae.onload=function(){if(p.drawImage(ae,0,0,ie,Se),v(b.toDataURL()),r.content){Le(r.content,ae.height+8);return}};return}if(r.content){Le(r.content);return}},[Ee,ue,xe,De,ke,at,Ot,$e,C,Yt,Nt,Tt,r.content,kt]),(0,d.jsxs)("div",{style:(0,o.Z)({position:"relative"},re),className:Pn,children:[N,(0,d.jsx)("div",{className:e,style:(0,o.Z)((0,o.Z)({zIndex:Ce,position:"absolute",left:0,top:0,width:"100%",height:"100%",backgroundSize:"".concat(Ee+$e,"px"),pointerEvents:"none",backgroundRepeat:"repeat"},u?{backgroundImage:"url('".concat(u,"')")}:{}),ge)})]})},Qe=[576,768,992,1200].map(function(H){return"@media (max-width: ".concat(H,"px)")}),Ze=(0,A.Z)(Qe,4),st=Ze[0],dt=Ze[1],tt=Ze[2],Zt=Ze[3],$t=function(r){var f,E,N,re,oe,ge,Be,de,Ce,be,Ee,Ie,ue,Q,$e,J,C,we;return(0,n.Z)({},r.componentCls,(0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)({position:"relative","&-children-container":{paddingBlockStart:0,paddingBlockEnd:(f=r.layout)===null||f===void 0||(f=f.pageContainer)===null||f===void 0?void 0:f.paddingBlockPageContainerContent,paddingInline:(E=r.layout)===null||E===void 0||(E=E.pageContainer)===null||E===void 0?void 0:E.paddingInlinePageContainerContent},"&-children-container-no-header":{paddingBlockStart:(N=r.layout)===null||N===void 0||(N=N.pageContainer)===null||N===void 0?void 0:N.paddingBlockPageContainerContent},"&-affix":(0,n.Z)({},"".concat(r.antCls,"-affix"),(0,n.Z)({},"".concat(r.componentCls,"-warp"),{backgroundColor:(re=r.layout)===null||re===void 0||(re=re.pageContainer)===null||re===void 0?void 0:re.colorBgPageContainerFixed,transition:"background-color 0.3s",boxShadow:"0 2px 8px #f0f1f2"}))},"& &-warp-page-header",(0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)({paddingBlockStart:((oe=(ge=r.layout)===null||ge===void 0||(ge=ge.pageContainer)===null||ge===void 0?void 0:ge.paddingBlockPageContainerContent)!==null&&oe!==void 0?oe:40)/4,paddingBlockEnd:((Be=(de=r.layout)===null||de===void 0||(de=de.pageContainer)===null||de===void 0?void 0:de.paddingBlockPageContainerContent)!==null&&Be!==void 0?Be:40)/2,paddingInlineStart:(Ce=r.layout)===null||Ce===void 0||(Ce=Ce.pageContainer)===null||Ce===void 0?void 0:Ce.paddingInlinePageContainerContent,paddingInlineEnd:(be=r.layout)===null||be===void 0||(be=be.pageContainer)===null||be===void 0?void 0:be.paddingInlinePageContainerContent},"& ~ ".concat(r.proComponentsCls,"-grid-content"),(0,n.Z)({},"".concat(r.proComponentsCls,"-page-container-children-content"),{paddingBlock:((Ee=(Ie=r.layout)===null||Ie===void 0||(Ie=Ie.pageContainer)===null||Ie===void 0?void 0:Ie.paddingBlockPageContainerContent)!==null&&Ee!==void 0?Ee:24)/3})),"".concat(r.antCls,"-page-header-breadcrumb"),{paddingBlockStart:((ue=(Q=r.layout)===null||Q===void 0||(Q=Q.pageContainer)===null||Q===void 0?void 0:Q.paddingBlockPageContainerContent)!==null&&ue!==void 0?ue:40)/4+10}),"".concat(r.antCls,"-page-header-heading"),{paddingBlockStart:(($e=(J=r.layout)===null||J===void 0||(J=J.pageContainer)===null||J===void 0?void 0:J.paddingBlockPageContainerContent)!==null&&$e!==void 0?$e:40)/4}),"".concat(r.antCls,"-page-header-footer"),{marginBlockStart:((C=(we=r.layout)===null||we===void 0||(we=we.pageContainer)===null||we===void 0?void 0:we.paddingBlockPageContainerContent)!==null&&C!==void 0?C:40)/4})),"&-detail",(0,n.Z)({display:"flex"},st,{display:"block"})),"&-main",{width:"100%"}),"&-row",(0,n.Z)({display:"flex",width:"100%"},dt,{display:"block"})),"&-content",{flex:"auto",width:"100%"}),"&-extraContent",(0,n.Z)((0,n.Z)((0,n.Z)((0,n.Z)({flex:"0 1 auto",minWidth:"242px",marginInlineStart:88,textAlign:"end"},Zt,{marginInlineStart:44}),tt,{marginInlineStart:20}),dt,{marginInlineStart:0,textAlign:"start"}),st,{marginInlineStart:0})))};function ut(H,r){return(0,_.Xj)("ProLayoutPageContainer",function(f){var E,N=(0,o.Z)((0,o.Z)({},f),{},{componentCls:".".concat(H),layout:(0,o.Z)((0,o.Z)({},f==null?void 0:f.layout),{},{pageContainer:(0,o.Z)((0,o.Z)({},f==null||(E=f.layout)===null||E===void 0?void 0:E.pageContainer),r)})});return[$t(N)]})}function nt(H,r){var f=r.stylish;return(0,_.Xj)("ProLayoutPageContainerStylish",function(E){var N=(0,o.Z)((0,o.Z)({},E),{},{componentCls:".".concat(H)});return f?[(0,n.Z)({},"div".concat(N.componentCls),f==null?void 0:f(N))]:[]})}var Gt=a(1977),bt=["title","content","pageHeaderRender","header","prefixedClassName","extraContent","childrenContentStyle","style","prefixCls","hashId","value","breadcrumbRender"],Et=["children","loading","className","style","footer","affixProps","token","fixedHeader","breadcrumbRender","footerToolBarProps","childrenContentStyle"];function ht(H){return(0,g.Z)(H)==="object"?H:{spinning:H}}var Kt=function(r){var f=r.tabList,E=r.tabActiveKey,N=r.onTabChange,re=r.hashId,oe=r.tabBarExtraContent,ge=r.tabProps,Be=r.prefixedClassName;return Array.isArray(f)||oe?(0,d.jsx)(R.Z,(0,o.Z)((0,o.Z)({className:"".concat(Be,"-tabs ").concat(re).trim(),activeKey:E,onChange:function(Ce){N&&N(Ce)},tabBarExtraContent:oe,items:f==null?void 0:f.map(function(de,Ce){var be;return(0,o.Z)((0,o.Z)({label:de.tab},de),{},{key:((be=de.key)===null||be===void 0?void 0:be.toString())||(Ce==null?void 0:Ce.toString())})})},ge),{},{children:(0,Gt.n)(O.Z,"4.23.0")<0?f==null?void 0:f.map(function(de,Ce){return(0,d.jsx)(R.Z.TabPane,(0,o.Z)({tab:de.tab},de),de.key||Ce)}):null})):null},rn=function(r,f,E,N){return!r&&!f?null:(0,d.jsx)("div",{className:"".concat(E,"-detail ").concat(N).trim(),children:(0,d.jsx)("div",{className:"".concat(E,"-main ").concat(N).trim(),children:(0,d.jsxs)("div",{className:"".concat(E,"-row ").concat(N).trim(),children:[r&&(0,d.jsx)("div",{className:"".concat(E,"-content ").concat(N).trim(),children:r}),f&&(0,d.jsx)("div",{className:"".concat(E,"-extraContent ").concat(N).trim(),children:f})]})})})},_t=function(r){var f=useContext(RouteContext);return _jsx("div",{style:{height:"100%",display:"flex",alignItems:"center"},children:_jsx(Breadcrumb,_objectSpread(_objectSpread(_objectSpread({},f==null?void 0:f.breadcrumb),f==null?void 0:f.breadcrumbProps),r))})},qt=function(r){var f,E=r.title,N=r.content,re=r.pageHeaderRender,oe=r.header,ge=r.prefixedClassName,Be=r.extraContent,de=r.childrenContentStyle,Ce=r.style,be=r.prefixCls,Ee=r.hashId,Ie=r.value,ue=r.breadcrumbRender,Q=(0,h.Z)(r,bt),$e=function(){if(ue)return ue};if(re===!1)return null;if(re)return(0,d.jsxs)(d.Fragment,{children:[" ",re((0,o.Z)((0,o.Z)({},r),Ie))]});var J=E;!E&&E!==!1&&(J=Ie.title);var C=(0,o.Z)((0,o.Z)((0,o.Z)({},Ie),{},{title:J},Q),{},{footer:Kt((0,o.Z)((0,o.Z)({},Q),{},{hashId:Ee,breadcrumbRender:ue,prefixedClassName:ge}))},oe),we=C,ke=we.breadcrumb,Tt=(!ke||!(ke!=null&&ke.itemRender)&&!(ke!=null&&(f=ke.items)!==null&&f!==void 0&&f.length))&&!ue;return["title","subTitle","extra","tags","footer","avatar","backIcon"].every(function(xe){return!C[xe]})&&Tt&&!N&&!Be?null:(0,d.jsx)(wt,(0,o.Z)((0,o.Z)({},C),{},{className:"".concat(ge,"-warp-page-header ").concat(Ee).trim(),breadcrumb:ue===!1?void 0:(0,o.Z)((0,o.Z)({},C.breadcrumb),Ie.breadcrumbProps),breadcrumbRender:$e(),prefixCls:be,children:(oe==null?void 0:oe.children)||rn(N,Be,ge,Ee)}))},Xe=function(r){var f,E,N=r.children,re=r.loading,oe=re===void 0?!1:re,ge=r.className,Be=r.style,de=r.footer,Ce=r.affixProps,be=r.token,Ee=r.fixedHeader,Ie=r.breadcrumbRender,ue=r.footerToolBarProps,Q=r.childrenContentStyle,$e=(0,h.Z)(r,Et),J=(0,S.useContext)(U.X);(0,S.useEffect)(function(){var Je;return!J||!(J!=null&&J.setHasPageContainer)?function(){}:(J==null||(Je=J.setHasPageContainer)===null||Je===void 0||Je.call(J,function(Rt){return Rt+1}),function(){var Rt;J==null||(Rt=J.setHasPageContainer)===null||Rt===void 0||Rt.call(J,function(on){return on-1})})},[]);var C=(0,S.useContext)(x.L_),we=C.token,ke=(0,S.useContext)(Z.ZP.ConfigContext),Tt=ke.getPrefixCls,xe=r.prefixCls||Tt("pro"),De="".concat(xe,"-page-container"),At=ut(De,be),at=At.wrapSSR,ft=At.hashId,Ot=nt("".concat(De,".").concat(De,"-stylish"),{stylish:r.stylish}),en=(0,S.useMemo)(function(){var Je;return Ie==!1?!1:Ie||($e==null||(Je=$e.header)===null||Je===void 0?void 0:Je.breadcrumbRender)},[Ie,$e==null||(f=$e.header)===null||f===void 0?void 0:f.breadcrumbRender]),Nt=qt((0,o.Z)((0,o.Z)({},$e),{},{breadcrumbRender:en,ghost:!0,hashId:ft,prefixCls:void 0,prefixedClassName:De,value:J})),Ht=(0,S.useMemo)(function(){if(S.isValidElement(oe))return oe;if(typeof oe=="boolean"&&!oe)return null;var Je=ht(oe);return Je.spinning?(0,d.jsx)(zt.S,(0,o.Z)({},Je)):null},[oe]),kt=(0,S.useMemo)(function(){return N?(0,d.jsx)(d.Fragment,{children:(0,d.jsx)("div",{className:T()(ft,"".concat(De,"-children-container"),(0,n.Z)({},"".concat(De,"-children-container-no-header"),!Nt)),style:Q,children:N})}):null},[N,De,Q,ft]),Ut=(0,S.useMemo)(function(){var Je=Ht||kt;if(r.waterMarkProps||J.waterMarkProps){var Rt=(0,o.Z)((0,o.Z)({},J.waterMarkProps),r.waterMarkProps);return(0,d.jsx)(Pt,(0,o.Z)((0,o.Z)({},Rt),{},{children:Je}))}return Je},[r.waterMarkProps,J.waterMarkProps,Ht,kt]),Yt=T()(De,ft,ge,(0,n.Z)((0,n.Z)((0,n.Z)({},"".concat(De,"-with-footer"),de),"".concat(De,"-with-affix"),Ee&&Nt),"".concat(De,"-stylish"),!!$e.stylish));return at(Ot.wrapSSR((0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{style:Be,className:Yt,children:[Ee&&Nt?(0,d.jsx)(se.Z,(0,o.Z)((0,o.Z)({offsetTop:J.hasHeader&&J.fixedHeader?(E=we.layout)===null||E===void 0||(E=E.header)===null||E===void 0?void 0:E.heightLayoutHeader:1},Ce),{},{className:"".concat(De,"-affix ").concat(ft).trim(),children:(0,d.jsx)("div",{className:"".concat(De,"-warp ").concat(ft).trim(),children:Nt})})):Nt,Ut&&(0,d.jsx)(P.f,{children:Ut})]}),de&&(0,d.jsx)(ne,(0,o.Z)((0,o.Z)({stylish:$e.footerStylish,prefixCls:xe},ue),{},{children:de}))]})))},Pe=function(r){return(0,d.jsx)(x._Y,{needDeps:!0,children:(0,d.jsx)(Xe,(0,o.Z)({},r))})},pe=function(r){var f=useContext(RouteContext);return qt(_objectSpread(_objectSpread({},r),{},{hashId:"",value:f}))}},3770:function(l,i,a){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;const n=h(a(27863));function h(g){return g&&g.__esModule?g:{default:g}}const o=n;i.default=o,l.exports=o},77059:function(l,i,a){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;const n=h(a(21379));function h(g){return g&&g.__esModule?g:{default:g}}const o=n;i.default=o,l.exports=o},33046:function(l,i,a){"use strict";"use client";var n=a(64836).default,h=a(75263).default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o=n(a(10434)),g=n(a(27424)),x=n(a(38416)),R=n(a(70215)),O=h(a(67294)),Z=n(a(93967)),se=a(87646),G=n(a(61711)),T=n(a(27727)),S=a(26814),U=a(72014),$=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];(0,S.setTwoToneColor)(se.blue.primary);var ce=O.forwardRef(function(_,_e){var W=_.className,z=_.icon,d=_.spin,F=_.rotate,ne=_.tabIndex,P=_.onClick,A=_.twoToneColor,q=(0,R.default)(_,$),L=O.useContext(G.default),ee=L.prefixCls,He=ee===void 0?"anticon":ee,Ct=L.rootClassName,xt=(0,Z.default)(Ct,He,(0,x.default)((0,x.default)({},"".concat(He,"-").concat(z.name),!!z.name),"".concat(He,"-spin"),!!d||z.name==="loading"),W),qe=ne;qe===void 0&&P&&(qe=-1);var Xt=F?{msTransform:"rotate(".concat(F,"deg)"),transform:"rotate(".concat(F,"deg)")}:void 0,gt=(0,U.normalizeTwoToneColors)(A),c=(0,g.default)(gt,2),M=c[0],j=c[1];return O.createElement("span",(0,o.default)({role:"img","aria-label":z.name},q,{ref:_e,tabIndex:qe,onClick:P,className:xt}),O.createElement(T.default,{icon:z,primaryColor:M,secondaryColor:j,style:Xt}))});ce.displayName="AntdIcon",ce.getTwoToneColor=S.getTwoToneColor,ce.setTwoToneColor=S.setTwoToneColor;var We=i.default=ce},61711:function(l,i,a){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n=a(67294),h=(0,n.createContext)({}),o=i.default=h},27727:function(l,i,a){"use strict";var n=a(64836).default,h=a(75263).default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o=n(a(70215)),g=n(a(42122)),x=h(a(67294)),R=a(72014),O=["icon","className","onClick","style","primaryColor","secondaryColor"],Z={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function se(U){var $=U.primaryColor,ce=U.secondaryColor;Z.primaryColor=$,Z.secondaryColor=ce||(0,R.getSecondaryColor)($),Z.calculated=!!ce}function G(){return(0,g.default)({},Z)}var T=function($){var ce=$.icon,We=$.className,_=$.onClick,_e=$.style,W=$.primaryColor,z=$.secondaryColor,d=(0,o.default)($,O),F=x.useRef(),ne=Z;if(W&&(ne={primaryColor:W,secondaryColor:z||(0,R.getSecondaryColor)(W)}),(0,R.useInsertStyles)(F),(0,R.warning)((0,R.isIconDefinition)(ce),"icon should be icon definiton, but got ".concat(ce)),!(0,R.isIconDefinition)(ce))return null;var P=ce;return P&&typeof P.icon=="function"&&(P=(0,g.default)((0,g.default)({},P),{},{icon:P.icon(ne.primaryColor,ne.secondaryColor)})),(0,R.generate)(P.icon,"svg-".concat(P.name),(0,g.default)((0,g.default)({className:We,onClick:_,style:_e,"data-icon":P.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},d),{},{ref:F}))};T.displayName="IconReact",T.getTwoToneColors=G,T.setTwoToneColors=se;var S=i.default=T},26814:function(l,i,a){"use strict";var n=a(64836).default;Object.defineProperty(i,"__esModule",{value:!0}),i.getTwoToneColor=R,i.setTwoToneColor=x;var h=n(a(27424)),o=n(a(27727)),g=a(72014);function x(O){var Z=(0,g.normalizeTwoToneColors)(O),se=(0,h.default)(Z,2),G=se[0],T=se[1];return o.default.setTwoToneColors({primaryColor:G,secondaryColor:T})}function R(){var O=o.default.getTwoToneColors();return O.calculated?[O.primaryColor,O.secondaryColor]:O.primaryColor}},27863:function(l,i,a){"use strict";var n=a(75263).default,h=a(64836).default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o=h(a(10434)),g=n(a(67294)),x=h(a(47356)),R=h(a(33046)),O=function(T,S){return g.createElement(R.default,(0,o.default)({},T,{ref:S,icon:x.default}))},Z=g.forwardRef(O),se=i.default=Z},21379:function(l,i,a){"use strict";var n=a(75263).default,h=a(64836).default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o=h(a(10434)),g=n(a(67294)),x=h(a(44149)),R=h(a(33046)),O=function(T,S){return g.createElement(R.default,(0,o.default)({},T,{ref:S,icon:x.default}))},Z=g.forwardRef(O),se=i.default=Z},72014:function(l,i,a){"use strict";var n=a(75263).default,h=a(64836).default;Object.defineProperty(i,"__esModule",{value:!0}),i.generate=ce,i.getSecondaryColor=We,i.iconStyles=void 0,i.isIconDefinition=U,i.normalizeAttrs=$,i.normalizeTwoToneColors=_,i.useInsertStyles=i.svgBaseProps=void 0,i.warning=S;var o=h(a(42122)),g=h(a(18698)),x=a(87646),R=a(93399),O=a(63298),Z=h(a(45520)),se=n(a(67294)),G=h(a(61711));function T(d){return d.replace(/-(.)/g,function(F,ne){return ne.toUpperCase()})}function S(d,F){(0,Z.default)(d,"[@ant-design/icons] ".concat(F))}function U(d){return(0,g.default)(d)==="object"&&typeof d.name=="string"&&typeof d.theme=="string"&&((0,g.default)(d.icon)==="object"||typeof d.icon=="function")}function $(){var d=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(d).reduce(function(F,ne){var P=d[ne];switch(ne){case"class":F.className=P,delete F.class;break;default:delete F[ne],F[T(ne)]=P}return F},{})}function ce(d,F,ne){return ne?se.default.createElement(d.tag,(0,o.default)((0,o.default)({key:F},$(d.attrs)),ne),(d.children||[]).map(function(P,A){return ce(P,"".concat(F,"-").concat(d.tag,"-").concat(A))})):se.default.createElement(d.tag,(0,o.default)({key:F},$(d.attrs)),(d.children||[]).map(function(P,A){return ce(P,"".concat(F,"-").concat(d.tag,"-").concat(A))}))}function We(d){return(0,x.generate)(d)[0]}function _(d){return d?Array.isArray(d)?d:[d]:[]}var _e=i.svgBaseProps={width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",focusable:"false"},W=i.iconStyles=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,z=i.useInsertStyles=function(F){var ne=(0,se.useContext)(G.default),P=ne.csp,A=ne.prefixCls,q=ne.layer,L=W;A&&(L=L.replace(/anticon/g,A)),q&&(L="@layer ".concat(q,` {
`).concat(L,`
}`)),(0,se.useEffect)(function(){var ee=F.current,He=(0,O.getShadowRoot)(ee);(0,R.updateCSS)(L,"@ant-design-icons",{prepend:!q,csp:P,attachTo:He})},[])}},64218:function(l,i,a){"use strict";a.d(i,{Z:function(){return ne}});var n=a(67294),h=a(93967),o=a.n(h),g=a(9220),x=a(74902),R=a(75164);function O(P){let A;const q=ee=>()=>{A=null,P.apply(void 0,(0,x.Z)(ee))},L=(...ee)=>{A==null&&(A=(0,R.Z)(q(ee)))};return L.cancel=()=>{R.Z.cancel(A),A=null},L}var Z=O,se=a(53124),G=a(83559);const T=P=>{const{componentCls:A}=P;return{[A]:{position:"fixed",zIndex:P.zIndexPopup}}},S=P=>({zIndexPopup:P.zIndexBase+10});var U=(0,G.I$)("Affix",T,S);function $(P){return P!==window?P.getBoundingClientRect():{top:0,bottom:window.innerHeight}}function ce(P,A,q){if(q!==void 0&&Math.round(A.top)>Math.round(P.top)-q)return q+A.top}function We(P,A,q){if(q!==void 0&&Math.round(A.bottom)<Math.round(P.bottom)+q){const L=window.innerHeight-A.bottom;return q+L}}var _=function(P,A){var q={};for(var L in P)Object.prototype.hasOwnProperty.call(P,L)&&A.indexOf(L)<0&&(q[L]=P[L]);if(P!=null&&typeof Object.getOwnPropertySymbols=="function")for(var ee=0,L=Object.getOwnPropertySymbols(P);ee<L.length;ee++)A.indexOf(L[ee])<0&&Object.prototype.propertyIsEnumerable.call(P,L[ee])&&(q[L[ee]]=P[L[ee]]);return q};const _e=["resize","scroll","touchstart","touchmove","touchend","pageshow","load"];function W(){return typeof window!="undefined"?window:null}const z=0,d=1;var ne=n.forwardRef((P,A)=>{var q;const{style:L,offsetTop:ee,offsetBottom:He,prefixCls:Ct,className:xt,rootClassName:qe,children:Xt,target:gt,onChange:c,onTestUpdatePosition:M}=P,j=_(P,["style","offsetTop","offsetBottom","prefixCls","className","rootClassName","children","target","onChange","onTestUpdatePosition"]),{getPrefixCls:y,getTargetContainer:B}=n.useContext(se.E_),Fe=y("affix",Ct),[Ue,et]=n.useState(!1),[Ve,Lt]=n.useState(),[wt,zt]=n.useState(),St=n.useRef(z),Pt=n.useRef(null),Qe=n.useRef(null),Ze=n.useRef(null),st=n.useRef(null),dt=n.useRef(null),tt=(q=gt!=null?gt:B)!==null&&q!==void 0?q:W,Zt=He===void 0&&ee===void 0?0:ee,$t=()=>{if(St.current!==d||!st.current||!Ze.current||!tt)return;const Xe=tt();if(Xe){const Pe={status:z},pe=$(Ze.current);if(pe.top===0&&pe.left===0&&pe.width===0&&pe.height===0)return;const H=$(Xe),r=ce(pe,H,Zt),f=We(pe,H,He);r!==void 0?(Pe.affixStyle={position:"fixed",top:r,width:pe.width,height:pe.height},Pe.placeholderStyle={width:pe.width,height:pe.height}):f!==void 0&&(Pe.affixStyle={position:"fixed",bottom:f,width:pe.width,height:pe.height},Pe.placeholderStyle={width:pe.width,height:pe.height}),Pe.lastAffix=!!Pe.affixStyle,Ue!==Pe.lastAffix&&(c==null||c(Pe.lastAffix)),St.current=Pe.status,Lt(Pe.affixStyle),zt(Pe.placeholderStyle),et(Pe.lastAffix)}},ut=()=>{St.current=d,$t()},nt=Z(()=>{ut()}),Gt=Z(()=>{if(tt&&Ve){const Xe=tt();if(Xe&&Ze.current){const Pe=$(Xe),pe=$(Ze.current),H=ce(pe,Pe,Zt),r=We(pe,Pe,He);if(H!==void 0&&Ve.top===H||r!==void 0&&Ve.bottom===r)return}}ut()}),bt=()=>{const Xe=tt==null?void 0:tt();Xe&&(_e.forEach(Pe=>{var pe;Qe.current&&((pe=Pt.current)===null||pe===void 0||pe.removeEventListener(Pe,Qe.current)),Xe==null||Xe.addEventListener(Pe,Gt)}),Pt.current=Xe,Qe.current=Gt)},Et=()=>{dt.current&&(clearTimeout(dt.current),dt.current=null);const Xe=tt==null?void 0:tt();_e.forEach(Pe=>{var pe;Xe==null||Xe.removeEventListener(Pe,Gt),Qe.current&&((pe=Pt.current)===null||pe===void 0||pe.removeEventListener(Pe,Qe.current))}),nt.cancel(),Gt.cancel()};n.useImperativeHandle(A,()=>({updatePosition:nt})),n.useEffect(()=>(dt.current=setTimeout(bt),()=>Et()),[]),n.useEffect(()=>(bt(),()=>Et()),[gt,Ve,Ue,ee,He]),n.useEffect(()=>{nt()},[gt,ee,He]);const[ht,Kt,rn]=U(Fe),_t=o()(qe,Kt,Fe,rn),qt=o()({[_t]:Ve});return ht(n.createElement(g.Z,{onResize:nt},n.createElement("div",Object.assign({style:L,className:xt,ref:Ze},j),Ve&&n.createElement("div",{style:wt,"aria-hidden":"true"}),n.createElement("div",{className:qt,ref:st,style:Ve},n.createElement(g.Z,{onResize:nt},Xt)))))})},85673:function(l,i,a){"use strict";a.d(i,{Z:function(){return gt}});var n=a(67294),h=a(93967),o=a.n(h),g=a(50344),x=a(64217),R=a(96159),O=a(53124),Z=a(13622),se=a(7743);const G=({children:c})=>{const{getPrefixCls:M}=n.useContext(O.E_),j=M("breadcrumb");return n.createElement("li",{className:`${j}-separator`,"aria-hidden":"true"},c===""?c:c||"/")};G.__ANT_BREADCRUMB_SEPARATOR=!0;var T=G,S=function(c,M){var j={};for(var y in c)Object.prototype.hasOwnProperty.call(c,y)&&M.indexOf(y)<0&&(j[y]=c[y]);if(c!=null&&typeof Object.getOwnPropertySymbols=="function")for(var B=0,y=Object.getOwnPropertySymbols(c);B<y.length;B++)M.indexOf(y[B])<0&&Object.prototype.propertyIsEnumerable.call(c,y[B])&&(j[y[B]]=c[y[B]]);return j};function U(c,M){if(c.title===void 0||c.title===null)return null;const j=Object.keys(M).join("|");return typeof c.title=="object"?c.title:String(c.title).replace(new RegExp(`:(${j})`,"g"),(y,B)=>M[B]||y)}function $(c,M,j,y){if(j==null)return null;const{className:B,onClick:Fe}=M,Ue=S(M,["className","onClick"]),et=Object.assign(Object.assign({},(0,x.Z)(Ue,{data:!0,aria:!0})),{onClick:Fe});return y!==void 0?n.createElement("a",Object.assign({},et,{className:o()(`${c}-link`,B),href:y}),j):n.createElement("span",Object.assign({},et,{className:o()(`${c}-link`,B)}),j)}function ce(c,M){return(y,B,Fe,Ue,et)=>{if(M)return M(y,B,Fe,Ue);const Ve=U(y,B);return $(c,y,Ve,et)}}var We=function(c,M){var j={};for(var y in c)Object.prototype.hasOwnProperty.call(c,y)&&M.indexOf(y)<0&&(j[y]=c[y]);if(c!=null&&typeof Object.getOwnPropertySymbols=="function")for(var B=0,y=Object.getOwnPropertySymbols(c);B<y.length;B++)M.indexOf(y[B])<0&&Object.prototype.propertyIsEnumerable.call(c,y[B])&&(j[y[B]]=c[y[B]]);return j};const _=c=>{const{prefixCls:M,separator:j="/",children:y,menu:B,overlay:Fe,dropdownProps:Ue,href:et}=c,Lt=(wt=>{if(B||Fe){const zt=Object.assign({},Ue);if(B){const St=B||{},{items:Pt}=St,Qe=We(St,["items"]);zt.menu=Object.assign(Object.assign({},Qe),{items:Pt==null?void 0:Pt.map((Ze,st)=>{var{key:dt,title:tt,label:Zt,path:$t}=Ze,ut=We(Ze,["key","title","label","path"]);let nt=Zt!=null?Zt:tt;return $t&&(nt=n.createElement("a",{href:`${et}${$t}`},nt)),Object.assign(Object.assign({},ut),{key:dt!=null?dt:st,label:nt})})})}else Fe&&(zt.overlay=Fe);return n.createElement(se.Z,Object.assign({placement:"bottom"},zt),n.createElement("span",{className:`${M}-overlay-link`},wt,n.createElement(Z.Z,null)))}return wt})(y);return Lt!=null?n.createElement(n.Fragment,null,n.createElement("li",null,Lt),j&&n.createElement(T,null,j)):null},_e=c=>{const{prefixCls:M,children:j,href:y}=c,B=We(c,["prefixCls","children","href"]),{getPrefixCls:Fe}=n.useContext(O.E_),Ue=Fe("breadcrumb",M);return n.createElement(_,Object.assign({},B,{prefixCls:Ue}),$(Ue,B,j,y))};_e.__ANT_BREADCRUMB_ITEM=!0;var W=_e,z=a(11568),d=a(14747),F=a(83559),ne=a(83262);const P=c=>{const{componentCls:M,iconCls:j,calc:y}=c;return{[M]:Object.assign(Object.assign({},(0,d.Wf)(c)),{color:c.itemColor,fontSize:c.fontSize,[j]:{fontSize:c.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:c.linkColor,transition:`color ${c.motionDurationMid}`,padding:`0 ${(0,z.bf)(c.paddingXXS)}`,borderRadius:c.borderRadiusSM,height:c.fontHeight,display:"inline-block",marginInline:y(c.marginXXS).mul(-1).equal(),"&:hover":{color:c.linkHoverColor,backgroundColor:c.colorBgTextHover}},(0,d.Qy)(c)),"li:last-child":{color:c.lastItemColor},[`${M}-separator`]:{marginInline:c.separatorMargin,color:c.separatorColor},[`${M}-link`]:{[`
          > ${j} + span,
          > ${j} + a
        `]:{marginInlineStart:c.marginXXS}},[`${M}-overlay-link`]:{borderRadius:c.borderRadiusSM,height:c.fontHeight,display:"inline-block",padding:`0 ${(0,z.bf)(c.paddingXXS)}`,marginInline:y(c.marginXXS).mul(-1).equal(),[`> ${j}`]:{marginInlineStart:c.marginXXS,fontSize:c.fontSizeIcon},"&:hover":{color:c.linkHoverColor,backgroundColor:c.colorBgTextHover,a:{color:c.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${c.componentCls}-rtl`]:{direction:"rtl"}})}},A=c=>({itemColor:c.colorTextDescription,lastItemColor:c.colorText,iconFontSize:c.fontSize,linkColor:c.colorTextDescription,linkHoverColor:c.colorText,separatorColor:c.colorTextDescription,separatorMargin:c.marginXS});var q=(0,F.I$)("Breadcrumb",c=>{const M=(0,ne.IX)(c,{});return P(M)},A),L=function(c,M){var j={};for(var y in c)Object.prototype.hasOwnProperty.call(c,y)&&M.indexOf(y)<0&&(j[y]=c[y]);if(c!=null&&typeof Object.getOwnPropertySymbols=="function")for(var B=0,y=Object.getOwnPropertySymbols(c);B<y.length;B++)M.indexOf(y[B])<0&&Object.prototype.propertyIsEnumerable.call(c,y[B])&&(j[y[B]]=c[y[B]]);return j};function ee(c){const{breadcrumbName:M,children:j}=c,y=L(c,["breadcrumbName","children"]),B=Object.assign({title:M},y);return j&&(B.menu={items:j.map(Fe=>{var{breadcrumbName:Ue}=Fe,et=L(Fe,["breadcrumbName"]);return Object.assign(Object.assign({},et),{title:Ue})})}),B}function He(c,M){return(0,n.useMemo)(()=>c||(M?M.map(ee):null),[c,M])}var Ct=function(c,M){var j={};for(var y in c)Object.prototype.hasOwnProperty.call(c,y)&&M.indexOf(y)<0&&(j[y]=c[y]);if(c!=null&&typeof Object.getOwnPropertySymbols=="function")for(var B=0,y=Object.getOwnPropertySymbols(c);B<y.length;B++)M.indexOf(y[B])<0&&Object.prototype.propertyIsEnumerable.call(c,y[B])&&(j[y[B]]=c[y[B]]);return j};const xt=(c,M)=>{if(M===void 0)return M;let j=(M||"").replace(/^\//,"");return Object.keys(c).forEach(y=>{j=j.replace(`:${y}`,c[y])}),j},qe=c=>{const{prefixCls:M,separator:j="/",style:y,className:B,rootClassName:Fe,routes:Ue,items:et,children:Ve,itemRender:Lt,params:wt={}}=c,zt=Ct(c,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:St,direction:Pt,breadcrumb:Qe}=n.useContext(O.E_);let Ze;const st=St("breadcrumb",M),[dt,tt,Zt]=q(st),$t=He(et,Ue),ut=ce(st,Lt);if($t&&$t.length>0){const bt=[],Et=et||Ue;Ze=$t.map((ht,Kt)=>{const{path:rn,key:_t,type:qt,menu:Xe,overlay:Pe,onClick:pe,className:H,separator:r,dropdownProps:f}=ht,E=xt(wt,rn);E!==void 0&&bt.push(E);const N=_t!=null?_t:Kt;if(qt==="separator")return n.createElement(T,{key:N},r);const re={},oe=Kt===$t.length-1;Xe?re.menu=Xe:Pe&&(re.overlay=Pe);let{href:ge}=ht;return bt.length&&E!==void 0&&(ge=`#/${bt.join("/")}`),n.createElement(_,Object.assign({key:N},re,(0,x.Z)(ht,{data:!0,aria:!0}),{className:H,dropdownProps:f,href:ge,separator:oe?"":j,onClick:pe,prefixCls:st}),ut(ht,wt,Et,bt,ge))})}else if(Ve){const bt=(0,g.Z)(Ve).length;Ze=(0,g.Z)(Ve).map((Et,ht)=>{if(!Et)return Et;const Kt=ht===bt-1;return(0,R.Tm)(Et,{separator:Kt?"":j,key:ht})})}const nt=o()(st,Qe==null?void 0:Qe.className,{[`${st}-rtl`]:Pt==="rtl"},B,Fe,tt,Zt),Gt=Object.assign(Object.assign({},Qe==null?void 0:Qe.style),y);return dt(n.createElement("nav",Object.assign({className:nt,style:Gt},zt),n.createElement("ol",null,Ze)))};qe.Item=W,qe.Separator=T;var Xt=qe,gt=Xt},11941:function(l,i,a){"use strict";a.d(i,{Z:function(){return Pn}});var n=a(67294),h=a(62208),o=a(35872),g=a(87462),x=a(42110),R=a(93771),O=function(t,s){return n.createElement(R.Z,(0,g.Z)({},t,{ref:s,icon:x.Z}))},Z=n.forwardRef(O),se=Z,G=a(93967),T=a.n(G),S=a(4942),U=a(1413),$=a(97685),ce=a(71002),We=a(91),_=a(21770),_e=a(31131),W=(0,n.createContext)(null),z=a(74902),d=a(9220),F=a(66680),ne=a(42550),P=a(75164),A=function(t){var s=t.activeTabOffset,u=t.horizontal,v=t.rtl,b=t.indicator,p=b===void 0?{}:b,m=p.size,I=p.align,w=I===void 0?"center":I,Y=(0,n.useState)(),V=(0,$.Z)(Y,2),ie=V[0],Se=V[1],Le=(0,n.useRef)(),ae=n.useCallback(function(X){return typeof m=="function"?m(X):typeof m=="number"?m:X},[m]);function ze(){P.Z.cancel(Le.current)}return(0,n.useEffect)(function(){var X={};if(s)if(u){X.width=ae(s.width);var k=v?"right":"left";w==="start"&&(X[k]=s[k]),w==="center"&&(X[k]=s[k]+s.width/2,X.transform=v?"translateX(50%)":"translateX(-50%)"),w==="end"&&(X[k]=s[k]+s.width,X.transform="translateX(-100%)")}else X.height=ae(s.height),w==="start"&&(X.top=s.top),w==="center"&&(X.top=s.top+s.height/2,X.transform="translateY(-50%)"),w==="end"&&(X.top=s.top+s.height,X.transform="translateY(-100%)");return ze(),Le.current=(0,P.Z)(function(){var fe=ie&&X&&Object.keys(X).every(function(Oe){var Me=X[Oe],Ge=ie[Oe];return typeof Me=="number"&&typeof Ge=="number"?Math.round(Me)===Math.round(Ge):Me===Ge});fe||Se(X)}),ze},[JSON.stringify(s),u,v,w,ae]),{style:ie}},q=A,L={width:0,height:0,left:0,top:0};function ee(e,t,s){return(0,n.useMemo)(function(){for(var u,v=new Map,b=t.get((u=e[0])===null||u===void 0?void 0:u.key)||L,p=b.left+b.width,m=0;m<e.length;m+=1){var I=e[m].key,w=t.get(I);if(!w){var Y;w=t.get((Y=e[m-1])===null||Y===void 0?void 0:Y.key)||L}var V=v.get(I)||(0,U.Z)({},w);V.right=p-V.left-V.width,v.set(I,V)}return v},[e.map(function(u){return u.key}).join("_"),t,s])}function He(e,t){var s=n.useRef(e),u=n.useState({}),v=(0,$.Z)(u,2),b=v[1];function p(m){var I=typeof m=="function"?m(s.current):m;I!==s.current&&t(I,s.current),s.current=I,b({})}return[s.current,p]}var Ct=.1,xt=.01,qe=20,Xt=Math.pow(.995,qe);function gt(e,t){var s=(0,n.useState)(),u=(0,$.Z)(s,2),v=u[0],b=u[1],p=(0,n.useState)(0),m=(0,$.Z)(p,2),I=m[0],w=m[1],Y=(0,n.useState)(0),V=(0,$.Z)(Y,2),ie=V[0],Se=V[1],Le=(0,n.useState)(),ae=(0,$.Z)(Le,2),ze=ae[0],X=ae[1],k=(0,n.useRef)();function fe(he){var Te=he.touches[0],K=Te.screenX,ve=Te.screenY;b({x:K,y:ve}),window.clearInterval(k.current)}function Oe(he){if(v){var Te=he.touches[0],K=Te.screenX,ve=Te.screenY;b({x:K,y:ve});var te=K-v.x,me=ve-v.y;t(te,me);var ot=Date.now();w(ot),Se(ot-I),X({x:te,y:me})}}function Me(){if(v&&(b(null),X(null),ze)){var he=ze.x/ie,Te=ze.y/ie,K=Math.abs(he),ve=Math.abs(Te);if(Math.max(K,ve)<Ct)return;var te=he,me=Te;k.current=window.setInterval(function(){if(Math.abs(te)<xt&&Math.abs(me)<xt){window.clearInterval(k.current);return}te*=Xt,me*=Xt,t(te*qe,me*qe)},qe)}}var Ge=(0,n.useRef)();function rt(he){var Te=he.deltaX,K=he.deltaY,ve=0,te=Math.abs(Te),me=Math.abs(K);te===me?ve=Ge.current==="x"?Te:K:te>me?(ve=Te,Ge.current="x"):(ve=K,Ge.current="y"),t(-ve,-ve)&&he.preventDefault()}var Ae=(0,n.useRef)(null);Ae.current={onTouchStart:fe,onTouchMove:Oe,onTouchEnd:Me,onWheel:rt},n.useEffect(function(){function he(te){Ae.current.onTouchStart(te)}function Te(te){Ae.current.onTouchMove(te)}function K(te){Ae.current.onTouchEnd(te)}function ve(te){Ae.current.onWheel(te)}return document.addEventListener("touchmove",Te,{passive:!1}),document.addEventListener("touchend",K,{passive:!0}),e.current.addEventListener("touchstart",he,{passive:!0}),e.current.addEventListener("wheel",ve,{passive:!1}),function(){document.removeEventListener("touchmove",Te),document.removeEventListener("touchend",K)}},[])}var c=a(8410);function M(e){var t=(0,n.useState)(0),s=(0,$.Z)(t,2),u=s[0],v=s[1],b=(0,n.useRef)(0),p=(0,n.useRef)();return p.current=e,(0,c.o)(function(){var m;(m=p.current)===null||m===void 0||m.call(p)},[u]),function(){b.current===u&&(b.current+=1,v(b.current))}}function j(e){var t=(0,n.useRef)([]),s=(0,n.useState)({}),u=(0,$.Z)(s,2),v=u[1],b=(0,n.useRef)(typeof e=="function"?e():e),p=M(function(){var I=b.current;t.current.forEach(function(w){I=w(I)}),t.current=[],b.current=I,v({})});function m(I){t.current.push(I),p()}return[b.current,m]}var y={width:0,height:0,left:0,top:0,right:0};function B(e,t,s,u,v,b,p){var m=p.tabs,I=p.tabPosition,w=p.rtl,Y,V,ie;return["top","bottom"].includes(I)?(Y="width",V=w?"right":"left",ie=Math.abs(s)):(Y="height",V="top",ie=-s),(0,n.useMemo)(function(){if(!m.length)return[0,0];for(var Se=m.length,Le=Se,ae=0;ae<Se;ae+=1){var ze=e.get(m[ae].key)||y;if(Math.floor(ze[V]+ze[Y])>Math.floor(ie+t)){Le=ae-1;break}}for(var X=0,k=Se-1;k>=0;k-=1){var fe=e.get(m[k].key)||y;if(fe[V]<ie){X=k+1;break}}return X>=Le?[0,0]:[X,Le]},[e,t,u,v,b,ie,I,m.map(function(Se){return Se.key}).join("_"),w])}function Fe(e){var t;return e instanceof Map?(t={},e.forEach(function(s,u){t[u]=s})):t=e,JSON.stringify(t)}var Ue="TABS_DQ";function et(e){return String(e).replace(/"/g,Ue)}function Ve(e,t,s,u){return!(!s||u||e===!1||e===void 0&&(t===!1||t===null))}var Lt=n.forwardRef(function(e,t){var s=e.prefixCls,u=e.editable,v=e.locale,b=e.style;return!u||u.showAdd===!1?null:n.createElement("button",{ref:t,type:"button",className:"".concat(s,"-nav-add"),style:b,"aria-label":(v==null?void 0:v.addAriaLabel)||"Add tab",onClick:function(m){u.onEdit("add",{event:m})}},u.addIcon||"+")}),wt=Lt,zt=n.forwardRef(function(e,t){var s=e.position,u=e.prefixCls,v=e.extra;if(!v)return null;var b,p={};return(0,ce.Z)(v)==="object"&&!n.isValidElement(v)?p=v:p.right=v,s==="right"&&(b=p.right),s==="left"&&(b=p.left),b?n.createElement("div",{className:"".concat(u,"-extra-content"),ref:t},b):null}),St=zt,Pt=a(29171),Qe=a(72512),Ze=a(15105),st=n.forwardRef(function(e,t){var s=e.prefixCls,u=e.id,v=e.tabs,b=e.locale,p=e.mobile,m=e.more,I=m===void 0?{}:m,w=e.style,Y=e.className,V=e.editable,ie=e.tabBarGutter,Se=e.rtl,Le=e.removeAriaLabel,ae=e.onTabClick,ze=e.getPopupContainer,X=e.popupClassName,k=(0,n.useState)(!1),fe=(0,$.Z)(k,2),Oe=fe[0],Me=fe[1],Ge=(0,n.useState)(null),rt=(0,$.Z)(Ge,2),Ae=rt[0],he=rt[1],Te=I.icon,K=Te===void 0?"More":Te,ve="".concat(u,"-more-popup"),te="".concat(s,"-dropdown"),me=Ae!==null?"".concat(ve,"-").concat(Ae):null,ot=b==null?void 0:b.dropdownAriaLabel;function it(ye,Ye){ye.preventDefault(),ye.stopPropagation(),V.onEdit("remove",{key:Ye,event:ye})}var vt=n.createElement(Qe.ZP,{onClick:function(Ye){var lt=Ye.key,ct=Ye.domEvent;ae(lt,ct),Me(!1)},prefixCls:"".concat(te,"-menu"),id:ve,tabIndex:-1,role:"listbox","aria-activedescendant":me,selectedKeys:[Ae],"aria-label":ot!==void 0?ot:"expanded dropdown"},v.map(function(ye){var Ye=ye.closable,lt=ye.disabled,ct=ye.closeIcon,yt=ye.key,Wt=ye.label,It=Ve(Ye,ct,V,lt);return n.createElement(Qe.sN,{key:yt,id:"".concat(ve,"-").concat(yt),role:"option","aria-controls":u&&"".concat(u,"-panel-").concat(yt),disabled:lt},n.createElement("span",null,Wt),It&&n.createElement("button",{type:"button","aria-label":Le||"remove",tabIndex:0,className:"".concat(te,"-menu-item-remove"),onClick:function(tn){tn.stopPropagation(),it(tn,yt)}},ct||V.removeIcon||"\xD7"))}));function pt(ye){for(var Ye=v.filter(function(It){return!It.disabled}),lt=Ye.findIndex(function(It){return It.key===Ae})||0,ct=Ye.length,yt=0;yt<ct;yt+=1){lt=(lt+ye+ct)%ct;var Wt=Ye[lt];if(!Wt.disabled){he(Wt.key);return}}}function le(ye){var Ye=ye.which;if(!Oe){[Ze.Z.DOWN,Ze.Z.SPACE,Ze.Z.ENTER].includes(Ye)&&(Me(!0),ye.preventDefault());return}switch(Ye){case Ze.Z.UP:pt(-1),ye.preventDefault();break;case Ze.Z.DOWN:pt(1),ye.preventDefault();break;case Ze.Z.ESC:Me(!1);break;case Ze.Z.SPACE:case Ze.Z.ENTER:Ae!==null&&ae(Ae,ye);break}}(0,n.useEffect)(function(){var ye=document.getElementById(me);ye&&ye.scrollIntoView&&ye.scrollIntoView(!1)},[Ae]),(0,n.useEffect)(function(){Oe||he(null)},[Oe]);var Dt=(0,S.Z)({},Se?"marginRight":"marginLeft",ie);v.length||(Dt.visibility="hidden",Dt.order=1);var Mt=T()((0,S.Z)({},"".concat(te,"-rtl"),Se)),mt=p?null:n.createElement(Pt.Z,(0,g.Z)({prefixCls:te,overlay:vt,visible:v.length?Oe:!1,onVisibleChange:Me,overlayClassName:T()(Mt,X),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:ze},I),n.createElement("button",{type:"button",className:"".concat(s,"-nav-more"),style:Dt,"aria-haspopup":"listbox","aria-controls":ve,id:"".concat(u,"-more"),"aria-expanded":Oe,onKeyDown:le},K));return n.createElement("div",{className:T()("".concat(s,"-nav-operations"),Y),style:w,ref:t},mt,n.createElement(wt,{prefixCls:s,locale:b,editable:V}))}),dt=n.memo(st,function(e,t){return t.tabMoving}),tt=function(t){var s=t.prefixCls,u=t.id,v=t.active,b=t.focus,p=t.tab,m=p.key,I=p.label,w=p.disabled,Y=p.closeIcon,V=p.icon,ie=t.closable,Se=t.renderWrapper,Le=t.removeAriaLabel,ae=t.editable,ze=t.onClick,X=t.onFocus,k=t.onBlur,fe=t.onKeyDown,Oe=t.onMouseDown,Me=t.onMouseUp,Ge=t.style,rt=t.tabCount,Ae=t.currentPosition,he="".concat(s,"-tab"),Te=Ve(ie,Y,ae,w);function K(it){w||ze(it)}function ve(it){it.preventDefault(),it.stopPropagation(),ae.onEdit("remove",{key:m,event:it})}var te=n.useMemo(function(){return V&&typeof I=="string"?n.createElement("span",null,I):I},[I,V]),me=n.useRef(null);n.useEffect(function(){b&&me.current&&me.current.focus()},[b]);var ot=n.createElement("div",{key:m,"data-node-key":et(m),className:T()(he,(0,S.Z)((0,S.Z)((0,S.Z)((0,S.Z)({},"".concat(he,"-with-remove"),Te),"".concat(he,"-active"),v),"".concat(he,"-disabled"),w),"".concat(he,"-focus"),b)),style:Ge,onClick:K},n.createElement("div",{ref:me,role:"tab","aria-selected":v,id:u&&"".concat(u,"-tab-").concat(m),className:"".concat(he,"-btn"),"aria-controls":u&&"".concat(u,"-panel-").concat(m),"aria-disabled":w,tabIndex:w?null:v?0:-1,onClick:function(vt){vt.stopPropagation(),K(vt)},onKeyDown:fe,onMouseDown:Oe,onMouseUp:Me,onFocus:X,onBlur:k},b&&n.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(Ae," of ").concat(rt)),V&&n.createElement("span",{className:"".concat(he,"-icon")},V),I&&te),Te&&n.createElement("button",{type:"button",role:"tab","aria-label":Le||"remove",tabIndex:v?0:-1,className:"".concat(he,"-remove"),onClick:function(vt){vt.stopPropagation(),ve(vt)}},Y||ae.removeIcon||"\xD7"));return Se?Se(ot):ot},Zt=tt,$t=function(t,s){var u=t.offsetWidth,v=t.offsetHeight,b=t.offsetTop,p=t.offsetLeft,m=t.getBoundingClientRect(),I=m.width,w=m.height,Y=m.left,V=m.top;return Math.abs(I-u)<1?[I,w,Y-s.left,V-s.top]:[u,v,p,b]},ut=function(t){var s=t.current||{},u=s.offsetWidth,v=u===void 0?0:u,b=s.offsetHeight,p=b===void 0?0:b;if(t.current){var m=t.current.getBoundingClientRect(),I=m.width,w=m.height;if(Math.abs(I-v)<1)return[I,w]}return[v,p]},nt=function(t,s){return t[s?0:1]},Gt=n.forwardRef(function(e,t){var s=e.className,u=e.style,v=e.id,b=e.animated,p=e.activeKey,m=e.rtl,I=e.extra,w=e.editable,Y=e.locale,V=e.tabPosition,ie=e.tabBarGutter,Se=e.children,Le=e.onTabClick,ae=e.onTabScroll,ze=e.indicator,X=n.useContext(W),k=X.prefixCls,fe=X.tabs,Oe=(0,n.useRef)(null),Me=(0,n.useRef)(null),Ge=(0,n.useRef)(null),rt=(0,n.useRef)(null),Ae=(0,n.useRef)(null),he=(0,n.useRef)(null),Te=(0,n.useRef)(null),K=V==="top"||V==="bottom",ve=He(0,function(Re,D){K&&ae&&ae({direction:Re>D?"left":"right"})}),te=(0,$.Z)(ve,2),me=te[0],ot=te[1],it=He(0,function(Re,D){!K&&ae&&ae({direction:Re>D?"top":"bottom"})}),vt=(0,$.Z)(it,2),pt=vt[0],le=vt[1],Dt=(0,n.useState)([0,0]),Mt=(0,$.Z)(Dt,2),mt=Mt[0],ye=Mt[1],Ye=(0,n.useState)([0,0]),lt=(0,$.Z)(Ye,2),ct=lt[0],yt=lt[1],Wt=(0,n.useState)([0,0]),It=(0,$.Z)(Wt,2),dn=It[0],tn=It[1],un=(0,n.useState)([0,0]),fn=(0,$.Z)(un,2),Ne=fn[0],Qt=fn[1],cn=j(new Map),Mn=(0,$.Z)(cn,2),Qn=Mn[0],Jn=Mn[1],yn=ee(fe,Qn,ct[0]),$n=nt(mt,K),bn=nt(ct,K),Tn=nt(dn,K),jn=nt(Ne,K),Bn=Math.floor($n)<Math.floor(bn+Tn),Vt=Bn?$n-jn:$n-Tn,_n="".concat(k,"-nav-operations-hidden"),nn=0,sn=0;K&&m?(nn=0,sn=Math.max(0,bn-Vt)):(nn=Math.min(0,Vt-bn),sn=0);function Rn(Re){return Re<nn?nn:Re>sn?sn:Re}var In=(0,n.useRef)(null),qn=(0,n.useState)(),Ln=(0,$.Z)(qn,2),Cn=Ln[0],zn=Ln[1];function wn(){zn(Date.now())}function Zn(){In.current&&clearTimeout(In.current)}gt(rt,function(Re,D){function je(Ke,Ft){Ke(function(jt){var mn=Rn(jt+Ft);return mn})}return Bn?(K?je(ot,Re):je(le,D),Zn(),wn(),!0):!1}),(0,n.useEffect)(function(){return Zn(),Cn&&(In.current=setTimeout(function(){zn(0)},100)),Zn},[Cn]);var ea=B(yn,Vt,K?me:pt,bn,Tn,jn,(0,U.Z)((0,U.Z)({},e),{},{tabs:fe})),An=(0,$.Z)(ea,2),ta=An[0],na=An[1],Hn=(0,F.Z)(function(){var Re=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p,D=yn.get(Re)||{width:0,height:0,left:0,right:0,top:0};if(K){var je=me;m?D.right<me?je=D.right:D.right+D.width>me+Vt&&(je=D.right+D.width-Vt):D.left<-me?je=-D.left:D.left+D.width>-me+Vt&&(je=-(D.left+D.width-Vt)),le(0),ot(Rn(je))}else{var Ke=pt;D.top<-pt?Ke=-D.top:D.top+D.height>-pt+Vt&&(Ke=-(D.top+D.height-Vt)),ot(0),le(Rn(Ke))}}),aa=(0,n.useState)(),Dn=(0,$.Z)(aa,2),an=Dn[0],hn=Dn[1],ra=(0,n.useState)(!1),Wn=(0,$.Z)(ra,2),oa=Wn[0],Fn=Wn[1],Jt=fe.filter(function(Re){return!Re.disabled}).map(function(Re){return Re.key}),vn=function(D){var je=Jt.indexOf(an||p),Ke=Jt.length,Ft=(je+D+Ke)%Ke,jt=Jt[Ft];hn(jt)},ia=function(D){var je=D.code,Ke=m&&K,Ft=Jt[0],jt=Jt[Jt.length-1];switch(je){case"ArrowLeft":{K&&vn(Ke?1:-1);break}case"ArrowRight":{K&&vn(Ke?-1:1);break}case"ArrowUp":{D.preventDefault(),K||vn(-1);break}case"ArrowDown":{D.preventDefault(),K||vn(1);break}case"Home":{D.preventDefault(),hn(Ft);break}case"End":{D.preventDefault(),hn(jt);break}case"Enter":case"Space":{D.preventDefault(),Le(an!=null?an:p,D);break}case"Backspace":case"Delete":{var mn=Jt.indexOf(an),Bt=fe.find(function(gn){return gn.key===an}),Nn=Ve(Bt==null?void 0:Bt.closable,Bt==null?void 0:Bt.closeIcon,w,Bt==null?void 0:Bt.disabled);Nn&&(D.preventDefault(),D.stopPropagation(),w.onEdit("remove",{key:an,event:D}),mn===Jt.length-1?vn(-1):vn(1));break}}},xn={};K?xn[m?"marginRight":"marginLeft"]=ie:xn.marginTop=ie;var Xn=fe.map(function(Re,D){var je=Re.key;return n.createElement(Zt,{id:v,prefixCls:k,key:je,tab:Re,style:D===0?void 0:xn,closable:Re.closable,editable:w,active:je===p,focus:je===an,renderWrapper:Se,removeAriaLabel:Y==null?void 0:Y.removeAriaLabel,tabCount:Jt.length,currentPosition:D+1,onClick:function(Ft){Le(je,Ft)},onKeyDown:ia,onFocus:function(){oa||hn(je),Hn(je),wn(),rt.current&&(m||(rt.current.scrollLeft=0),rt.current.scrollTop=0)},onBlur:function(){hn(void 0)},onMouseDown:function(){Fn(!0)},onMouseUp:function(){Fn(!1)}})}),Gn=function(){return Jn(function(){var D,je=new Map,Ke=(D=Ae.current)===null||D===void 0?void 0:D.getBoundingClientRect();return fe.forEach(function(Ft){var jt,mn=Ft.key,Bt=(jt=Ae.current)===null||jt===void 0?void 0:jt.querySelector('[data-node-key="'.concat(et(mn),'"]'));if(Bt){var Nn=$t(Bt,Ke),gn=(0,$.Z)(Nn,4),ua=gn[0],fa=gn[1],va=gn[2],ma=gn[3];je.set(mn,{width:ua,height:fa,left:va,top:ma})}}),je})};(0,n.useEffect)(function(){Gn()},[fe.map(function(Re){return Re.key}).join("_")]);var Sn=M(function(){var Re=ut(Oe),D=ut(Me),je=ut(Ge);ye([Re[0]-D[0]-je[0],Re[1]-D[1]-je[1]]);var Ke=ut(Te);tn(Ke);var Ft=ut(he);Qt(Ft);var jt=ut(Ae);yt([jt[0]-Ke[0],jt[1]-Ke[1]]),Gn()}),la=fe.slice(0,ta),ca=fe.slice(na+1),Kn=[].concat((0,z.Z)(la),(0,z.Z)(ca)),Un=yn.get(p),sa=q({activeTabOffset:Un,horizontal:K,indicator:ze,rtl:m}),da=sa.style;(0,n.useEffect)(function(){Hn()},[p,nn,sn,Fe(Un),Fe(yn),K]),(0,n.useEffect)(function(){Sn()},[m]);var Vn=!!Kn.length,pn="".concat(k,"-nav-wrap"),En,On,kn,Yn;return K?m?(On=me>0,En=me!==sn):(En=me<0,On=me!==nn):(kn=pt<0,Yn=pt!==nn),n.createElement(d.Z,{onResize:Sn},n.createElement("div",{ref:(0,ne.x1)(t,Oe),role:"tablist","aria-orientation":K?"horizontal":"vertical",className:T()("".concat(k,"-nav"),s),style:u,onKeyDown:function(){wn()}},n.createElement(St,{ref:Me,position:"left",extra:I,prefixCls:k}),n.createElement(d.Z,{onResize:Sn},n.createElement("div",{className:T()(pn,(0,S.Z)((0,S.Z)((0,S.Z)((0,S.Z)({},"".concat(pn,"-ping-left"),En),"".concat(pn,"-ping-right"),On),"".concat(pn,"-ping-top"),kn),"".concat(pn,"-ping-bottom"),Yn)),ref:rt},n.createElement(d.Z,{onResize:Sn},n.createElement("div",{ref:Ae,className:"".concat(k,"-nav-list"),style:{transform:"translate(".concat(me,"px, ").concat(pt,"px)"),transition:Cn?"none":void 0}},Xn,n.createElement(wt,{ref:Te,prefixCls:k,locale:Y,editable:w,style:(0,U.Z)((0,U.Z)({},Xn.length===0?void 0:xn),{},{visibility:Vn?"hidden":null})}),n.createElement("div",{className:T()("".concat(k,"-ink-bar"),(0,S.Z)({},"".concat(k,"-ink-bar-animated"),b.inkBar)),style:da}))))),n.createElement(dt,(0,g.Z)({},e,{removeAriaLabel:Y==null?void 0:Y.removeAriaLabel,ref:he,prefixCls:k,tabs:Kn,className:!Vn&&_n,tabMoving:!!Cn})),n.createElement(St,{ref:Ge,position:"right",extra:I,prefixCls:k})))}),bt=Gt,Et=n.forwardRef(function(e,t){var s=e.prefixCls,u=e.className,v=e.style,b=e.id,p=e.active,m=e.tabKey,I=e.children;return n.createElement("div",{id:b&&"".concat(b,"-panel-").concat(m),role:"tabpanel",tabIndex:p?0:-1,"aria-labelledby":b&&"".concat(b,"-tab-").concat(m),"aria-hidden":!p,style:v,className:T()(s,p&&"".concat(s,"-active"),u),ref:t},I)}),ht=Et,Kt=["renderTabBar"],rn=["label","key"],_t=function(t){var s=t.renderTabBar,u=(0,We.Z)(t,Kt),v=n.useContext(W),b=v.tabs;if(s){var p=(0,U.Z)((0,U.Z)({},u),{},{panes:b.map(function(m){var I=m.label,w=m.key,Y=(0,We.Z)(m,rn);return n.createElement(ht,(0,g.Z)({tab:I,key:w,tabKey:w},Y))})});return s(p,bt)}return n.createElement(bt,u)},qt=_t,Xe=a(29372),Pe=["key","forceRender","style","className","destroyInactiveTabPane"],pe=function(t){var s=t.id,u=t.activeKey,v=t.animated,b=t.tabPosition,p=t.destroyInactiveTabPane,m=n.useContext(W),I=m.prefixCls,w=m.tabs,Y=v.tabPane,V="".concat(I,"-tabpane");return n.createElement("div",{className:T()("".concat(I,"-content-holder"))},n.createElement("div",{className:T()("".concat(I,"-content"),"".concat(I,"-content-").concat(b),(0,S.Z)({},"".concat(I,"-content-animated"),Y))},w.map(function(ie){var Se=ie.key,Le=ie.forceRender,ae=ie.style,ze=ie.className,X=ie.destroyInactiveTabPane,k=(0,We.Z)(ie,Pe),fe=Se===u;return n.createElement(Xe.ZP,(0,g.Z)({key:Se,visible:fe,forceRender:Le,removeOnLeave:!!(p||X),leavedClassName:"".concat(V,"-hidden")},v.tabPaneMotion),function(Oe,Me){var Ge=Oe.style,rt=Oe.className;return n.createElement(ht,(0,g.Z)({},k,{prefixCls:V,id:s,tabKey:Se,animated:Y,active:fe,style:(0,U.Z)((0,U.Z)({},ae),Ge),className:T()(ze,rt),ref:Me}))})})))},H=pe,r=a(80334);function f(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{inkBar:!0,tabPane:!1},t;return e===!1?t={inkBar:!1,tabPane:!1}:e===!0?t={inkBar:!0,tabPane:!1}:t=(0,U.Z)({inkBar:!0},(0,ce.Z)(e)==="object"?e:{}),t.tabPaneMotion&&t.tabPane===void 0&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}var E=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],N=0,re=n.forwardRef(function(e,t){var s=e.id,u=e.prefixCls,v=u===void 0?"rc-tabs":u,b=e.className,p=e.items,m=e.direction,I=e.activeKey,w=e.defaultActiveKey,Y=e.editable,V=e.animated,ie=e.tabPosition,Se=ie===void 0?"top":ie,Le=e.tabBarGutter,ae=e.tabBarStyle,ze=e.tabBarExtraContent,X=e.locale,k=e.more,fe=e.destroyInactiveTabPane,Oe=e.renderTabBar,Me=e.onChange,Ge=e.onTabClick,rt=e.onTabScroll,Ae=e.getPopupContainer,he=e.popupClassName,Te=e.indicator,K=(0,We.Z)(e,E),ve=n.useMemo(function(){return(p||[]).filter(function(Ne){return Ne&&(0,ce.Z)(Ne)==="object"&&"key"in Ne})},[p]),te=m==="rtl",me=f(V),ot=(0,n.useState)(!1),it=(0,$.Z)(ot,2),vt=it[0],pt=it[1];(0,n.useEffect)(function(){pt((0,_e.Z)())},[]);var le=(0,_.Z)(function(){var Ne;return(Ne=ve[0])===null||Ne===void 0?void 0:Ne.key},{value:I,defaultValue:w}),Dt=(0,$.Z)(le,2),Mt=Dt[0],mt=Dt[1],ye=(0,n.useState)(function(){return ve.findIndex(function(Ne){return Ne.key===Mt})}),Ye=(0,$.Z)(ye,2),lt=Ye[0],ct=Ye[1];(0,n.useEffect)(function(){var Ne=ve.findIndex(function(cn){return cn.key===Mt});if(Ne===-1){var Qt;Ne=Math.max(0,Math.min(lt,ve.length-1)),mt((Qt=ve[Ne])===null||Qt===void 0?void 0:Qt.key)}ct(Ne)},[ve.map(function(Ne){return Ne.key}).join("_"),Mt,lt]);var yt=(0,_.Z)(null,{value:s}),Wt=(0,$.Z)(yt,2),It=Wt[0],dn=Wt[1];(0,n.useEffect)(function(){s||(dn("rc-tabs-".concat(N)),N+=1)},[]);function tn(Ne,Qt){Ge==null||Ge(Ne,Qt);var cn=Ne!==Mt;mt(Ne),cn&&(Me==null||Me(Ne))}var un={id:It,activeKey:Mt,animated:me,tabPosition:Se,rtl:te,mobile:vt},fn=(0,U.Z)((0,U.Z)({},un),{},{editable:Y,locale:X,more:k,tabBarGutter:Le,onTabClick:tn,onTabScroll:rt,extra:ze,style:ae,panes:null,getPopupContainer:Ae,popupClassName:he,indicator:Te});return n.createElement(W.Provider,{value:{tabs:ve,prefixCls:v}},n.createElement("div",(0,g.Z)({ref:t,id:s,className:T()(v,"".concat(v,"-").concat(Se),(0,S.Z)((0,S.Z)((0,S.Z)({},"".concat(v,"-mobile"),vt),"".concat(v,"-editable"),Y),"".concat(v,"-rtl"),te),b)},K),n.createElement(qt,(0,g.Z)({},fn,{renderTabBar:Oe})),n.createElement(H,(0,g.Z)({destroyInactiveTabPane:fe},un,{animated:me}))))}),oe=re,ge=oe,Be=a(53124),de=a(35792),Ce=a(98675),be=a(33603);const Ee={motionAppear:!1,motionEnter:!0,motionLeave:!0};function Ie(e,t={inkBar:!0,tabPane:!1}){let s;return t===!1?s={inkBar:!1,tabPane:!1}:t===!0?s={inkBar:!0,tabPane:!0}:s=Object.assign({inkBar:!0},typeof t=="object"?t:{}),s.tabPane&&(s.tabPaneMotion=Object.assign(Object.assign({},Ee),{motionName:(0,be.m)(e,"switch")})),s}var ue=a(50344),Q=function(e,t){var s={};for(var u in e)Object.prototype.hasOwnProperty.call(e,u)&&t.indexOf(u)<0&&(s[u]=e[u]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var v=0,u=Object.getOwnPropertySymbols(e);v<u.length;v++)t.indexOf(u[v])<0&&Object.prototype.propertyIsEnumerable.call(e,u[v])&&(s[u[v]]=e[u[v]]);return s};function $e(e){return e.filter(t=>t)}function J(e,t){if(e)return e.map(u=>{var v;const b=(v=u.destroyOnHidden)!==null&&v!==void 0?v:u.destroyInactiveTabPane;return Object.assign(Object.assign({},u),{destroyInactiveTabPane:b})});const s=(0,ue.Z)(t).map(u=>{if(n.isValidElement(u)){const{key:v,props:b}=u,p=b||{},{tab:m}=p,I=Q(p,["tab"]);return Object.assign(Object.assign({key:String(v)},I),{label:m})}return null});return $e(s)}var C=a(11568),we=a(14747),ke=a(83559),Tt=a(83262),xe=a(67771),At=e=>{const{componentCls:t,motionDurationSlow:s}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${s}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${s}`}}}}},[(0,xe.oN)(e,"slide-up"),(0,xe.oN)(e,"slide-down")]]};const at=e=>{const{componentCls:t,tabsCardPadding:s,cardBg:u,cardGutter:v,colorBorderSecondary:b,itemSelectedColor:p}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:s,background:u,border:`${(0,C.bf)(e.lineWidth)} ${e.lineType} ${b}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:p,background:e.colorBgContainer},[`${t}-tab-focus:has(${t}-tab-btn:focus-visible)`]:(0,we.oN)(e,-3),[`& ${t}-tab${t}-tab-focus ${t}-tab-btn:focus-visible`]:{outline:"none"},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:(0,C.bf)(v)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${(0,C.bf)(e.borderRadiusLG)} ${(0,C.bf)(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${(0,C.bf)(e.borderRadiusLG)} ${(0,C.bf)(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:(0,C.bf)(v)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,C.bf)(e.borderRadiusLG)} 0 0 ${(0,C.bf)(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,C.bf)(e.borderRadiusLG)} ${(0,C.bf)(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},ft=e=>{const{componentCls:t,itemHoverColor:s,dropdownEdgeChildVerticalPadding:u}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},(0,we.Wf)(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${(0,C.bf)(u)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},we.vS),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${(0,C.bf)(e.paddingXXS)} ${(0,C.bf)(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:s}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},Ot=e=>{const{componentCls:t,margin:s,colorBorderSecondary:u,horizontalMargin:v,verticalItemPadding:b,verticalItemMargin:p,calc:m}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:v,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${(0,C.bf)(e.lineWidth)} ${e.lineType} ${u}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:s,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:m(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:b,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:p},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:(0,C.bf)(m(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${(0,C.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:m(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${(0,C.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},en=e=>{const{componentCls:t,cardPaddingSM:s,cardPaddingLG:u,cardHeightSM:v,cardHeightLG:b,horizontalItemPaddingSM:p,horizontalItemPaddingLG:m}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:p,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:m,fontSize:e.titleFontSizeLG,lineHeight:e.lineHeightLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:s},[`${t}-nav-add`]:{minWidth:v,minHeight:v}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${(0,C.bf)(e.borderRadius)} ${(0,C.bf)(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${(0,C.bf)(e.borderRadius)} ${(0,C.bf)(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,C.bf)(e.borderRadius)} ${(0,C.bf)(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,C.bf)(e.borderRadius)} 0 0 ${(0,C.bf)(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:u},[`${t}-nav-add`]:{minWidth:b,minHeight:b}}}}}},Nt=e=>{const{componentCls:t,itemActiveColor:s,itemHoverColor:u,iconCls:v,tabsHorizontalItemMargin:b,horizontalItemPadding:p,itemSelectedColor:m,itemColor:I}=e,w=`${t}-tab`;return{[w]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:p,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:I,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:s}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${w}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},(0,we.Qy)(e)),"&:hover":{color:u},[`&${w}-active ${w}-btn`]:{color:m,textShadow:e.tabsActiveTextShadow},[`&${w}-focus ${w}-btn:focus-visible`]:(0,we.oN)(e),[`&${w}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${w}-disabled ${w}-btn, &${w}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${w}-remove ${v}`]:{margin:0},[`${v}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${w} + ${w}`]:{margin:{_skip_check_:!0,value:b}}}},Ht=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:s,iconCls:u,cardGutter:v,calc:b}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:s},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[u]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,C.bf)(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:(0,C.bf)(e.marginXS)},marginLeft:{_skip_check_:!0,value:(0,C.bf)(b(e.marginXXS).mul(-1).equal())},[u]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:v},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},kt=e=>{const{componentCls:t,tabsCardPadding:s,cardHeight:u,cardGutter:v,itemHoverColor:b,itemActiveColor:p,colorBorderSecondary:m}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,we.Wf)(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:s,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:u,minHeight:u,marginLeft:{_skip_check_:!0,value:v},background:"transparent",border:`${(0,C.bf)(e.lineWidth)} ${e.lineType} ${m}`,borderRadius:`${(0,C.bf)(e.borderRadiusLG)} ${(0,C.bf)(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:b},"&:active, &:focus:not(:focus-visible)":{color:p}},(0,we.Qy)(e,-3))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),Nt(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:Object.assign(Object.assign({},(0,we.Qy)(e)),{"&-hidden":{display:"none"}})}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping']) > ${t}-nav-list`]:{margin:"auto"}}}}}},Ut=e=>{const{cardHeight:t,cardHeightSM:s,cardHeightLG:u,controlHeight:v,controlHeightLG:b}=e,p=t||b,m=s||v,I=u||b+8;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:p,cardHeightSM:m,cardHeightLG:I,cardPadding:`${(p-e.fontHeight)/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${(m-e.fontHeight)/2-e.lineWidth}px ${e.paddingXS}px`,cardPaddingLG:`${(I-e.fontHeightLG)/2-e.lineWidth}px ${e.padding}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}};var Yt=(0,ke.I$)("Tabs",e=>{const t=(0,Tt.IX)(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${(0,C.bf)(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${(0,C.bf)(e.horizontalItemGutter)}`});return[en(t),Ht(t),Ot(t),ft(t),at(t),kt(t),At(t)]},Ut),Rt=()=>null,on=function(e,t){var s={};for(var u in e)Object.prototype.hasOwnProperty.call(e,u)&&t.indexOf(u)<0&&(s[u]=e[u]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var v=0,u=Object.getOwnPropertySymbols(e);v<u.length;v++)t.indexOf(u[v])<0&&Object.prototype.propertyIsEnumerable.call(e,u[v])&&(s[u[v]]=e[u[v]]);return s};const ln=e=>{var t,s,u,v,b,p,m,I,w,Y,V;const{type:ie,className:Se,rootClassName:Le,size:ae,onEdit:ze,hideAdd:X,centered:k,addIcon:fe,removeIcon:Oe,moreIcon:Me,more:Ge,popupClassName:rt,children:Ae,items:he,animated:Te,style:K,indicatorSize:ve,indicator:te,destroyInactiveTabPane:me,destroyOnHidden:ot}=e,it=on(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:vt}=it,{direction:pt,tabs:le,getPrefixCls:Dt,getPopupContainer:Mt}=n.useContext(Be.E_),mt=Dt("tabs",vt),ye=(0,de.Z)(mt),[Ye,lt,ct]=Yt(mt,ye);let yt;ie==="editable-card"&&(yt={onEdit:(Ne,{key:Qt,event:cn})=>{ze==null||ze(Ne==="add"?cn:Qt,Ne)},removeIcon:(t=Oe!=null?Oe:le==null?void 0:le.removeIcon)!==null&&t!==void 0?t:n.createElement(h.Z,null),addIcon:(fe!=null?fe:le==null?void 0:le.addIcon)||n.createElement(se,null),showAdd:X!==!0});const Wt=Dt(),It=(0,Ce.Z)(ae),dn=J(he,Ae),tn=Ie(mt,Te),un=Object.assign(Object.assign({},le==null?void 0:le.style),K),fn={align:(s=te==null?void 0:te.align)!==null&&s!==void 0?s:(u=le==null?void 0:le.indicator)===null||u===void 0?void 0:u.align,size:(m=(b=(v=te==null?void 0:te.size)!==null&&v!==void 0?v:ve)!==null&&b!==void 0?b:(p=le==null?void 0:le.indicator)===null||p===void 0?void 0:p.size)!==null&&m!==void 0?m:le==null?void 0:le.indicatorSize};return Ye(n.createElement(ge,Object.assign({direction:pt,getPopupContainer:Mt},it,{items:dn,className:T()({[`${mt}-${It}`]:It,[`${mt}-card`]:["card","editable-card"].includes(ie),[`${mt}-editable-card`]:ie==="editable-card",[`${mt}-centered`]:k},le==null?void 0:le.className,Se,Le,lt,ct,ye),popupClassName:T()(rt,lt,ct,ye),style:un,editable:yt,more:Object.assign({icon:(V=(Y=(w=(I=le==null?void 0:le.more)===null||I===void 0?void 0:I.icon)!==null&&w!==void 0?w:le==null?void 0:le.moreIcon)!==null&&Y!==void 0?Y:Me)!==null&&V!==void 0?V:n.createElement(o.Z,null),transitionName:`${Wt}-slide-up`},Ge),prefixCls:mt,animated:tn,indicator:fn,destroyInactiveTabPane:ot!=null?ot:me})))};ln.TabPane=Rt;var Pn=ln},19158:function(l,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=a;function a(){return!!(typeof window!="undefined"&&window.document&&window.document.createElement)}},32191:function(l,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=a;function a(n,h){if(!n)return!1;if(n.contains)return n.contains(h);for(var o=h;o;){if(o===n)return!0;o=o.parentNode}return!1}},93399:function(l,i,a){"use strict";var n=a(64836).default;Object.defineProperty(i,"__esModule",{value:!0}),i.clearContainerCache=_,i.injectCSS=U,i.removeCSS=ce,i.updateCSS=_e;var h=n(a(42122)),o=n(a(19158)),g=n(a(32191)),x="data-rc-order",R="data-rc-priority",O="rc-util-key",Z=new Map;function se(){var W=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},z=W.mark;return z?z.startsWith("data-")?z:"data-".concat(z):O}function G(W){if(W.attachTo)return W.attachTo;var z=document.querySelector("head");return z||document.body}function T(W){return W==="queue"?"prependQueue":W?"prepend":"append"}function S(W){return Array.from((Z.get(W)||W).children).filter(function(z){return z.tagName==="STYLE"})}function U(W){var z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!(0,o.default)())return null;var d=z.csp,F=z.prepend,ne=z.priority,P=ne===void 0?0:ne,A=T(F),q=A==="prependQueue",L=document.createElement("style");L.setAttribute(x,A),q&&P&&L.setAttribute(R,"".concat(P)),d!=null&&d.nonce&&(L.nonce=d==null?void 0:d.nonce),L.innerHTML=W;var ee=G(z),He=ee.firstChild;if(F){if(q){var Ct=(z.styles||S(ee)).filter(function(xt){if(!["prepend","prependQueue"].includes(xt.getAttribute(x)))return!1;var qe=Number(xt.getAttribute(R)||0);return P>=qe});if(Ct.length)return ee.insertBefore(L,Ct[Ct.length-1].nextSibling),L}ee.insertBefore(L,He)}else ee.appendChild(L);return L}function $(W){var z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},d=G(z);return(z.styles||S(d)).find(function(F){return F.getAttribute(se(z))===W})}function ce(W){var z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},d=$(W,z);if(d){var F=G(z);F.removeChild(d)}}function We(W,z){var d=Z.get(W);if(!d||!(0,g.default)(document,d)){var F=U("",z),ne=F.parentNode;Z.set(W,ne),W.removeChild(F)}}function _(){Z.clear()}function _e(W,z){var d=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},F=G(d),ne=S(F),P=(0,h.default)((0,h.default)({},d),{},{styles:ne});We(F,P);var A=$(z,P);if(A){var q,L;if((q=P.csp)!==null&&q!==void 0&&q.nonce&&A.nonce!==((L=P.csp)===null||L===void 0?void 0:L.nonce)){var ee;A.nonce=(ee=P.csp)===null||ee===void 0?void 0:ee.nonce}return A.innerHTML!==W&&(A.innerHTML=W),A}var He=U(W,P);return He.setAttribute(se(P),z),He}},63298:function(l,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.getShadowRoot=h,i.inShadow=n;function a(o){var g;return o==null||(g=o.getRootNode)===null||g===void 0?void 0:g.call(o)}function n(o){return a(o)instanceof ShadowRoot}function h(o){return n(o)?a(o):null}},45520:function(l,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.call=R,i.default=void 0,i.note=g,i.noteOnce=Z,i.preMessage=void 0,i.resetWarned=x,i.warning=o,i.warningOnce=O;var a={},n=[],h=i.preMessage=function(T){n.push(T)};function o(G,T){if(0)var S}function g(G,T){if(0)var S}function x(){a={}}function R(G,T,S){!T&&!a[S]&&(G(!1,S),a[S]=!0)}function O(G,T){R(o,G,T)}function Z(G,T){R(g,G,T)}O.preMessage=h,O.resetWarned=x,O.noteOnce=Z;var se=i.default=O},73897:function(l){function i(a,n){(n==null||n>a.length)&&(n=a.length);for(var h=0,o=Array(n);h<n;h++)o[h]=a[h];return o}l.exports=i,l.exports.__esModule=!0,l.exports.default=l.exports},85372:function(l){function i(a){if(Array.isArray(a))return a}l.exports=i,l.exports.__esModule=!0,l.exports.default=l.exports},38416:function(l,i,a){var n=a(64062);function h(o,g,x){return(g=n(g))in o?Object.defineProperty(o,g,{value:x,enumerable:!0,configurable:!0,writable:!0}):o[g]=x,o}l.exports=h,l.exports.__esModule=!0,l.exports.default=l.exports},10434:function(l){function i(){return l.exports=i=Object.assign?Object.assign.bind():function(a){for(var n=1;n<arguments.length;n++){var h=arguments[n];for(var o in h)({}).hasOwnProperty.call(h,o)&&(a[o]=h[o])}return a},l.exports.__esModule=!0,l.exports.default=l.exports,i.apply(null,arguments)}l.exports=i,l.exports.__esModule=!0,l.exports.default=l.exports},64836:function(l){function i(a){return a&&a.__esModule?a:{default:a}}l.exports=i,l.exports.__esModule=!0,l.exports.default=l.exports},75263:function(l,i,a){var n=a(18698).default;function h(o,g){if(typeof WeakMap=="function")var x=new WeakMap,R=new WeakMap;return(l.exports=h=function(Z,se){if(!se&&Z&&Z.__esModule)return Z;var G,T,S={__proto__:null,default:Z};if(Z===null||n(Z)!="object"&&typeof Z!="function")return S;if(G=se?R:x){if(G.has(Z))return G.get(Z);G.set(Z,S)}for(var U in Z)U!=="default"&&{}.hasOwnProperty.call(Z,U)&&((T=(G=Object.defineProperty)&&Object.getOwnPropertyDescriptor(Z,U))&&(T.get||T.set)?G(S,U,T):S[U]=Z[U]);return S},l.exports.__esModule=!0,l.exports.default=l.exports)(o,g)}l.exports=h,l.exports.__esModule=!0,l.exports.default=l.exports},68872:function(l){function i(a,n){var h=a==null?null:typeof Symbol!="undefined"&&a[Symbol.iterator]||a["@@iterator"];if(h!=null){var o,g,x,R,O=[],Z=!0,se=!1;try{if(x=(h=h.call(a)).next,n===0){if(Object(h)!==h)return;Z=!1}else for(;!(Z=(o=x.call(h)).done)&&(O.push(o.value),O.length!==n);Z=!0);}catch(G){se=!0,g=G}finally{try{if(!Z&&h.return!=null&&(R=h.return(),Object(R)!==R))return}finally{if(se)throw g}}return O}}l.exports=i,l.exports.__esModule=!0,l.exports.default=l.exports},12218:function(l){function i(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}l.exports=i,l.exports.__esModule=!0,l.exports.default=l.exports},42122:function(l,i,a){var n=a(38416);function h(g,x){var R=Object.keys(g);if(Object.getOwnPropertySymbols){var O=Object.getOwnPropertySymbols(g);x&&(O=O.filter(function(Z){return Object.getOwnPropertyDescriptor(g,Z).enumerable})),R.push.apply(R,O)}return R}function o(g){for(var x=1;x<arguments.length;x++){var R=arguments[x]!=null?arguments[x]:{};x%2?h(Object(R),!0).forEach(function(O){n(g,O,R[O])}):Object.getOwnPropertyDescriptors?Object.defineProperties(g,Object.getOwnPropertyDescriptors(R)):h(Object(R)).forEach(function(O){Object.defineProperty(g,O,Object.getOwnPropertyDescriptor(R,O))})}return g}l.exports=o,l.exports.__esModule=!0,l.exports.default=l.exports},70215:function(l,i,a){var n=a(7071);function h(o,g){if(o==null)return{};var x,R,O=n(o,g);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(o);for(R=0;R<Z.length;R++)x=Z[R],g.indexOf(x)===-1&&{}.propertyIsEnumerable.call(o,x)&&(O[x]=o[x])}return O}l.exports=h,l.exports.__esModule=!0,l.exports.default=l.exports},7071:function(l){function i(a,n){if(a==null)return{};var h={};for(var o in a)if({}.hasOwnProperty.call(a,o)){if(n.indexOf(o)!==-1)continue;h[o]=a[o]}return h}l.exports=i,l.exports.__esModule=!0,l.exports.default=l.exports},27424:function(l,i,a){var n=a(85372),h=a(68872),o=a(86116),g=a(12218);function x(R,O){return n(R)||h(R,O)||o(R,O)||g()}l.exports=x,l.exports.__esModule=!0,l.exports.default=l.exports},95036:function(l,i,a){var n=a(18698).default;function h(o,g){if(n(o)!="object"||!o)return o;var x=o[Symbol.toPrimitive];if(x!==void 0){var R=x.call(o,g||"default");if(n(R)!="object")return R;throw new TypeError("@@toPrimitive must return a primitive value.")}return(g==="string"?String:Number)(o)}l.exports=h,l.exports.__esModule=!0,l.exports.default=l.exports},64062:function(l,i,a){var n=a(18698).default,h=a(95036);function o(g){var x=h(g,"string");return n(x)=="symbol"?x:x+""}l.exports=o,l.exports.__esModule=!0,l.exports.default=l.exports},18698:function(l){function i(a){"@babel/helpers - typeof";return l.exports=i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},l.exports.__esModule=!0,l.exports.default=l.exports,i(a)}l.exports=i,l.exports.__esModule=!0,l.exports.default=l.exports},86116:function(l,i,a){var n=a(73897);function h(o,g){if(o){if(typeof o=="string")return n(o,g);var x={}.toString.call(o).slice(8,-1);return x==="Object"&&o.constructor&&(x=o.constructor.name),x==="Map"||x==="Set"?Array.from(o):x==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(x)?n(o,g):void 0}}l.exports=h,l.exports.__esModule=!0,l.exports.default=l.exports}}]);
