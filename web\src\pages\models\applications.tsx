import React from 'react';
import { Card, Typography } from 'antd';

const { Title, Text } = Typography;

const Applications: React.FC = () => {
  return (
    <div style={{ 
      padding: '24px',
      background: '#f5f5f5',
      minHeight: '100vh',
    }}>
      <Card 
        style={{ 
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}
        bodyStyle={{ padding: '24px' }}
      >
        <Title level={2} style={{ margin: 0, color: '#262626' }}>
          Applications
        </Title>
        <Text type="secondary" style={{ fontSize: '16px', marginTop: '8px', display: 'block' }}>
          Manage and deploy your model applications.
        </Text>
        
        <div style={{ marginTop: '32px' }}>
          <Text>Applications content will be implemented here.</Text>
        </div>
      </Card>
    </div>
  );
};

export default Applications;
