"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9366],{31736:function(S,t,n){n.r(t),n.d(t,{default:function(){return j}});var c=n(15009),l=n.n(c),d=n(99289),v=n.n(d),h=n(10915),s=n(84226),g=n(2453),f=n(68744),a=n(85893),m=function(){var P=(0,s.useIntl)(),i=(0,s.useModel)("@@initialState"),p=i.initialState,C=i.setInitialState,y=function(){var F=v()(l()().mark(function o(){var u,r;return l()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,u=new URLSearchParams(window.location.search),r=u.get("redirect")||"/Console/projects",console.log("\u767B\u5F55\u540E\u5C06\u91CD\u5B9A\u5411\u5230:",r),e.next=6,f.e.login(r);case 6:e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Keycloak\u767B\u5F55\u5931\u8D25:",e.t0),g.ZP.error("Keycloak\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5\uFF01");case 12:case"end":return e.stop()}},o,null,[[0,8]])}));return function(){return F.apply(this,arguments)}}();return(0,a.jsxs)("div",{className:"login-container",children:[(0,a.jsx)("div",{className:"login-content-left"}),(0,a.jsx)("div",{className:"login-content-right",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{children:"\u82AF\u5408\u8DE8\u67B6\u6784\u7CFB\u7EDF-DeepSeek\u4E13\u4EAB\u5957\u4EF6"}),(0,a.jsx)("button",{onClick:y,className:"login-button",children:"\u4F7F\u7528 Keycloak \u767B\u5F55"})]})})]})},j=function(){return(0,a.jsx)(h._Y,{dark:!0,children:(0,a.jsx)(m,{})})}}}]);
