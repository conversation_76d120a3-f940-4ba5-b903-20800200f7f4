import { ProLayoutProps } from '@ant-design/pro-components';

/**
 * @name
 */
const Settings: ProLayoutProps & {
  pwa?: boolean;
  logo?: string;
} =
{
  "navTheme": "light",
  "layout": "side",
  "contentWidth": "Fluid",
  "fixedHeader": false,
  "fixSiderbar": true,
  "pwa": true,
  title:'芯合跨架构智算软件工厂',

  // "token": {
  //colorSplit:#393B3C;
  //   "header": {
  //     "heightLayoutHeader":64,
  //   },
  //   "sider": {
  //     colorMenuBackground:'#0c0e11',
  //     "colorTextMenuTitle": "#E6E6E9",
  //     "colorTextMenu": "#C7C8C7",
  //     "colorTextMenuSelected": "#C7C7CB",
  //     "colorTextMenuActive": "#C7C7CB",
  //     "colorTextMenuItemHover": "#C7C7CB",
  //     "colorBgMenuItemHover": "#2F3133",
  //     "colorBgMenuItemSelected": "#87A9FF",
  //     "colorBgCollapsedButton": "#2F3133",
  //     "colorMenuItemDivider":"#393B3C",
  //     colorTextMenuSecondary:'#C7C8C7',
  //     colorBgMenuItemCollapsedElevated:'#1a1c1e',
  //     colorTextCollapsedButton:'#C7C8C7',
  //     colorTextCollapsedButtonHover:'C7C8C7',
  //   },
  //   "pageContainer": {
  //     colorBgPageContainer: "#1a1c1e"//背景底色
  //   },

  // },
  // "colorPrimary": "#2f3133",//选中项

}


export default Settings;
