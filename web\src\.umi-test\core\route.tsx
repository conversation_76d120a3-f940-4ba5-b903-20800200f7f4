// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/user","layout":false,"id":"1"},"2":{"name":"login","path":"/user/login","parentId":"1","id":"2"},"3":{"name":"register","path":"/user/register","parentId":"1","id":"3"},"4":{"name":"register-result","path":"/user/register-result","parentId":"1","id":"4"},"5":{"path":"/auth/callback","layout":false,"id":"5"},"6":{"path":"/auth/process-callback","layout":false,"id":"6"},"7":{"path":"/auth/logout-callback","layout":false,"id":"7"},"8":{"path":"/Console","name":"console","icon":"ConsoleSqlOutlined","parentId":"ant-design-pro-layout","id":"8"},"9":{"path":"/Console/projects","name":"projects","parentId":"8","id":"9"},"10":{"path":"/Console/xpu","name":"xpu","parentId":"8","id":"10"},"11":{"path":"/Console/plan","name":"plan","parentId":"8","id":"11"},"12":{"path":"/Console/plan/milestoneDetail","name":"里程碑详情","hideInMenu":true,"parentId":"11","id":"12"},"13":{"path":"/Console/plan/newMilestone","name":"新建里程碑","hideInMenu":true,"parentId":"11","id":"13"},"14":{"path":"/Console/plan/pipelineDetail","name":"流水线详情","hideInMenu":true,"parentId":"11","id":"14"},"15":{"path":"/repository","name":"repository","icon":"ProjectOutlined","parentId":"ant-design-pro-layout","id":"15"},"16":{"path":"/models","name":"models","icon":"DatabaseOutlined","parentId":"ant-design-pro-layout","id":"16"},"17":{"path":"/models","parentId":"16","id":"17"},"18":{"path":"/models/overview","parentId":"16","id":"18"},"19":{"path":"/models/applications","parentId":"16","id":"19"},"20":{"path":"/models/datasets","parentId":"16","id":"20"},"21":{"path":"/models/resources","parentId":"16","id":"21"},"22":{"path":"/tools","name":"tools","icon":"ToolOutlined","parentId":"ant-design-pro-layout","id":"22"},"23":{"path":"/tools/modeling","name":"modeling","parentId":"22","id":"23"},"24":{"path":"/tools/s2s","name":"s2s","parentId":"22","id":"24"},"25":{"path":"/tools/ide","name":"ide","parentId":"22","id":"25"},"26":{"path":"/tools/repo","name":"repo","parentId":"22","id":"26"},"27":{"path":"/tools/repo","parentId":"26","id":"27"},"28":{"path":"/tools/repo/newProject","parentId":"26","id":"28"},"29":{"path":"/tools/repo/branch","parentId":"26","id":"29"},"30":{"path":"/tools/repo/commits","parentId":"26","id":"30"},"31":{"path":"/tools/repo/compare","parentId":"26","id":"31"},"32":{"path":"/tools/repo/files","parentId":"26","id":"32"},"33":{"path":"/tools/repo/files/newFile","parentId":"26","id":"33"},"34":{"path":"/tools/repo/newTag","parentId":"26","id":"34"},"35":{"path":"/tools/repo/compare/compare_result","parentId":"26","id":"35"},"36":{"path":"/tools/repo/tags","parentId":"26","id":"36"},"37":{"path":"/tools/repo/commits/newMergeRequest","parentId":"26","id":"37"},"38":{"path":"/tools/repo/mergeRequests","parentId":"26","id":"38"},"39":{"path":"/tools/repo/newMergeRequest","parentId":"26","id":"39"},"40":{"path":"/tools/repo/settings","parentId":"26","id":"40"},"41":{"path":"/tools/build","name":"build","parentId":"22","id":"41"},"42":{"path":"/tools/file","name":"file","parentId":"22","id":"42"},"43":{"path":"/ci","icon":"LineChartOutlined","name":"ci","parentId":"ant-design-pro-layout","id":"43"},"44":{"path":"/deploy","name":"deploy","icon":"DeploymentUnitOutlined","parentId":"ant-design-pro-layout","id":"44"},"45":{"path":"/deploy/resource","name":"resource","parentId":"44","id":"45"},"46":{"path":"/deploy/plan","name":"plan","parentId":"44","id":"46"},"47":{"path":"/deploy/tools","name":"tools","parentId":"44","id":"47"},"48":{"path":"/deploy/migration","name":"migration","parentId":"44","id":"48"},"49":{"path":"/deploy/tasks","name":"tasks","parentId":"44","id":"49"},"50":{"path":"/appstore","name":"appstore","icon":"AppstoreOutlined","parentId":"ant-design-pro-layout","id":"50"},"51":{"path":"/help","name":"help","icon":"DesktopOutlined","parentId":"ant-design-pro-layout","id":"51"},"52":{"path":"/","redirect":"/Console/projects","parentId":"ant-design-pro-layout","id":"52"},"53":{"path":"*","layout":false,"id":"53"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': require('./EmptyRoute').default,
'2': require('@/pages/User/Login/index.tsx').default,
'3': require('@/pages/User/Register/index.tsx').default,
'4': require('@/pages/User/register-result/index.tsx').default,
'5': require('@/pages/auth/callback.tsx').default,
'6': require('@/pages/auth/process-callback.tsx').default,
'7': require('@/pages/auth/logout-callback.tsx').default,
'8': require('./EmptyRoute').default,
'9': require('@/pages/Console/projects.tsx').default,
'10': require('@/pages/Console/xpu.tsx').default,
'11': require('@/pages/Console/plan.tsx').default,
'12': require('@/pages/Console/milestoneDetail.tsx').default,
'13': require('@/pages/Console/newMilestone.tsx').default,
'14': require('@/pages/Console/pipelineDetail.tsx').default,
'15': require('@/pages/repository/index.tsx').default,
'16': require('@/layouts/ModelsLayout.tsx').default,
'17': require('@/pages/models/index.tsx').default,
'18': require('@/pages/models/overview.tsx').default,
'19': require('@/pages/models/applications.tsx').default,
'20': require('@/pages/models/datasets.tsx').default,
'21': require('@/pages/models/resources.tsx').default,
'22': require('./EmptyRoute').default,
'23': require('@/pages/tools/modeling.tsx').default,
'24': require('@/pages/tools/s2s.tsx').default,
'25': require('@/pages/tools/ide/ide.tsx').default,
'26': require('@/layouts/RepoLayout.tsx').default,
'27': require('@/pages/tools/repo/index.tsx').default,
'28': require('@/pages/tools/repo/newProject.tsx').default,
'29': require('@/pages/tools/repo/branch.tsx').default,
'30': require('@/pages/tools/repo/commits.tsx').default,
'31': require('@/pages/tools/repo/compare.tsx').default,
'32': require('@/pages/tools/repo/files.tsx').default,
'33': require('@/pages/tools/repo/newFile.tsx').default,
'34': require('@/pages/tools/repo/newTag.tsx').default,
'35': require('@/pages/tools/repo/compare_result.tsx').default,
'36': require('@/pages/tools/repo/tags.tsx').default,
'37': require('@/pages/tools/repo/newMergeRequest.tsx').default,
'38': require('@/pages/tools/repo/mergeRequests.tsx').default,
'39': require('@/pages/tools/repo/newMergeRequest.tsx').default,
'40': require('@/pages/tools/repo/settings.tsx').default,
'41': require('@/pages/tools/build.tsx').default,
'42': require('@/pages/tools/file.tsx').default,
'43': require('@/pages/ci/index.tsx').default,
'44': require('./EmptyRoute').default,
'45': require('@/pages/deploy/resource.tsx').default,
'46': require('@/pages/deploy/plan.tsx').default,
'47': require('@/pages/deploy/tools.tsx').default,
'48': require('@/pages/deploy/migration.tsx').default,
'49': require('@/pages/deploy/tasks.tsx').default,
'50': require('@/pages/appstore/index.tsx').default,
'51': require('@/pages/help/index.tsx').default,
'52': require('./EmptyRoute').default,
'53': require('@/pages/404.tsx').default,
'ant-design-pro-layout': require('D:/project/web_app_0527v2/web/src/.umi-test/plugin-layout/Layout.tsx').default,
},
  };
}
