"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7797],{85425:function(X,g,t){var j=t(5574),b=t.n(j),e=t(20057),B=t(85673),u=t(84226),i=t(31622),o=t(67294),h=t(85893),G=function(Z){var S=Z.proName,p=Z.currentPath,T=Z.customItems,P=(0,e.TH)(),N=(0,e.s0)(),R=(0,u.useIntl)(),J=(0,o.useState)((0,i.gh)()||"dark"),A=b()(J,2),I=A[0],W=A[1];(0,o.useEffect)(function(){W((0,i.gh)());var k=new MutationObserver(function(a){a.forEach(function(s){s.attributeName==="data-theme"&&W((0,i.gh)())})});return k.observe(document.body,{attributes:!0}),function(){k.disconnect()}},[]);var F=function(){if(T)return T;var a=P.pathname,s=[{path:"/tools",breadcrumbName:"\u5DE5\u5177\u5957\u4EF6"},{path:"/tools/repo",breadcrumbName:"\u4EE3\u7801\u4ED3\u5E93"}];if(a.startsWith("/tools/repo/newProject"))return[].concat(s,[{path:"/tools/repo/newProject",breadcrumbName:"\u65B0\u5EFA\u9879\u76EE"}]);if(a.startsWith("/tools/repo/files/newFile"))return[].concat(s,[{path:"/tools/repo/files",breadcrumbName:"\u6587\u4EF6\u7BA1\u7406"},{path:"/tools/repo/files/newFile",breadcrumbName:"\u65B0\u5EFA\u6587\u4EF6"}]);if(a.startsWith("/tools/repo/files")){var x=[].concat(s,[{path:"/tools/repo/files",breadcrumbName:"\u6587\u4EF6\u7BA1\u7406"}]);if(S&&x.push({path:"/tools/repo/files",breadcrumbName:S}),p){var U=p.split("/").filter(Boolean),L="";U.forEach(function(H,V){L+="/".concat(H),x.push({path:"/tools/repo/files?path=".concat(L),breadcrumbName:H})})}return x}else{if(a.startsWith("/tools/repo/branch"))return[].concat(s,[{path:"/tools/repo/branch",breadcrumbName:"\u5206\u652F\u7BA1\u7406"}]);if(a.startsWith("/tools/repo/commits"))return[].concat(s,[{path:"/tools/repo/commits",breadcrumbName:"\u63D0\u4EA4\u7BA1\u7406"}]);if(a.startsWith("/tools/repo/compare"))return[].concat(s,[{path:"/tools/repo/compare",breadcrumbName:"\u6BD4\u8F83\u4FEE\u8BA2\u7248\u672C"}]);if(a.startsWith("/tools/repo/newTag"))return[].concat(s,[{path:"/tools/repo/tags",breadcrumbName:"\u6807\u7B7E"},{path:"/tools/repo/newTag",breadcrumbName:"\u65B0\u5EFA\u6807\u7B7E"}]);if(a.startsWith("/tools/repo/tags"))return[].concat(s,[{path:"/tools/repo/tags",breadcrumbName:"\u6807\u7B7E"}])}return s};return(0,h.jsx)(B.Z,{separator:"/",style:{fontSize:"14px",marginBottom:"16px",color:I==="light"?"#666":"#aaa"},items:F(),itemRender:function(a,s,x){var U=x.indexOf(a)===x.length-1;return U?(0,h.jsx)("span",{style:{color:I==="light"?"#333":"#fff"},children:a.breadcrumbName}):(0,h.jsx)("a",{onClick:function(){return a.path?N(a.path):null},style:{color:I==="light"?"#666":"#aaa"},children:a.breadcrumbName})}})};g.Z=G},57945:function(X,g,t){t.d(g,{o4:function(){return ee},$E:function(){return U},y_:function(){return L}});var j=t(67294),b=t(46671),e=t(85893),B=function(r){var n=r.currentBranch,l=r.branches,c=r.theme,d=r.onBranchSelect,f=[{key:"1",type:"group",label:"\u5207\u6362\u5206\u652F",children:l.length>0?l.map(function(v,C){return{key:"branch-".concat(C),label:v.name,onClick:function(){return d(v.name)}}}):[{key:"1",label:n||"main",onClick:function(){return d(n||"main")}}]}];return _jsx(Dropdown,{trigger:["click"],menu:{items:f,style:getDropdownStyle(c)},children:_jsx(Button,{onClick:function(C){return C.preventDefault()},size:"large",style:{marginRight:16},children:_jsxs(Space,{children:[n,_jsx(DownOutlined,{})]})})})},u=t(15009),i=t.n(u),o=t(99289),h=t.n(o),G=t(5574),Q=t.n(G),Z=t(78957),S=t(71471),p=t(83622),T=t(66309),P=t(83062),N=t(85418),R=t(2487),J=t(85175),A=t(50336),I=t(41441),W=t(69753),F=t(34804),k=t(82061),a=t(44438),s=function(r){var n=r.branch,l=r.onCopy,c=r.onNavigate;return(0,e.jsxs)(Z.Z,{size:12,children:[c?(0,e.jsx)(S.Z.Link,{strong:!0,onClick:function(){return c(n.name)},children:n.name}):(0,e.jsx)(S.Z.Text,{strong:!0,children:n.name}),(0,e.jsx)(p.ZP,{style:{border:"0",color:"#20c997"},ghost:!0,onClick:function(){return l(n.name)},title:"\u590D\u5236\u5206\u652F\u540D\u79F0",children:(0,e.jsx)(J.Z,{})}),n.protected?(0,e.jsx)(T.Z,{color:"geekblue",children:"protected"}):"",n.default?(0,e.jsx)(T.Z,{color:"pink",children:"default"}):"",n.update&&(0,a.E_)(n.update)&&(0,e.jsx)(T.Z,{color:"green",children:"Active"}),n.update&&!(0,a.E_)(n.update)&&(0,e.jsx)(T.Z,{color:"orange",children:"Stale"})]})},x=function(r){var n=r.branch;return(0,e.jsxs)(Z.Z,{size:12,children:[n.id&&(0,e.jsx)("span",{children:n.id}),n.title&&(0,e.jsx)("span",{children:n.title}),n.update&&(0,e.jsx)("span",{children:(0,a.ct)(n.update.toString())})]})},U=function(r){var n=r.branches,l=r.onCopy,c=r.onDelete,d=r.onCompare,f=r.onMergeRequest,v=r.onNavigate,C=(0,j.useState)(void 0),E=Q()(C,2),M=E[0],ne=E[1],_=function(){var z=h()(i()().mark(function m(D,K){return i()().wrap(function($){for(;;)switch($.prev=$.next){case 0:case"end":return $.stop()}},m)}));return function(D,K){return z.apply(this,arguments)}}();if(n.length===1){var y=n[0],w=[];return y.default||(f&&w.push((0,e.jsx)(P.Z,{title:"\u521B\u5EFA\u5408\u5E76\u8BF7\u6C42",children:(0,e.jsx)(p.ZP,{size:"middle",style:{marginRight:8},onClick:function(){return f(y.name)},children:(0,e.jsx)(A.Z,{})},"mergeRequest")},"mergeRequestTooltip")),d&&w.push((0,e.jsx)(P.Z,{title:"\u6BD4\u8F83\u5206\u652F",children:(0,e.jsx)(p.ZP,{size:"middle",style:{marginRight:8},onClick:function(){return d(y.name)},children:(0,e.jsx)(I.Z,{})},"compare")},"compareTooltip"))),w.push((0,e.jsx)(N.Z,{menu:{onClick:function(m){var D=m.key;return _(D,y.name)},items:[{key:"zip",label:"zip"},{key:"tar.gz",label:"tar.gz"},{key:"tar.bz2",label:"tar.bz2"},{key:"tar",label:"tar"}]},trigger:["click"],children:(0,e.jsxs)(p.ZP,{size:"middle",style:{marginRight:8},children:[(0,e.jsx)(W.Z,{}),(0,e.jsx)(F.Z,{})]})},"download")),w.push((0,e.jsx)(p.ZP,{size:"middle",style:{border:"0"},onClick:function(){return c(y.name)},disabled:y.protected===!0,title:y.protected?"\u53D7\u4FDD\u62A4\u5206\u652F\u4E0D\u80FD\u5220\u9664":"",children:(0,e.jsx)(k.Z,{})},"more")),(0,e.jsx)(R.Z.Item,{actions:w,children:(0,e.jsx)(R.Z.Item.Meta,{title:(0,e.jsx)(s,{branch:y,onCopy:l,onNavigate:v}),description:(0,e.jsx)(x,{branch:y})})})}return(0,e.jsx)(R.Z,{size:"large",dataSource:n,renderItem:function(m){var D=[];return m.default||(f&&D.push((0,e.jsx)(P.Z,{title:"\u521B\u5EFA\u5408\u5E76\u8BF7\u6C42",children:(0,e.jsx)(p.ZP,{size:"middle",style:{marginRight:8},onClick:function(){return f(m.name)},children:(0,e.jsx)(A.Z,{})},"mergeRequest")},"mergeRequestTooltip")),d&&D.push((0,e.jsx)(P.Z,{title:"\u6BD4\u8F83\u5206\u652F",children:(0,e.jsx)(p.ZP,{size:"middle",style:{marginRight:8},onClick:function(){return d(m.name)},children:(0,e.jsx)(I.Z,{})},"compare")},"compareTooltip"))),D.push((0,e.jsx)(N.Z,{menu:{onClick:function(q){var $=q.key;return _($,m.name)},items:[{key:"zip",label:"zip"},{key:"tar.gz",label:"tar.gz"},{key:"tar.bz2",label:"tar.bz2"},{key:"tar",label:"tar"}]},trigger:["click"],children:(0,e.jsxs)(p.ZP,{size:"middle",style:{marginRight:8},children:[(0,e.jsx)(W.Z,{}),(0,e.jsx)(F.Z,{})]})},"download")),D.push((0,e.jsx)(p.ZP,{size:"middle",style:{border:"0"},onClick:function(){return c(m.name)},disabled:m.protected===!0,title:m.protected?"\u53D7\u4FDD\u62A4\u5206\u652F\u4E0D\u80FD\u5220\u9664":"",children:(0,e.jsx)(k.Z,{})},"more")),(0,e.jsx)(R.Z.Item,{actions:D,children:(0,e.jsx)(R.Z.Item.Meta,{title:(0,e.jsx)(s,{branch:m,onCopy:l,onNavigate:v}),description:(0,e.jsx)(x,{branch:m})})})}})},L=function(r){var n,l,c;return{name:r.name,id:(n=r.commit)===null||n===void 0?void 0:n.short_id,update:(l=r.commit)===null||l===void 0?void 0:l.created_at,title:(c=r.commit)===null||c===void 0?void 0:c.title,protected:r.protected,default:r.default}},H=t(97857),V=t.n(H),te=function(r){var n=r.placeholder,l=n===void 0?"\u641C\u7D22":n,c=r.value,d=r.onChange,f=r.style;return _jsx("span",{style:{display:"inline-block",verticalAlign:"middle"},children:_jsx(Input,{placeholder:l,prefix:_jsx(SearchOutlined,{}),style:_objectSpread({width:200,height:"40px",marginRight:6},f),value:c,onChange:function(C){return d(C.target.value)},allowClear:!0})})},Y=t(44394),ee=function(r){var n=r.branches,l=r.defaultBranch,c=l===void 0?"main":l,d=r.onBranchSelect,f=r.buttonStyle,v=(0,Y.gz)(),C=[{key:"1",type:"group",label:"\u5207\u6362\u5206\u652F",children:n.length>0?n.map(function(E,M){return{key:"branch-".concat(M),label:E.name}}):[{key:"1",label:c}]}];return(0,e.jsx)(N.Z,{trigger:["click"],menu:{items:C,onClick:d?function(E){var M=E.key;return d(M)}:void 0,style:(0,Y.XE)(v)},children:(0,e.jsx)(p.ZP,{onClick:function(M){return M.preventDefault()},size:"large",style:V()({marginRight:16},f),children:(0,e.jsxs)(Z.Z,{children:[c,(0,e.jsx)(F.Z,{})]})})})}},44438:function(X,g,t){t.d(g,{E_:function(){return e},ct:function(){return j}});var j=function(u){if(!u)return"";var i=new Date,o=new Date(u),h=Math.abs(i.getTime()-o.getTime())/(1e3*60*60);return h>=24?"".concat(Math.round(h/24),"\u5929\u524D"):"".concat(Math.round(h),"\u5C0F\u65F6\u524D")},b=function(u){if(!u)return!1;var i=new Date,o=new Date(u),h=(i.getTime()-o.getTime())/(1e3*60*60*24);return h<=5},e=function(u){if(!u)return!1;var i=new Date,o=new Date(u),h=Math.abs(i.getTime()-o.getTime())/(1e3*60*60);return h<720}},44394:function(X,g,t){t.d(g,{E_:function(){return j.E_},XE:function(){return b.XE},gz:function(){return b.gz}});var j=t(44438),b=t(46671)},46671:function(X,g,t){t.d(g,{XE:function(){return u},gz:function(){return b}});var j=t(31622),b=function(){return(0,j.gh)()||"dark"},e=function(o){return o==="light"?"#fff":"#1a1c1e"},B=function(o){return o==="light"?"#1a1c1e":"#fff"},u=function(o){return{background:e(o),color:B(o)}}}}]);
