!(function(){var dt=(pe,oe)=>(oe=Symbol[pe])?oe:Symbol.for("Symbol."+pe),gi=pe=>{throw TypeError(pe)};var hi=function(pe,oe){this[0]=pe,this[1]=oe};var mt=pe=>{var oe=pe[dt("asyncIterator")],m=!1,l,G={};return oe==null?(oe=pe[dt("iterator")](),l=$=>G[$]=J=>oe[$](J)):(oe=oe.call(pe),l=$=>G[$]=J=>{if(m){if(m=!1,$==="throw")throw J;return J}return m=!0,{done:!1,value:new hi(new Promise(j=>{var d=oe[$](J);d instanceof Object||gi("Object expected"),j(d)}),1)}}),G[dt("iterator")]=()=>G,l("next"),"throw"in oe?l("throw"):G.throw=$=>{throw $},"return"in oe&&l("return"),G};(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4265],{2354:function(pe,oe,m){"use strict";m.d(oe,{f:function(){return qa}});var l=m(4942),G=m(74165),$=m(15861),J=m(91),j=m(97685),d=m(1413),ie=m(10915),ne=m(21770),p=m(67294);function U(r){var e=typeof window=="undefined",n=(0,p.useState)(function(){return e?!1:window.matchMedia(r).matches}),t=(0,j.Z)(n,2),o=t[0],i=t[1];return(0,p.useLayoutEffect)(function(){if(!e){var a=window.matchMedia(r),c=function(f){return i(f.matches)};return a.addListener(c),function(){return a.removeListener(c)}}},[r]),o}var W={xs:{maxWidth:575,matchMedia:"(max-width: 575px)"},sm:{minWidth:576,maxWidth:767,matchMedia:"(min-width: 576px) and (max-width: 767px)"},md:{minWidth:768,maxWidth:991,matchMedia:"(min-width: 768px) and (max-width: 991px)"},lg:{minWidth:992,maxWidth:1199,matchMedia:"(min-width: 992px) and (max-width: 1199px)"},xl:{minWidth:1200,maxWidth:1599,matchMedia:"(min-width: 1200px) and (max-width: 1599px)"},xxl:{minWidth:1600,matchMedia:"(min-width: 1600px)"}},Z=function(){var e=void 0;if(typeof window=="undefined")return e;var n=Object.keys(W).find(function(t){var o=W[t].matchMedia;return!!window.matchMedia(o).matches});return e=n,e},re=function(){var e=U(W.md.matchMedia),n=U(W.lg.matchMedia),t=U(W.xxl.matchMedia),o=U(W.xl.matchMedia),i=U(W.sm.matchMedia),a=U(W.xs.matchMedia),c=(0,p.useState)(Z()),u=(0,j.Z)(c,2),f=u[0],g=u[1];return(0,p.useEffect)(function(){if(t){g("xxl");return}if(o){g("xl");return}if(n){g("lg");return}if(e){g("md");return}if(i){g("sm");return}if(a){g("xs");return}g("md")},[e,n,t,o,i,a]),f},N=m(12044);function h(r,e){var n=typeof r.pageName=="string"?r.title:e;(0,p.useEffect)(function(){(0,N.j)()&&n&&(document.title=n)},[r.title,n])}var y=m(1977),S=m(73177);function H(r){if((0,y.n)((0,S.b)(),"5.6.0")<0)return r;var e={colorGroupTitle:"groupTitleColor",radiusItem:"itemBorderRadius",radiusSubMenuItem:"subMenuItemBorderRadius",colorItemText:"itemColor",colorItemTextHover:"itemHoverColor",colorItemTextHoverHorizontal:"horizontalItemHoverColor",colorItemTextSelected:"itemSelectedColor",colorItemTextSelectedHorizontal:"horizontalItemSelectedColor",colorItemTextDisabled:"itemDisabledColor",colorDangerItemText:"dangerItemColor",colorDangerItemTextHover:"dangerItemHoverColor",colorDangerItemTextSelected:"dangerItemSelectedColor",colorDangerItemBgActive:"dangerItemActiveBg",colorDangerItemBgSelected:"dangerItemSelectedBg",colorItemBg:"itemBg",colorItemBgHover:"itemHoverBg",colorSubItemBg:"subMenuItemBg",colorItemBgActive:"itemActiveBg",colorItemBgSelected:"itemSelectedBg",colorItemBgSelectedHorizontal:"horizontalItemSelectedBg",colorActiveBarWidth:"activeBarWidth",colorActiveBarHeight:"activeBarHeight",colorActiveBarBorderSize:"activeBarBorderWidth"},n=(0,d.Z)({},r);return Object.keys(e).forEach(function(t){n[t]!==void 0&&(n[e[t]]=n[t],delete n[t])}),n}var ee=m(90743);function F(r,e){return e>>>r|e<<32-r}function ae(r,e,n){return r&e^~r&n}function le(r,e,n){return r&e^r&n^e&n}function te(r){return F(2,r)^F(13,r)^F(22,r)}function _(r){return F(6,r)^F(11,r)^F(25,r)}function C(r){return F(7,r)^F(18,r)^r>>>3}function v(r){return F(17,r)^F(19,r)^r>>>10}function A(r,e){return r[e&15]+=v(r[e+14&15])+r[e+9&15]+C(r[e+1&15])}var D=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],P,k,I,ue="0123456789abcdef";function ve(r,e){var n=(r&65535)+(e&65535),t=(r>>16)+(e>>16)+(n>>16);return t<<16|n&65535}function be(){P=new Array(8),k=new Array(2),I=new Array(64),k[0]=k[1]=0,P[0]=1779033703,P[1]=3144134277,P[2]=1013904242,P[3]=2773480762,P[4]=1359893119,P[5]=2600822924,P[6]=528734635,P[7]=1541459225}function ye(){var r,e,n,t,o,i,a,c,u,f,g=new Array(16);r=P[0],e=P[1],n=P[2],t=P[3],o=P[4],i=P[5],a=P[6],c=P[7];for(var x=0;x<16;x++)g[x]=I[(x<<2)+3]|I[(x<<2)+2]<<8|I[(x<<2)+1]<<16|I[x<<2]<<24;for(var b=0;b<64;b++)u=c+_(o)+ae(o,i,a)+D[b],b<16?u+=g[b]:u+=A(g,b),f=te(r)+le(r,e,n),c=a,a=i,i=o,o=ve(t,u),t=n,n=e,e=r,r=ve(u,f);P[0]+=r,P[1]+=e,P[2]+=n,P[3]+=t,P[4]+=o,P[5]+=i,P[6]+=a,P[7]+=c}function Ie(r,e){var n,t,o=0;t=k[0]>>3&63;var i=e&63;for((k[0]+=e<<3)<e<<3&&k[1]++,k[1]+=e>>29,n=0;n+63<e;n+=64){for(var a=t;a<64;a++)I[a]=r.charCodeAt(o++);ye(),t=0}for(var c=0;c<i;c++)I[c]=r.charCodeAt(o++)}function Se(){var r=k[0]>>3&63;if(I[r++]=128,r<=56)for(var e=r;e<56;e++)I[e]=0;else{for(var n=r;n<64;n++)I[n]=0;ye();for(var t=0;t<56;t++)I[t]=0}I[56]=k[1]>>>24&255,I[57]=k[1]>>>16&255,I[58]=k[1]>>>8&255,I[59]=k[1]&255,I[60]=k[0]>>>24&255,I[61]=k[0]>>>16&255,I[62]=k[0]>>>8&255,I[63]=k[0]&255,ye()}function De(){for(var r=0,e=new Array(32),n=0;n<8;n++)e[r++]=P[n]>>>24&255,e[r++]=P[n]>>>16&255,e[r++]=P[n]>>>8&255,e[r++]=P[n]&255;return e}function Ae(){for(var r=new String,e=0;e<8;e++)for(var n=28;n>=0;n-=4)r+=ue.charAt(P[e]>>>n&15);return r}function Fe(r){return be(),Ie(r,r.length),Se(),Ae()}var Qe=Fe;function ze(r){"@babel/helpers - typeof";return ze=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ze(r)}var un=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function dn(r,e){return Ye(r)||pn(r,e)||Vn(r,e)||mn()}function mn(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function pn(r,e){var n=r==null?null:typeof Symbol!="undefined"&&r[Symbol.iterator]||r["@@iterator"];if(n!=null){var t=[],o=!0,i=!1,a,c;try{for(n=n.call(r);!(o=(a=n.next()).done)&&(t.push(a.value),!(e&&t.length===e));o=!0);}catch(u){i=!0,c=u}finally{try{!o&&n.return!=null&&n.return()}finally{if(i)throw c}}return t}}function Ye(r){if(Array.isArray(r))return r}function Ke(r,e){var n=typeof Symbol!="undefined"&&r[Symbol.iterator]||r["@@iterator"];if(!n){if(Array.isArray(r)||(n=Vn(r))||e&&r&&typeof r.length=="number"){n&&(r=n);var t=0,o=function(){};return{s:o,n:function(){return t>=r.length?{done:!0}:{done:!1,value:r[t++]}},e:function(f){throw f},f:o}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i=!0,a=!1,c;return{s:function(){n=n.call(r)},n:function(){var f=n.next();return i=f.done,f},e:function(f){a=!0,c=f},f:function(){try{!i&&n.return!=null&&n.return()}finally{if(a)throw c}}}}function fn(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function Dn(r,e){for(var n=0;n<e.length;n++){var t=e[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(r,t.key,t)}}function Rn(r,e,n){return e&&Dn(r.prototype,e),n&&Dn(r,n),Object.defineProperty(r,"prototype",{writable:!1}),r}function On(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&gn(r,e)}function Le(r){var e=pt();return function(){var t=hn(r),o;if(e){var i=hn(this).constructor;o=Reflect.construct(t,arguments,i)}else o=t.apply(this,arguments);return nn(this,o)}}function nn(r,e){if(e&&(ze(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return vn(r)}function vn(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function $n(r){var e=typeof Map=="function"?new Map:void 0;return $n=function(t){if(t===null||!pr(t))return t;if(typeof t!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e!="undefined"){if(e.has(t))return e.get(t);e.set(t,o)}function o(){return Tn(t,arguments,hn(this).constructor)}return o.prototype=Object.create(t.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),gn(o,t)},$n(r)}function Tn(r,e,n){return pt()?Tn=Reflect.construct.bind():Tn=function(o,i,a){var c=[null];c.push.apply(c,i);var u=Function.bind.apply(o,c),f=new u;return a&&gn(f,a.prototype),f},Tn.apply(null,arguments)}function pt(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(r){return!1}}function pr(r){return Function.toString.call(r).indexOf("[native code]")!==-1}function gn(r,e){return gn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,o){return t.__proto__=o,t},gn(r,e)}function hn(r){return hn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},hn(r)}function ft(r){return gr(r)||vr(r)||Vn(r)||fr()}function fr(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Vn(r,e){if(r){if(typeof r=="string")return zn(r,e);var n=Object.prototype.toString.call(r).slice(8,-1);if(n==="Object"&&r.constructor&&(n=r.constructor.name),n==="Map"||n==="Set")return Array.from(r);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return zn(r,e)}}function vr(r){if(typeof Symbol!="undefined"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function gr(r){if(Array.isArray(r))return zn(r)}function zn(r,e){(e==null||e>r.length)&&(e=r.length);for(var n=0,t=new Array(e);n<e;n++)t[n]=r[n];return t}function hr(r,e){if(r==null)return{};var n=yr(r,e),t,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(r);for(o=0;o<i.length;o++)t=i[o],!(e.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(r,t)&&(n[t]=r[t])}return n}function yr(r,e){if(r==null)return{};var n={},t=Object.keys(r),o,i;for(i=0;i<t.length;i++)o=t[i],!(e.indexOf(o)>=0)&&(n[o]=r[o]);return n}function vt(r,e){var n=Object.keys(r);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(r);e&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(r,o).enumerable})),n.push.apply(n,t)}return n}function _e(r){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?vt(Object(n),!0).forEach(function(t){Cr(r,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):vt(Object(n)).forEach(function(t){Object.defineProperty(r,t,Object.getOwnPropertyDescriptor(n,t))})}return r}function Cr(r,e,n){return e in r?Object.defineProperty(r,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[e]=n,r}var we="routes";function yn(r){return r.split("?")[0].split("#")[0]}var Kn=function(e){if(!e.startsWith("http"))return!1;try{var n=new URL(e);return!!n}catch(t){return!1}},xr=function(e){var n=e.path;if(!n||n==="/")try{return"/".concat(Qe(JSON.stringify(e)))}catch(t){}return n&&yn(n)},br=function(e,n){var t=e.name,o=e.locale;return"locale"in e&&o===!1||!t?!1:e.locale||"".concat(n,".").concat(t)},gt=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/";return e.endsWith("/*")?e.replace("/*","/"):(e||n).startsWith("/")||Kn(e)?e:"/".concat(n,"/").concat(e).replace(/\/\//g,"/").replace(/\/\//g,"/")},Sr=function(e,n){var t=e.menu,o=t===void 0?{}:t,i=e.indexRoute,a=e.path,c=a===void 0?"":a,u=e.children||[],f=o.name,g=f===void 0?e.name:f,x=o.icon,b=x===void 0?e.icon:x,R=o.hideChildren,V=R===void 0?e.hideChildren:R,w=o.flatMenu,O=w===void 0?e.flatMenu:w,Y=i&&Object.keys(i).join(",")!=="redirect"?[_e({path:c,menu:o},i)].concat(u||[]):u,K=_e({},e);if(g&&(K.name=g),b&&(K.icon=b),Y&&Y.length){if(V)return delete K.children,K;var q=kn(_e(_e({},n),{},{data:Y}),e);if(O)return q;delete K[we]}return K},qe=function(e){return Array.isArray(e)&&e.length>0};function kn(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{path:"/"},n=r.data,t=r.formatMessage,o=r.parentName,i=r.locale;return!n||!Array.isArray(n)?[]:n.filter(function(a){return a?qe(a.children)||a.path||a.originPath||a.layout?!0:(a.redirect||a.unaccessible,!1):!1}).filter(function(a){var c,u;return!(a==null||(c=a.menu)===null||c===void 0)&&c.name||a!=null&&a.flatMenu||!(a==null||(u=a.menu)===null||u===void 0)&&u.flatMenu?!0:a.menu!==!1}).map(function(a){var c=_e(_e({},a),{},{path:a.path||a.originPath});return!c.children&&c[we]&&(c.children=c[we],delete c[we]),c.unaccessible&&delete c.name,c.path==="*"&&(c.path="."),c.path==="/*"&&(c.path="."),!c.path&&c.originPath&&(c.path=c.originPath),c}).map(function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{path:"/"},c=a.children||a[we]||[],u=gt(a.path,e?e.path:"/"),f=a.name,g=br(a,o||"menu"),x=g!==!1&&i!==!1&&t&&g?t({id:g,defaultMessage:f}):f,b=e.pro_layout_parentKeys,R=b===void 0?[]:b,V=e.children,w=e.icon,O=e.flatMenu,Y=e.indexRoute,K=e.routes,q=hr(e,un),L=new Set([].concat(ft(R),ft(a.parentKeys||[])));e.key&&L.add(e.key);var X=_e(_e(_e({},q),{},{menu:void 0},a),{},{path:u,locale:g,key:a.key||xr(_e(_e({},a),{},{path:u})),pro_layout_parentKeys:Array.from(L).filter(function(B){return B&&B!=="/"})});if(x?X.name=x:delete X.name,X.menu===void 0&&delete X.menu,qe(c)){var M=kn(_e(_e({},r),{},{data:c,parentName:g||""}),X);qe(M)&&(X.children=M)}return Sr(X,r)}).flat(1)}var _r=function r(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.filter(function(n){return n&&(n.name||qe(n.children))&&!n.hideInMenu&&!n.redirect}).map(function(n){var t=_e({},n),o=t.children||n[we]||[];if(delete t[we],qe(o)&&!t.hideChildrenInMenu&&o.some(function(a){return a&&!!a.name})){var i=r(o);if(i.length)return _e(_e({},t),{},{children:i})}return _e({},n)}).filter(function(n){return n})},Pr=function(r){On(n,r);var e=Le(n);function n(){return fn(this,n),e.apply(this,arguments)}return Rn(n,[{key:"get",value:function(o){var i;try{var a=Ke(this.entries()),c;try{for(a.s();!(c=a.n()).done;){var u=dn(c.value,2),f=u[0],g=u[1],x=yn(f);if(!Kn(f)&&(0,ee.Bo)(x,[]).test(o)){i=g;break}}}catch(b){a.e(b)}finally{a.f()}}catch(b){i=void 0}return i}}]),n}($n(Map)),Er=function(e){var n=new Pr,t=function o(i,a){i.forEach(function(c){var u=c.children||c[we]||[];qe(u)&&o(u,c);var f=gt(c.path,a?a.path:"/");n.set(yn(f),c)})};return t(e),n},Mr=function r(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.map(function(n){var t=n.children||n[we];if(qe(t)){var o=r(t);if(o.length)return _e({},n)}var i=_e({},n);return delete i[we],delete i.children,i}).filter(function(n){return n})},Ir=function(e,n,t,o){var i=kn({data:e,formatMessage:t,locale:n}),a=o?Mr(i):_r(i),c=Er(i);return{breadcrumb:c,menuData:a}},Dr=Ir;function ht(r,e){var n=Object.keys(r);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(r);e&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(r,o).enumerable})),n.push.apply(n,t)}return n}function Cn(r){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?ht(Object(n),!0).forEach(function(t){Rr(r,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(n)):ht(Object(n)).forEach(function(t){Object.defineProperty(r,t,Object.getOwnPropertyDescriptor(n,t))})}return r}function Rr(r,e,n){return e in r?Object.defineProperty(r,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[e]=n,r}var Or=function r(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n={};return e.forEach(function(t){var o=Cn({},t);if(!(!o||!o.key)){!o.children&&o[we]&&(o.children=o[we],delete o[we]);var i=o.children||[];n[yn(o.path||o.key||"/")]=Cn({},o),n[o.key||o.path||"/"]=Cn({},o),i&&(n=Cn(Cn({},n),r(i)))}}),n},Tr=Or,Ar=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0;return e.filter(function(o){if(o==="/"&&n==="/")return!0;if(o!=="/"&&o!=="/*"&&o&&!Kn(o)){var i=yn(o);try{if(t&&(0,ee.Bo)("".concat(i)).test(n)||(0,ee.Bo)("".concat(i),[]).test(n)||(0,ee.Bo)("".concat(i,"/(.*)")).test(n))return!0}catch(a){}}return!1}).sort(function(o,i){return o===n?10:i===n?-10:o.substr(1).split("/").length-i.substr(1).split("/").length})},Nr=function(e,n,t,o){var i=Tr(n),a=Object.keys(i),c=Ar(a,e||"/",o);return!c||c.length<1?[]:(t||(c=[c[c.length-1]]),c.map(function(u){var f=i[u]||{pro_layout_parentKeys:"",key:""},g=new Map,x=(f.pro_layout_parentKeys||[]).map(function(b){return g.has(b)?null:(g.set(b,!0),i[b])}).filter(function(b){return b});return f.key&&x.push(f),x}).flat(1))},Zr=Nr,He=m(21532),en=m(26058),wr=m(93967),me=m.n(wr),yt=m(98423),Ct=m(80334),jr=m(5068),Lr=m(25269),Br=m(78164),s=m(85893),Wr=function(e){var n=(0,p.useContext)(ie.L_),t=n.hashId,o=e.style,i=e.prefixCls,a=e.children,c=e.hasPageContainer,u=c===void 0?0:c,f=me()("".concat(i,"-content"),t,(0,l.Z)((0,l.Z)({},"".concat(i,"-has-header"),e.hasHeader),"".concat(i,"-content-has-page-container"),u>0)),g=e.ErrorBoundary||Br.S;return e.ErrorBoundary===!1?(0,s.jsx)(en.Z.Content,{className:f,style:o,children:a}):(0,s.jsx)(g,{children:(0,s.jsx)(en.Z.Content,{className:f,style:o,children:a})})},Fr=function(){return(0,s.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 200 200",children:[(0,s.jsxs)("defs",{children:[(0,s.jsxs)("linearGradient",{x1:"62.1023273%",y1:"0%",x2:"108.19718%",y2:"37.8635764%",id:"linearGradient-1",children:[(0,s.jsx)("stop",{stopColor:"#4285EB",offset:"0%"}),(0,s.jsx)("stop",{stopColor:"#2EC7FF",offset:"100%"})]}),(0,s.jsxs)("linearGradient",{x1:"69.644116%",y1:"0%",x2:"54.0428975%",y2:"108.456714%",id:"linearGradient-2",children:[(0,s.jsx)("stop",{stopColor:"#29CDFF",offset:"0%"}),(0,s.jsx)("stop",{stopColor:"#148EFF",offset:"37.8600687%"}),(0,s.jsx)("stop",{stopColor:"#0A60FF",offset:"100%"})]}),(0,s.jsxs)("linearGradient",{x1:"69.6908165%",y1:"-12.9743587%",x2:"16.7228981%",y2:"117.391248%",id:"linearGradient-3",children:[(0,s.jsx)("stop",{stopColor:"#FA816E",offset:"0%"}),(0,s.jsx)("stop",{stopColor:"#F74A5C",offset:"41.472606%"}),(0,s.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]}),(0,s.jsxs)("linearGradient",{x1:"68.1279872%",y1:"-35.6905737%",x2:"30.4400914%",y2:"114.942679%",id:"linearGradient-4",children:[(0,s.jsx)("stop",{stopColor:"#FA8E7D",offset:"0%"}),(0,s.jsx)("stop",{stopColor:"#F74A5C",offset:"51.2635191%"}),(0,s.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]})]}),(0,s.jsx)("g",{stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd",children:(0,s.jsx)("g",{transform:"translate(-20.000000, -20.000000)",children:(0,s.jsx)("g",{transform:"translate(20.000000, 20.000000)",children:(0,s.jsxs)("g",{children:[(0,s.jsxs)("g",{fillRule:"nonzero",children:[(0,s.jsxs)("g",{children:[(0,s.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-1)"}),(0,s.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-2)"})]}),(0,s.jsx)("path",{d:"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",fill:"url(#linearGradient-3)"})]}),(0,s.jsx)("ellipse",{fill:"url(#linearGradient-4)",cx:"100.519339",cy:"100.436681",rx:"23.6001926",ry:"23.580786"})]})})})})]})},tn=m(87462),Hr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"}}]},name:"copyright",theme:"outlined"},Ur=Hr,xt=m(87646),$r=(0,p.createContext)({}),Gn=$r,Xn=m(71002),Vr=m(44958),zr=m(27571);function Kr(r){return r.replace(/-(.)/g,function(e,n){return n.toUpperCase()})}function Jn(r,e){(0,Ct.ZP)(r,"[@ant-design/icons] ".concat(e))}function bt(r){return(0,Xn.Z)(r)==="object"&&typeof r.name=="string"&&typeof r.theme=="string"&&((0,Xn.Z)(r.icon)==="object"||typeof r.icon=="function")}function St(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(r).reduce(function(e,n){var t=r[n];switch(n){case"class":e.className=t,delete e.class;break;default:delete e[n],e[Kr(n)]=t}return e},{})}function Qn(r,e,n){return n?p.createElement(r.tag,(0,d.Z)((0,d.Z)({key:e},St(r.attrs)),n),(r.children||[]).map(function(t,o){return Qn(t,"".concat(e,"-").concat(r.tag,"-").concat(o))})):p.createElement(r.tag,(0,d.Z)({key:e},St(r.attrs)),(r.children||[]).map(function(t,o){return Qn(t,"".concat(e,"-").concat(r.tag,"-").concat(o))}))}function _t(r){return(0,xt.generate)(r)[0]}function Pt(r){return r?Array.isArray(r)?r:[r]:[]}var kr={width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",focusable:"false"},Gr=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,Et=function(e){var n=(0,p.useContext)(Gn),t=n.csp,o=n.prefixCls,i=n.layer,a=Gr;o&&(a=a.replace(/anticon/g,o)),i&&(a="@layer ".concat(i,` {
`).concat(a,`
}`)),(0,p.useEffect)(function(){var c=e.current,u=(0,zr.A)(c);(0,Vr.hq)(a,"@ant-design-icons",{prepend:!i,csp:t,attachTo:u})},[])},Xr=["icon","className","onClick","style","primaryColor","secondaryColor"],xn={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function Jr(r){var e=r.primaryColor,n=r.secondaryColor;xn.primaryColor=e,xn.secondaryColor=n||_t(e),xn.calculated=!!n}function Qr(){return(0,d.Z)({},xn)}var An=function(e){var n=e.icon,t=e.className,o=e.onClick,i=e.style,a=e.primaryColor,c=e.secondaryColor,u=(0,J.Z)(e,Xr),f=p.useRef(),g=xn;if(a&&(g={primaryColor:a,secondaryColor:c||_t(a)}),Et(f),Jn(bt(n),"icon should be icon definiton, but got ".concat(n)),!bt(n))return null;var x=n;return x&&typeof x.icon=="function"&&(x=(0,d.Z)((0,d.Z)({},x),{},{icon:x.icon(g.primaryColor,g.secondaryColor)})),Qn(x.icon,"svg-".concat(x.name),(0,d.Z)((0,d.Z)({className:t,onClick:o,style:i,"data-icon":x.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},u),{},{ref:f}))};An.displayName="IconReact",An.getTwoToneColors=Qr,An.setTwoToneColors=Jr;var Yn=An;function Mt(r){var e=Pt(r),n=(0,j.Z)(e,2),t=n[0],o=n[1];return Yn.setTwoToneColors({primaryColor:t,secondaryColor:o})}function Yr(){var r=Yn.getTwoToneColors();return r.calculated?[r.primaryColor,r.secondaryColor]:r.primaryColor}var qr=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];Mt(xt.blue.primary);var Nn=p.forwardRef(function(r,e){var n=r.className,t=r.icon,o=r.spin,i=r.rotate,a=r.tabIndex,c=r.onClick,u=r.twoToneColor,f=(0,J.Z)(r,qr),g=p.useContext(Gn),x=g.prefixCls,b=x===void 0?"anticon":x,R=g.rootClassName,V=me()(R,b,(0,l.Z)((0,l.Z)({},"".concat(b,"-").concat(t.name),!!t.name),"".concat(b,"-spin"),!!o||t.name==="loading"),n),w=a;w===void 0&&c&&(w=-1);var O=i?{msTransform:"rotate(".concat(i,"deg)"),transform:"rotate(".concat(i,"deg)")}:void 0,Y=Pt(u),K=(0,j.Z)(Y,2),q=K[0],L=K[1];return p.createElement("span",(0,tn.Z)({role:"img","aria-label":t.name},f,{ref:e,tabIndex:w,onClick:c,className:V}),p.createElement(Yn,{icon:t,primaryColor:q,secondaryColor:L,style:O}))});Nn.displayName="AntdIcon",Nn.getTwoToneColor=Yr,Nn.setTwoToneColor=Mt;var It=Nn,eo=function(e,n){return p.createElement(It,(0,tn.Z)({},e,{ref:n,icon:Ur}))},no=p.forwardRef(eo),to=no,Ne=m(64847),ro=function(e){return(0,l.Z)({},e.componentCls,{marginBlock:0,marginBlockStart:48,marginBlockEnd:24,marginInline:0,paddingBlock:0,paddingInline:16,textAlign:"center","&-list":{marginBlockEnd:8,color:e.colorTextSecondary,"&-link":{color:e.colorTextSecondary,textDecoration:e.linkDecoration},"*:not(:last-child)":{marginInlineEnd:8},"&:hover":{color:e.colorPrimary}},"&-copyright":{fontSize:"14px",color:e.colorText}})};function oo(r){return(0,Ne.Xj)("ProLayoutFooter",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[ro(n)]})}var ao=function(e){var n=e.className,t=e.prefixCls,o=e.links,i=e.copyright,a=e.style,c=(0,p.useContext)(He.ZP.ConfigContext),u=c.getPrefixCls(t||"pro-global-footer"),f=oo(u),g=f.wrapSSR,x=f.hashId;return(o==null||o===!1||Array.isArray(o)&&o.length===0)&&(i==null||i===!1)?null:g((0,s.jsxs)("div",{className:me()(u,x,n),style:a,children:[o&&(0,s.jsx)("div",{className:"".concat(u,"-list ").concat(x).trim(),children:o.map(function(b){return(0,s.jsx)("a",{className:"".concat(u,"-list-link ").concat(x).trim(),title:b.key,target:b.blankTarget?"_blank":"_self",href:b.href,rel:"noreferrer",children:b.title},b.key)})}),i&&(0,s.jsx)("div",{className:"".concat(u,"-copyright ").concat(x).trim(),children:i})]}))},io=en.Z.Footer,lo=function(e){var n=e.links,t=e.copyright,o=e.style,i=e.className,a=e.prefixCls;return(0,s.jsx)(io,{className:i,style:(0,d.Z)({padding:0},o),children:(0,s.jsx)(ao,{links:n,prefixCls:a,copyright:t===!1?null:(0,s.jsxs)(p.Fragment,{children:[(0,s.jsx)(to,{})," ",t]})})})},Dt=function r(e){return(e||[]).reduce(function(n,t){if(t.key&&n.push(t.key),t.children||t.routes){var o=n.concat(r(t.children||t.routes)||[]);return o}return n},[])},Rt={techBlue:"#1677FF",daybreak:"#1890ff",dust:"#F5222D",volcano:"#FA541C",sunset:"#FAAD14",cyan:"#13C2C2",green:"#52C41A",geekblue:"#2F54EB",purple:"#722ED1"};function yi(r){return r&&Rt[r]?Rt[r]:r||""}function Zn(r){return r.map(function(e){var n=e.children||[],t=(0,d.Z)({},e);if(!t.children&&t.routes&&(t.children=t.routes),!t.name||t.hideInMenu)return null;if(t&&t!==null&&t!==void 0&&t.children){if(!t.hideChildrenInMenu&&n.some(function(o){return o&&o.name&&!o.hideInMenu}))return(0,d.Z)((0,d.Z)({},e),{},{children:Zn(n)});delete t.children}return delete t.routes,t}).filter(function(e){return e})}var so={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},co=so,uo=function(e,n){return p.createElement(It,(0,tn.Z)({},e,{ref:n,icon:co}))},mo=p.forwardRef(uo),po=mo,fo=m(55241),vo=function(){return(0,s.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{d:"M0 0h3v3H0V0zm4.5 0h3v3h-3V0zM9 0h3v3H9V0zM0 4.5h3v3H0v-3zm4.503 0h3v3h-3v-3zM9 4.5h3v3H9v-3zM0 9h3v3H0V9zm4.503 0h3v3h-3V9zM9 9h3v3H9V9z"})})},go=function r(e){var n=e.appList,t=e.baseClassName,o=e.hashId,i=e.itemClick;return(0,s.jsx)("div",{className:"".concat(t,"-content ").concat(o).trim(),children:(0,s.jsx)("ul",{className:"".concat(t,"-content-list ").concat(o).trim(),children:n==null?void 0:n.map(function(a,c){var u;return a!=null&&(u=a.children)!==null&&u!==void 0&&u.length?(0,s.jsxs)("div",{className:"".concat(t,"-content-list-item-group ").concat(o).trim(),children:[(0,s.jsx)("div",{className:"".concat(t,"-content-list-item-group-title ").concat(o).trim(),children:a.title}),(0,s.jsx)(r,{hashId:o,itemClick:i,appList:a==null?void 0:a.children,baseClassName:t})]},c):(0,s.jsx)("li",{className:"".concat(t,"-content-list-item ").concat(o).trim(),onClick:function(g){g.stopPropagation(),i==null||i(a)},children:(0,s.jsxs)("a",{href:i?void 0:a.url,target:a.target,rel:"noreferrer",children:[et(a.icon),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:a.title}),a.desc?(0,s.jsx)("span",{children:a.desc}):null]})]})},c)})})})},qn=function(e){if(!e||!e.startsWith("http"))return!1;try{var n=new URL(e);return!!n}catch(t){return!1}},ho=function(e,n){if(e&&typeof e=="string"&&qn(e))return(0,s.jsx)("img",{src:e,alt:"logo"});if(typeof e=="function")return e();if(e&&typeof e=="string")return(0,s.jsx)("div",{id:"avatarLogo",children:e});if(!e&&n&&typeof n=="string"){var t=n.substring(0,1);return(0,s.jsx)("div",{id:"avatarLogo",children:t})}return e},yo=function r(e){var n=e.appList,t=e.baseClassName,o=e.hashId,i=e.itemClick;return(0,s.jsx)("div",{className:"".concat(t,"-content ").concat(o).trim(),children:(0,s.jsx)("ul",{className:"".concat(t,"-content-list ").concat(o).trim(),children:n==null?void 0:n.map(function(a,c){var u;return a!=null&&(u=a.children)!==null&&u!==void 0&&u.length?(0,s.jsxs)("div",{className:"".concat(t,"-content-list-item-group ").concat(o).trim(),children:[(0,s.jsx)("div",{className:"".concat(t,"-content-list-item-group-title ").concat(o).trim(),children:a.title}),(0,s.jsx)(r,{hashId:o,itemClick:i,appList:a==null?void 0:a.children,baseClassName:t})]},c):(0,s.jsx)("li",{className:"".concat(t,"-content-list-item ").concat(o).trim(),onClick:function(g){g.stopPropagation(),i==null||i(a)},children:(0,s.jsxs)("a",{href:i?"javascript:;":a.url,target:a.target,rel:"noreferrer",children:[ho(a.icon,a.title),(0,s.jsx)("div",{children:(0,s.jsx)("div",{children:a.title})})]})},c)})})})},Co=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"content-box",maxWidth:656,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:328,height:72,paddingInline:16,paddingBlock:16,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},"* div":Ne.Wf===null||Ne.Wf===void 0?void 0:(0,Ne.Wf)(e),a:{display:"flex",height:"100%",fontSize:12,textDecoration:"none","& > img":{width:40,height:40},"& > div":{marginInlineStart:14,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},xo=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"border-box",maxWidth:376,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:104,height:104,marginBlock:8,marginInline:8,paddingInline:24,paddingBlock:24,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},a:{display:"flex",flexDirection:"column",alignItems:"center",height:"100%",fontSize:12,textDecoration:"none","& > #avatarLogo":{width:40,height:40,margin:"0 auto",color:e.colorPrimary,fontSize:22,lineHeight:"40px",textAlign:"center",backgroundImage:"linear-gradient(180deg, #E8F0FB 0%, #F6F8FC 100%)",borderRadius:e.borderRadius},"& > img":{width:40,height:40},"& > div":{marginBlockStart:5,marginInlineStart:0,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},bo=function(e){var n,t,o,i,a;return(0,l.Z)({},e.componentCls,{"&-icon":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInline:4,paddingBlock:0,fontSize:14,lineHeight:"14px",height:28,width:28,cursor:"pointer",color:(n=e.layout)===null||n===void 0?void 0:n.colorTextAppListIcon,borderRadius:e.borderRadius,"&:hover":{color:(t=e.layout)===null||t===void 0?void 0:t.colorTextAppListIconHover,backgroundColor:(o=e.layout)===null||o===void 0?void 0:o.colorBgAppListIconHover},"&-active":{color:(i=e.layout)===null||i===void 0?void 0:i.colorTextAppListIconHover,backgroundColor:(a=e.layout)===null||a===void 0?void 0:a.colorBgAppListIconHover}},"&-item-title":{marginInlineStart:"16px",marginInlineEnd:"8px",marginBlockStart:0,marginBlockEnd:"12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginBlockStart:12}},"&-popover":(0,l.Z)({},"".concat(e.antCls,"-popover-arrow"),{display:"none"}),"&-simple":xo(e),"&-default":Co(e)})};function So(r){return(0,Ne.Xj)("AppsLogoComponents",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[bo(n)]})}var et=function(e){return typeof e=="string"?(0,s.jsx)("img",{width:"auto",height:22,src:e,alt:"logo"}):typeof e=="function"?e():e},nt=function(e){var n,t=e.appList,o=e.appListRender,i=e.prefixCls,a=i===void 0?"ant-pro":i,c=e.onItemClick,u=p.useRef(null),f=p.useRef(null),g="".concat(a,"-layout-apps"),x=So(g),b=x.wrapSSR,R=x.hashId,V=(0,p.useState)(!1),w=(0,j.Z)(V,2),O=w[0],Y=w[1],K=function(B){c==null||c(B,f)},q=(0,p.useMemo)(function(){var M=t==null?void 0:t.some(function(B){return!(B!=null&&B.desc)});return M?(0,s.jsx)(yo,{hashId:R,appList:t,itemClick:c?K:void 0,baseClassName:"".concat(g,"-simple")}):(0,s.jsx)(go,{hashId:R,appList:t,itemClick:c?K:void 0,baseClassName:"".concat(g,"-default")})},[t,g,R]);if(!(e!=null&&(n=e.appList)!==null&&n!==void 0&&n.length))return null;var L=o?o(e==null?void 0:e.appList,q):q,X=(0,S.X)(void 0,function(M){return Y(M)});return b((0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{ref:u,onClick:function(B){B.stopPropagation(),B.preventDefault()}}),(0,s.jsx)(fo.Z,(0,d.Z)((0,d.Z)({placement:"bottomRight",trigger:["click"],zIndex:9999,arrow:!1},X),{},{overlayClassName:"".concat(g,"-popover ").concat(R).trim(),content:L,getPopupContainer:function(){return u.current||document.body},children:(0,s.jsx)("span",{ref:f,onClick:function(B){B.stopPropagation()},className:me()("".concat(g,"-icon"),R,(0,l.Z)({},"".concat(g,"-icon-active"),O)),children:(0,s.jsx)(vo,{})})}))]}))},Ot=m(85357),_o=m(78957),Tt=m(50136);function Po(){return(0,s.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{d:"M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z"})})}var Eo=function(e){var n,t,o;return(0,l.Z)({},e.componentCls,{position:"absolute",insetBlockStart:"18px",zIndex:"101",width:"24px",height:"24px",fontSize:["14px","16px"],textAlign:"center",borderRadius:"40px",insetInlineEnd:"-13px",transition:"transform 0.3s",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:(n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorTextCollapsedButton,backgroundColor:(t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorBgCollapsedButton,boxShadow:"0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)","&:hover":{color:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorTextCollapsedButtonHover,boxShadow:"0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)"},".anticon":{fontSize:"14px"},"& > svg":{transition:"transform  0.3s",transform:"rotate(90deg)"},"&-collapsed":{"& > svg":{transform:"rotate(-90deg)"}}})};function Mo(r){return(0,Ne.Xj)("SiderMenuCollapsedIcon",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[Eo(n)]})}var Io=["isMobile","collapsed"],Do=function(e){var n=e.isMobile,t=e.collapsed,o=(0,J.Z)(e,Io),i=Mo(e.className),a=i.wrapSSR,c=i.hashId;return n&&t?null:a((0,s.jsx)("div",(0,d.Z)((0,d.Z)({},o),{},{className:me()(e.className,c,(0,l.Z)((0,l.Z)({},"".concat(e.className,"-collapsed"),t),"".concat(e.className,"-is-mobile"),n)),children:(0,s.jsx)(Po,{})})))},wn=m(74902),Ro=m(43144),Oo=m(15671),To=m(42550),Ao=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"],At=p.forwardRef(function(r,e){var n=r.className,t=r.component,o=r.viewBox,i=r.spin,a=r.rotate,c=r.tabIndex,u=r.onClick,f=r.children,g=(0,J.Z)(r,Ao),x=p.useRef(),b=(0,To.x1)(x,e);Jn(!!(t||f),"Should have `component` prop or `children`."),Et(x);var R=p.useContext(Gn),V=R.prefixCls,w=V===void 0?"anticon":V,O=R.rootClassName,Y=me()(O,w,(0,l.Z)({},"".concat(w,"-spin"),!!i&&!!t),n),K=me()((0,l.Z)({},"".concat(w,"-spin"),!!i)),q=a?{msTransform:"rotate(".concat(a,"deg)"),transform:"rotate(".concat(a,"deg)")}:void 0,L=(0,d.Z)((0,d.Z)({},kr),{},{className:K,style:q,viewBox:o});o||delete L.viewBox;var X=function(){return t?p.createElement(t,L,f):f?(Jn(!!o||p.Children.count(f)===1&&p.isValidElement(f)&&p.Children.only(f).type==="use","Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),p.createElement("svg",(0,tn.Z)({},L,{viewBox:o}),f)):null},M=c;return M===void 0&&u&&(M=-1),p.createElement("span",(0,tn.Z)({role:"img"},g,{ref:b,tabIndex:M,onClick:u,className:Y}),X())});At.displayName="AntdIcon";var No=At,Zo=["type","children"],Nt=new Set;function wo(r){return!!(typeof r=="string"&&r.length&&!Nt.has(r))}function jn(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=r[e];if(wo(n)){var t=document.createElement("script");t.setAttribute("src",n),t.setAttribute("data-namespace",n),r.length>e+1&&(t.onload=function(){jn(r,e+1)},t.onerror=function(){jn(r,e+1)}),Nt.add(n),document.body.appendChild(t)}}function Zt(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=r.scriptUrl,n=r.extraCommonProps,t=n===void 0?{}:n;e&&typeof document!="undefined"&&typeof window!="undefined"&&typeof document.createElement=="function"&&(Array.isArray(e)?jn(e.reverse()):jn([e]));var o=p.forwardRef(function(i,a){var c=i.type,u=i.children,f=(0,J.Z)(i,Zo),g=null;return i.type&&(g=p.createElement("use",{xlinkHref:"#".concat(c)})),u&&(g=u),p.createElement(No,(0,tn.Z)({},t,f,{ref:a}),g)});return o.displayName="Iconfont",o}function jo(r){return/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(r)}var Lo=m(83062),Bo=m(48054),wt={navTheme:"light",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!0,iconfontUrl:"",colorPrimary:"#1677FF",splitMenus:!1},Wo=function(e,n){var t,o,i=n.includes("horizontal")?(t=e.layout)===null||t===void 0?void 0:t.header:(o=e.layout)===null||o===void 0?void 0:o.sider;return(0,d.Z)((0,d.Z)((0,l.Z)({},"".concat(e.componentCls),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({background:"transparent",color:i==null?void 0:i.colorTextMenu,border:"none"},"".concat(e.componentCls,"-menu-item"),{transition:"none !important"}),"".concat(e.componentCls,"-submenu-has-icon"),(0,l.Z)({},"> ".concat(e.antCls,"-menu-sub"),{paddingInlineStart:10})),"".concat(e.antCls,"-menu-title-content"),{width:"100%",height:"100%",display:"inline-flex"}),"".concat(e.antCls,"-menu-title-content"),{"&:first-child":{width:"100%"}}),"".concat(e.componentCls,"-item-icon"),{display:"flex",alignItems:"center"}),"&&-collapsed",(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title"),{paddingInline:"0 !important",marginBlock:"4px !important"}),"".concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title"),{backgroundColor:i==null?void 0:i.colorBgMenuItemSelected,borderRadius:e.borderRadiusLG}),"".concat(e.componentCls,"-group"),(0,l.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{paddingInline:0}))),"&-item-title",(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({display:"flex",flexDirection:"row",alignItems:"center",gap:e.marginXS},"".concat(e.componentCls,"-item-text"),{maxWidth:"100%",textOverflow:"ellipsis",overflow:"hidden",wordBreak:"break-all",whiteSpace:"nowrap"}),"&-collapsed",(0,l.Z)((0,l.Z)({minWidth:40,height:40},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px !important",height:"16px"}}),"".concat(e.componentCls,"-item-text-has-icon"),{display:"none !important"})),"&-collapsed-level-0",{flexDirection:"column",justifyContent:"center"}),"&".concat(e.componentCls,"-group-item-title"),{gap:e.marginXS,height:18,overflow:"hidden"}),"&".concat(e.componentCls,"-item-collapsed-show-title"),(0,l.Z)({lineHeight:"16px",gap:0},"&".concat(e.componentCls,"-item-title-collapsed"),(0,l.Z)((0,l.Z)({display:"flex"},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px!important",height:"16px"}}),"".concat(e.componentCls,"-item-text"),{opacity:"1 !important",display:"inline !important",textAlign:"center",fontSize:12,height:12,lineHeight:"12px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",margin:0,padding:0,marginBlockStart:4})))),"&-group",(0,l.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:12,color:e.colorTextLabel,".anticon":{marginInlineEnd:8}})),"&-group-divider",{color:e.colorTextSecondary,fontSize:12,lineHeight:20})),n.includes("horizontal")?{}:(0,l.Z)({},"".concat(e.antCls,"-menu-submenu").concat(e.antCls,"-menu-submenu-popup"),(0,l.Z)({},"".concat(e.componentCls,"-item-title"),{alignItems:"flex-start"}))),{},(0,l.Z)({},"".concat(e.antCls,"-menu-submenu-popup"),{backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"}))};function Fo(r,e){return(0,Ne.Xj)("ProLayoutBaseMenu"+e,function(n){var t=(0,d.Z)((0,d.Z)({},n),{},{componentCls:".".concat(r)});return[Wo(t,e||"inline")]})}var jt=function(e){var n=(0,p.useState)(e.collapsed),t=(0,j.Z)(n,2),o=t[0],i=t[1],a=(0,p.useState)(!1),c=(0,j.Z)(a,2),u=c[0],f=c[1];return(0,p.useEffect)(function(){f(!1),setTimeout(function(){i(e.collapsed)},400)},[e.collapsed]),e.disable?e.children:(0,s.jsx)(Lo.Z,{title:e.title,open:o&&e.collapsed?u:!1,placement:"right",onOpenChange:f,children:e.children})},Lt=Zt({scriptUrl:wt.iconfontUrl}),Bt=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"icon-",t=arguments.length>2?arguments[2]:void 0;if(typeof e=="string"&&e!==""){if(qn(e)||jo(e))return(0,s.jsx)("img",{width:16,src:e,alt:"icon",className:t},e);if(e.startsWith(n))return(0,s.jsx)(Lt,{type:e})}return e},Wt=function(e){if(e&&typeof e=="string"){var n=e.substring(0,1).toUpperCase();return n}return null},Ho=(0,Ro.Z)(function r(e){var n=this;(0,Oo.Z)(this,r),(0,l.Z)(this,"props",void 0),(0,l.Z)(this,"getNavMenuItems",function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],o=arguments.length>1?arguments[1]:void 0,i=arguments.length>2?arguments[2]:void 0;return t.map(function(a){return n.getSubMenuOrItem(a,o,i)}).filter(function(a){return a}).flat(1)}),(0,l.Z)(this,"getSubMenuOrItem",function(t,o,i){var a=n.props,c=a.subMenuItemRender,u=a.baseClassName,f=a.prefixCls,g=a.collapsed,x=a.menu,b=a.iconPrefixes,R=a.layout,V=(x==null?void 0:x.type)==="group"&&R!=="top",w=n.props.token,O=n.getIntlName(t),Y=(t==null?void 0:t.children)||(t==null?void 0:t.routes),K=V&&o===0?"group":void 0;if(Array.isArray(Y)&&Y.length>0){var q,L,X,M,B,ce=o===0||V&&o===1,Q=Bt(t.icon,b,"".concat(u,"-icon ").concat((q=n.props)===null||q===void 0?void 0:q.hashId)),z=g&&ce?Wt(O):null,fe=(0,s.jsxs)("div",{className:me()("".concat(u,"-item-title"),(L=n.props)===null||L===void 0?void 0:L.hashId,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(u,"-item-title-collapsed"),g),"".concat(u,"-item-title-collapsed-level-").concat(i),g),"".concat(u,"-group-item-title"),K==="group"),"".concat(u,"-item-collapsed-show-title"),(x==null?void 0:x.collapsedShowTitle)&&g)),children:[K==="group"&&g?null:ce&&Q?(0,s.jsx)("span",{className:"".concat(u,"-item-icon ").concat((X=n.props)===null||X===void 0?void 0:X.hashId).trim(),children:Q}):z,(0,s.jsx)("span",{className:me()("".concat(u,"-item-text"),(M=n.props)===null||M===void 0?void 0:M.hashId,(0,l.Z)({},"".concat(u,"-item-text-has-icon"),K!=="group"&&ce&&(Q||z))),children:O})]}),Ce=c?c((0,d.Z)((0,d.Z)({},t),{},{isUrl:!1}),fe,n.props):fe;if(V&&o===0&&n.props.collapsed&&!x.collapsedShowGroupTitle)return n.getNavMenuItems(Y,o+1,o);var E=n.getNavMenuItems(Y,o+1,V&&o===0&&n.props.collapsed?o:o+1);return[{type:K,key:t.key||t.path,label:Ce,onClick:V?void 0:t.onTitleClick,children:E,className:me()((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(u,"-group"),K==="group"),"".concat(u,"-submenu"),K!=="group"),"".concat(u,"-submenu-has-icon"),K!=="group"&&ce&&Q))},V&&o===0?{type:"divider",prefixCls:f,className:"".concat(u,"-divider"),key:(t.key||t.path)+"-group-divider",style:{padding:0,borderBlockEnd:0,margin:n.props.collapsed?"4px":"6px 16px",marginBlockStart:n.props.collapsed?4:8,borderColor:w==null||(B=w.layout)===null||B===void 0||(B=B.sider)===null||B===void 0?void 0:B.colorMenuItemDivider}}:void 0].filter(Boolean)}return{className:"".concat(u,"-menu-item"),disabled:t.disabled,key:t.key||t.path,onClick:t.onTitleClick,label:n.getMenuItemPath(t,o,i)}}),(0,l.Z)(this,"getIntlName",function(t){var o=t.name,i=t.locale,a=n.props,c=a.menu,u=a.formatMessage,f=o;return i&&(c==null?void 0:c.locale)!==!1&&(f=u==null?void 0:u({id:i,defaultMessage:o})),n.props.menuTextRender?n.props.menuTextRender(t,f,n.props):f}),(0,l.Z)(this,"getMenuItemPath",function(t,o,i){var a,c,u,f,g=n.conversionPath(t.path||"/"),x=n.props,b=x.location,R=b===void 0?{pathname:"/"}:b,V=x.isMobile,w=x.onCollapse,O=x.menuItemRender,Y=x.iconPrefixes,K=n.getIntlName(t),q=n.props,L=q.baseClassName,X=q.menu,M=q.collapsed,B=(X==null?void 0:X.type)==="group",ce=o===0||B&&o===1,Q=ce?Bt(t.icon,Y,"".concat(L,"-icon ").concat((a=n.props)===null||a===void 0?void 0:a.hashId)):null,z=M&&ce?Wt(K):null,fe=(0,s.jsxs)("div",{className:me()("".concat(L,"-item-title"),(c=n.props)===null||c===void 0?void 0:c.hashId,(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(L,"-item-title-collapsed"),M),"".concat(L,"-item-title-collapsed-level-").concat(i),M),"".concat(L,"-item-collapsed-show-title"),(X==null?void 0:X.collapsedShowTitle)&&M)),children:[(0,s.jsx)("span",{className:"".concat(L,"-item-icon ").concat((u=n.props)===null||u===void 0?void 0:u.hashId).trim(),style:{display:z===null&&!Q?"none":""},children:Q||(0,s.jsx)("span",{className:"anticon",children:z})}),(0,s.jsx)("span",{className:me()("".concat(L,"-item-text"),(f=n.props)===null||f===void 0?void 0:f.hashId,(0,l.Z)({},"".concat(L,"-item-text-has-icon"),ce&&(Q||z))),children:K})]},g),Ce=qn(g);if(Ce){var E,xe,T;fe=(0,s.jsxs)("span",{onClick:function(){var Pe,he;(Pe=window)===null||Pe===void 0||(he=Pe.open)===null||he===void 0||he.call(Pe,g,"_blank")},className:me()("".concat(L,"-item-title"),(E=n.props)===null||E===void 0?void 0:E.hashId,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(L,"-item-title-collapsed"),M),"".concat(L,"-item-title-collapsed-level-").concat(i),M),"".concat(L,"-item-link"),!0),"".concat(L,"-item-collapsed-show-title"),(X==null?void 0:X.collapsedShowTitle)&&M)),children:[(0,s.jsx)("span",{className:"".concat(L,"-item-icon ").concat((xe=n.props)===null||xe===void 0?void 0:xe.hashId).trim(),style:{display:z===null&&!Q?"none":""},children:Q||(0,s.jsx)("span",{className:"anticon",children:z})}),(0,s.jsx)("span",{className:me()("".concat(L,"-item-text"),(T=n.props)===null||T===void 0?void 0:T.hashId,(0,l.Z)({},"".concat(L,"-item-text-has-icon"),ce&&(Q||z))),children:K})]},g)}if(O){var de=(0,d.Z)((0,d.Z)({},t),{},{isUrl:Ce,itemPath:g,isMobile:V,replace:g===R.pathname,onClick:function(){return w&&w(!0)},children:void 0});return o===0?(0,s.jsx)(jt,{collapsed:M,title:K,disable:t.disabledTooltip,children:O(de,fe,n.props)}):O(de,fe,n.props)}return o===0?(0,s.jsx)(jt,{collapsed:M,title:K,disable:t.disabledTooltip,children:fe}):fe}),(0,l.Z)(this,"conversionPath",function(t){return t&&t.indexOf("http")===0?t:"/".concat(t||"").replace(/\/+/g,"/")}),this.props=e}),Uo=function(e,n){var t=n.layout,o=n.collapsed,i={};return e&&!o&&["side","mix"].includes(t||"mix")&&(i={openKeys:e}),i},Ft=function(e){var n=e.mode,t=e.className,o=e.handleOpenChange,i=e.style,a=e.menuData,c=e.prefixCls,u=e.menu,f=e.matchMenuKeys,g=e.iconfontUrl,x=e.selectedKeys,b=e.onSelect,R=e.menuRenderType,V=e.openKeys,w=(0,p.useContext)(ie.L_),O=w.dark,Y=w.token,K="".concat(c,"-base-menu-").concat(n),q=(0,p.useRef)([]),L=(0,ne.Z)(u==null?void 0:u.defaultOpenAll),X=(0,j.Z)(L,2),M=X[0],B=X[1],ce=(0,ne.Z)(function(){return u!=null&&u.defaultOpenAll?Dt(a)||[]:V===!1?!1:[]},{value:V===!1?void 0:V,onChange:o}),Q=(0,j.Z)(ce,2),z=Q[0],fe=Q[1],Ce=(0,ne.Z)([],{value:x,onChange:b?function(Me){b&&Me&&b(Me)}:void 0}),E=(0,j.Z)(Ce,2),xe=E[0],T=E[1];(0,p.useEffect)(function(){u!=null&&u.defaultOpenAll||V===!1||f&&(fe(f),T(f))},[f.join("-")]),(0,p.useEffect)(function(){g&&(Lt=Zt({scriptUrl:g}))},[g]),(0,p.useEffect)(function(){if(f.join("-")!==(xe||[]).join("-")&&T(f),!M&&V!==!1&&f.join("-")!==(z||[]).join("-")){var Me=f;(u==null?void 0:u.autoClose)===!1&&(Me=Array.from(new Set([].concat((0,wn.Z)(f),(0,wn.Z)(z||[]))))),fe(Me)}else u!=null&&u.ignoreFlatMenu&&M?fe(Dt(a)):B(!1)},[f.join("-")]);var de=(0,p.useMemo)(function(){return Uo(z,e)},[z&&z.join(","),e.layout,e.collapsed]),ge=Fo(K,n),Pe=ge.wrapSSR,he=ge.hashId,Re=(0,p.useMemo)(function(){return new Ho((0,d.Z)((0,d.Z)({},e),{},{token:Y,menuRenderType:R,baseClassName:K,hashId:he}))},[e,Y,R,K,he]);if(u!=null&&u.loading)return(0,s.jsx)("div",{style:n!=null&&n.includes("inline")?{padding:24}:{marginBlockStart:16},children:(0,s.jsx)(Bo.Z,{active:!0,title:!1,paragraph:{rows:n!=null&&n.includes("inline")?6:1}})});e.openKeys===!1&&!e.handleOpenChange&&(q.current=f);var Ee=e.postMenuData?e.postMenuData(a):a;return Ee&&(Ee==null?void 0:Ee.length)<1?null:Pe((0,p.createElement)(Tt.Z,(0,d.Z)((0,d.Z)({},de),{},{_internalDisableMenuItemTitleTooltip:!0,key:"Menu",mode:n,inlineIndent:16,defaultOpenKeys:q.current,theme:O?"dark":"light",selectedKeys:xe,style:(0,d.Z)({backgroundColor:"transparent",border:"none"},i),className:me()(t,he,K,(0,l.Z)((0,l.Z)({},"".concat(K,"-horizontal"),n==="horizontal"),"".concat(K,"-collapsed"),e.collapsed)),items:Re.getNavMenuItems(Ee,0,0),onOpenChange:function(Be){e.collapsed||fe(Be)}},e.menuProps)))};function $o(r,e){var n=e.stylish,t=e.proLayoutCollapsedWidth;return(0,Ne.Xj)("ProLayoutSiderMenuStylish",function(o){var i=(0,d.Z)((0,d.Z)({},o),{},{componentCls:".".concat(r),proLayoutCollapsedWidth:t});return n?[(0,l.Z)({},"div".concat(o.proComponentsCls,"-layout"),(0,l.Z)({},"".concat(i.componentCls),n==null?void 0:n(i)))]:[]})}var Vo=["title","render"],zo=p.memo(function(r){return(0,s.jsx)(s.Fragment,{children:r.children})}),Ko=en.Z.Sider,Ht=en.Z._InternalSiderContext,ko=Ht===void 0?{Provider:zo}:Ht,tt=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"menuHeaderRender",t=e.logo,o=e.title,i=e.layout,a=e[n];if(a===!1)return null;var c=et(t),u=(0,s.jsx)("h1",{children:o!=null?o:"Ant Design Pro"});return a?a(c,e.collapsed?null:u,e):e.isMobile?null:i==="mix"&&n==="menuHeaderRender"?!1:e.collapsed?(0,s.jsx)("a",{children:c},"title"):(0,s.jsxs)("a",{children:[c,u]},"title")},Ut=function(e){var n,t=e.collapsed,o=e.originCollapsed,i=e.fixSiderbar,a=e.menuFooterRender,c=e.onCollapse,u=e.theme,f=e.siderWidth,g=e.isMobile,x=e.onMenuHeaderClick,b=e.breakpoint,R=b===void 0?"lg":b,V=e.style,w=e.layout,O=e.menuExtraRender,Y=O===void 0?!1:O,K=e.links,q=e.menuContentRender,L=e.collapsedButtonRender,X=e.prefixCls,M=e.avatarProps,B=e.rightContentRender,ce=e.actionsRender,Q=e.onOpenChange,z=e.stylish,fe=e.logoStyle,Ce=(0,p.useContext)(ie.L_),E=Ce.hashId,xe=(0,p.useMemo)(function(){return!(g||w==="mix")},[g,w]),T="".concat(X,"-sider"),de=64,ge=$o("".concat(T,".").concat(T,"-stylish"),{stylish:z,proLayoutCollapsedWidth:de}),Pe=me()("".concat(T),E,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(T,"-fixed"),i),"".concat(T,"-fixed-mix"),w==="mix"&&!g&&i),"".concat(T,"-collapsed"),e.collapsed),"".concat(T,"-layout-").concat(w),w&&!g),"".concat(T,"-light"),u!=="dark"),"".concat(T,"-mix"),w==="mix"&&!g),"".concat(T,"-stylish"),!!z)),he=tt(e),Re=Y&&Y(e),Ee=(0,p.useMemo)(function(){return q!==!1&&(0,p.createElement)(Ft,(0,d.Z)((0,d.Z)({},e),{},{key:"base-menu",mode:t&&!g?"vertical":"inline",handleOpenChange:Q,style:{width:"100%"},className:"".concat(T,"-menu ").concat(E).trim()}))},[T,E,q,Q,e]),Me=(K||[]).map(function(Oe,Ue){return{className:"".concat(T,"-link"),label:Oe,key:Ue}}),Be=(0,p.useMemo)(function(){return q?q(e,Ee):Ee},[q,Ee,e]),Ze=(0,p.useMemo)(function(){if(!M)return null;var Oe=M.title,Ue=M.render,We=(0,J.Z)(M,Vo),Wn=(0,s.jsxs)("div",{className:"".concat(T,"-actions-avatar"),children:[We!=null&&We.src||We!=null&&We.srcSet||We.icon||We.children?(0,s.jsx)(Ot.Z,(0,d.Z)({size:28},We)):null,M.title&&!t&&(0,s.jsx)("span",{children:Oe})]});return Ue?Ue(M,Wn,e):Wn},[M,T,t]),je=(0,p.useMemo)(function(){return ce?(0,s.jsx)(_o.Z,{align:"center",size:4,direction:t?"vertical":"horizontal",className:me()(["".concat(T,"-actions-list"),t&&"".concat(T,"-actions-list-collapsed"),E]),children:[ce==null?void 0:ce(e)].flat(1).map(function(Oe,Ue){return(0,s.jsx)("div",{className:"".concat(T,"-actions-list-item ").concat(E).trim(),children:Oe},Ue)})}):null},[ce,T,t]),$e=(0,p.useMemo)(function(){return(0,s.jsx)(nt,{onItemClick:e.itemClick,appListRender:e.appListRender,appList:e.appList,prefixCls:e.prefixCls})},[e.appList,e.appListRender,e.prefixCls]),ke=(0,p.useMemo)(function(){if(L===!1)return null;var Oe=(0,s.jsx)(Do,{isMobile:g,collapsed:o,className:"".concat(T,"-collapsed-button"),onClick:function(){c==null||c(!o)}});return L?L(t,Oe):Oe},[L,g,o,T,t,c]),Ge=(0,p.useMemo)(function(){return!Ze&&!je?null:(0,s.jsxs)("div",{className:me()("".concat(T,"-actions"),E,t&&"".concat(T,"-actions-collapsed")),children:[Ze,je]})},[je,Ze,T,t,E]),Xe=(0,p.useMemo)(function(){var Oe;return e!=null&&(Oe=e.menu)!==null&&Oe!==void 0&&Oe.hideMenuWhenCollapsed&&t?"".concat(T,"-hide-menu-collapsed"):null},[T,t,e==null||(n=e.menu)===null||n===void 0?void 0:n.hideMenuWhenCollapsed]),bn=a&&(a==null?void 0:a(e)),Bn=(0,s.jsxs)(s.Fragment,{children:[he&&(0,s.jsxs)("div",{className:me()([me()("".concat(T,"-logo"),E,(0,l.Z)({},"".concat(T,"-logo-collapsed"),t))]),onClick:xe?x:void 0,id:"logo",style:fe,children:[he,$e]}),Re&&(0,s.jsx)("div",{className:me()(["".concat(T,"-extra"),!he&&"".concat(T,"-extra-no-logo"),E]),children:Re}),(0,s.jsx)("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:Be}),(0,s.jsxs)(ko.Provider,{value:{},children:[K?(0,s.jsx)("div",{className:"".concat(T,"-links ").concat(E).trim(),children:(0,s.jsx)(Tt.Z,{inlineIndent:16,className:"".concat(T,"-link-menu ").concat(E).trim(),selectedKeys:[],openKeys:[],theme:u,mode:"inline",items:Me})}):null,xe&&(0,s.jsxs)(s.Fragment,{children:[Ge,!je&&B?(0,s.jsx)("div",{className:me()("".concat(T,"-actions"),E,(0,l.Z)({},"".concat(T,"-actions-collapsed"),t)),children:B==null?void 0:B(e)}):null]}),bn&&(0,s.jsx)("div",{className:me()(["".concat(T,"-footer"),E,(0,l.Z)({},"".concat(T,"-footer-collapsed"),t)]),children:bn})]})]});return ge.wrapSSR((0,s.jsxs)(s.Fragment,{children:[i&&!g&&!Xe&&(0,s.jsx)("div",{style:(0,d.Z)({width:t?de:f,overflow:"hidden",flex:"0 0 ".concat(t?de:f,"px"),maxWidth:t?de:f,minWidth:t?de:f,transition:"all 0.2s ease 0s"},V)}),(0,s.jsxs)(Ko,{collapsible:!0,trigger:null,collapsed:t,breakpoint:R===!1?void 0:R,onCollapse:function(Ue){g||c==null||c(Ue)},collapsedWidth:de,style:V,theme:u,width:f,className:me()(Pe,E,Xe),children:[Xe?(0,s.jsx)("div",{className:"".concat(T,"-hide-when-collapsed ").concat(E).trim(),style:{height:"100%",width:"100%",opacity:Xe?0:1},children:Bn}):Bn,ke]})]}))},Go=m(10178),Xo=m(9220),Jo=function(e){var n,t,o,i,a;return(0,l.Z)({},e.componentCls,{"&-header-actions":{display:"flex",height:"100%",alignItems:"center","&-item":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingBlock:0,paddingInline:2,color:(n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorTextRightActionsItem,fontSize:"16px",cursor:"pointer",borderRadius:e.borderRadius,"> *":{paddingInline:6,paddingBlock:6,borderRadius:e.borderRadius,"&:hover":{backgroundColor:(t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorBgRightActionsItemHover}}},"&-avatar":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInlineStart:e.padding,paddingInlineEnd:e.padding,cursor:"pointer",color:(o=e.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorTextRightActionsItem,"> div":{height:"44px",color:(i=e.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorTextRightActionsItem,paddingInline:8,paddingBlock:8,cursor:"pointer",display:"flex",alignItems:"center",lineHeight:"44px",borderRadius:e.borderRadius,"&:hover":{backgroundColor:(a=e.layout)===null||a===void 0||(a=a.header)===null||a===void 0?void 0:a.colorBgRightActionsItemHover}}}}})};function Qo(r){return(0,Ne.Xj)("ProLayoutRightContent",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[Jo(n)]})}var Yo=["rightContentRender","avatarProps","actionsRender","headerContentRender"],qo=["title","render"],$t=function(e){var n=e.rightContentRender,t=e.avatarProps,o=e.actionsRender,i=e.headerContentRender,a=(0,J.Z)(e,Yo),c=(0,p.useContext)(He.ZP.ConfigContext),u=c.getPrefixCls,f="".concat(u(),"-pro-global-header"),g=Qo(f),x=g.wrapSSR,b=g.hashId,R=(0,p.useState)("auto"),V=(0,j.Z)(R,2),w=V[0],O=V[1],Y=(0,p.useMemo)(function(){if(!t)return null;var X=t.title,M=t.render,B=(0,J.Z)(t,qo),ce=[B!=null&&B.src||B!=null&&B.srcSet||B.icon||B.children?(0,p.createElement)(Ot.Z,(0,d.Z)((0,d.Z)({},B),{},{size:28,key:"avatar"})):null,X?(0,s.jsx)("span",{style:{marginInlineStart:8},children:X},"name"):void 0];return M?M(t,(0,s.jsx)("div",{children:ce}),a):(0,s.jsx)("div",{children:ce})},[t]),K=o||Y?function(X){var M=o&&(o==null?void 0:o(X));return!M&&!Y?null:Array.isArray(M)?x((0,s.jsxs)("div",{className:"".concat(f,"-header-actions ").concat(b).trim(),children:[M.filter(Boolean).map(function(B,ce){var Q=!1;if(p.isValidElement(B)){var z;Q=!!(B!=null&&(z=B.props)!==null&&z!==void 0&&z["aria-hidden"])}return(0,s.jsx)("div",{className:me()("".concat(f,"-header-actions-item ").concat(b),(0,l.Z)({},"".concat(f,"-header-actions-hover"),!Q)),children:B},ce)}),Y&&(0,s.jsx)("span",{className:"".concat(f,"-header-actions-avatar ").concat(b).trim(),children:Y})]})):x((0,s.jsxs)("div",{className:"".concat(f,"-header-actions ").concat(b).trim(),children:[M,Y&&(0,s.jsx)("span",{className:"".concat(f,"-header-actions-avatar ").concat(b).trim(),children:Y})]}))}:void 0,q=(0,Go.D)(function(){var X=(0,$.Z)((0,G.Z)().mark(function M(B){return(0,G.Z)().wrap(function(Q){for(;;)switch(Q.prev=Q.next){case 0:O(B);case 1:case"end":return Q.stop()}},M)}));return function(M){return X.apply(this,arguments)}}(),160),L=K||n;return(0,s.jsx)("div",{className:"".concat(f,"-right-content ").concat(b).trim(),style:{minWidth:w,height:"100%"},children:(0,s.jsx)("div",{style:{height:"100%"},children:(0,s.jsx)(Xo.Z,{onResize:function(M){var B=M.width;q.run(B)},children:L?(0,s.jsx)("div",{style:{display:"flex",alignItems:"center",height:"100%",justifyContent:"flex-end"},children:L((0,d.Z)((0,d.Z)({},a),{},{rightContentSize:w}))}):null})})})},ea=function(e){var n,t;return(0,l.Z)({},e.componentCls,{position:"relative",width:"100%",height:"100%",backgroundColor:"transparent",".anticon":{color:"inherit"},"&-main":{display:"flex",height:"100%",paddingInlineStart:"16px","&-left":(0,l.Z)({display:"flex",alignItems:"center"},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16,marginInlineStart:-8})},"&-wide":{maxWidth:1152,margin:"0 auto"},"&-logo":{position:"relative",display:"flex",height:"100%",alignItems:"center",overflow:"hidden","> *:first-child":{display:"flex",alignItems:"center",minHeight:"22px",fontSize:"22px"},"> *:first-child > img":{display:"inline-block",height:"32px",verticalAlign:"middle"},"> *:first-child > h1":{display:"inline-block",marginBlock:0,marginInline:0,lineHeight:"24px",marginInlineStart:6,fontWeight:"600",fontSize:"16px",color:(n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorHeaderTitle,verticalAlign:"top"}},"&-menu":{minWidth:0,display:"flex",alignItems:"center",paddingInline:6,paddingBlock:6,lineHeight:"".concat(Math.max((((t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56)-12,40),"px")}})};function na(r){return(0,Ne.Xj)("ProLayoutTopNavHeader",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[ea(n)]})}var Vt=function(e){var n,t,o,i,a,c,u,f=(0,p.useRef)(null),g=e.onMenuHeaderClick,x=e.contentWidth,b=e.rightContentRender,R=e.className,V=e.style,w=e.headerContentRender,O=e.layout,Y=e.actionsRender,K=(0,p.useContext)(He.ZP.ConfigContext),q=K.getPrefixCls,L=(0,p.useContext)(ie.L_),X=L.dark,M="".concat(e.prefixCls||q("pro"),"-top-nav-header"),B=na(M),ce=B.wrapSSR,Q=B.hashId,z=void 0;e.menuHeaderRender!==void 0?z="menuHeaderRender":(O==="mix"||O==="top")&&(z="headerTitleRender");var fe=tt((0,d.Z)((0,d.Z)({},e),{},{collapsed:!1}),z),Ce=(0,p.useContext)(ie.L_),E=Ce.token,xe=(0,p.useMemo)(function(){var T,de,ge,Pe,he,Re,Ee,Me,Be,Ze,je,$e,ke,Ge=(0,s.jsx)(He.ZP,{theme:{hashed:(0,ie.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"},Menu:(0,d.Z)({},H({colorItemBg:((T=E.layout)===null||T===void 0||(T=T.header)===null||T===void 0?void 0:T.colorBgHeader)||"transparent",colorSubItemBg:((de=E.layout)===null||de===void 0||(de=de.header)===null||de===void 0?void 0:de.colorBgHeader)||"transparent",radiusItem:E.borderRadius,colorItemBgSelected:((ge=E.layout)===null||ge===void 0||(ge=ge.header)===null||ge===void 0?void 0:ge.colorBgMenuItemSelected)||(E==null?void 0:E.colorBgTextHover),itemHoverBg:((Pe=E.layout)===null||Pe===void 0||(Pe=Pe.header)===null||Pe===void 0?void 0:Pe.colorBgMenuItemHover)||(E==null?void 0:E.colorBgTextHover),colorItemBgSelectedHorizontal:((he=E.layout)===null||he===void 0||(he=he.header)===null||he===void 0?void 0:he.colorBgMenuItemSelected)||(E==null?void 0:E.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:((Re=E.layout)===null||Re===void 0||(Re=Re.header)===null||Re===void 0?void 0:Re.colorTextMenu)||(E==null?void 0:E.colorTextSecondary),colorItemTextHoverHorizontal:((Ee=E.layout)===null||Ee===void 0||(Ee=Ee.header)===null||Ee===void 0?void 0:Ee.colorTextMenuActive)||(E==null?void 0:E.colorText),colorItemTextSelectedHorizontal:((Me=E.layout)===null||Me===void 0||(Me=Me.header)===null||Me===void 0?void 0:Me.colorTextMenuSelected)||(E==null?void 0:E.colorTextBase),horizontalItemBorderRadius:4,colorItemTextHover:((Be=E.layout)===null||Be===void 0||(Be=Be.header)===null||Be===void 0?void 0:Be.colorTextMenuActive)||"rgba(0, 0, 0, 0.85)",horizontalItemHoverBg:((Ze=E.layout)===null||Ze===void 0||(Ze=Ze.header)===null||Ze===void 0?void 0:Ze.colorBgMenuItemHover)||"rgba(0, 0, 0, 0.04)",colorItemTextSelected:((je=E.layout)===null||je===void 0||(je=je.header)===null||je===void 0?void 0:je.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:E==null?void 0:E.colorBgElevated,subMenuItemBg:E==null?void 0:E.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:E==null?void 0:E.colorBgElevated}))},token:{colorBgElevated:(($e=E.layout)===null||$e===void 0||($e=$e.header)===null||$e===void 0?void 0:$e.colorBgHeader)||"transparent"}},children:(0,s.jsx)(Ft,(0,d.Z)((0,d.Z)((0,d.Z)({theme:X?"dark":"light"},e),{},{className:"".concat(M,"-base-menu ").concat(Q).trim()},e.menuProps),{},{style:(0,d.Z)({width:"100%"},(ke=e.menuProps)===null||ke===void 0?void 0:ke.style),collapsed:!1,menuRenderType:"header",mode:"horizontal"}))});return w?w(e,Ge):Ge},[(n=E.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorBgHeader,(t=E.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorBgMenuItemSelected,(o=E.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorBgMenuItemHover,(i=E.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorTextMenu,(a=E.layout)===null||a===void 0||(a=a.header)===null||a===void 0?void 0:a.colorTextMenuActive,(c=E.layout)===null||c===void 0||(c=c.header)===null||c===void 0?void 0:c.colorTextMenuSelected,(u=E.layout)===null||u===void 0||(u=u.header)===null||u===void 0?void 0:u.colorBgMenuElevated,E.borderRadius,E==null?void 0:E.colorBgTextHover,E==null?void 0:E.colorTextSecondary,E==null?void 0:E.colorText,E==null?void 0:E.colorTextBase,E.colorBgElevated,X,e,M,Q,w]);return ce((0,s.jsx)("div",{className:me()(M,Q,R,(0,l.Z)({},"".concat(M,"-light"),!0)),style:V,children:(0,s.jsxs)("div",{ref:f,className:me()("".concat(M,"-main"),Q,(0,l.Z)({},"".concat(M,"-wide"),x==="Fixed"&&O==="top")),children:[fe&&(0,s.jsxs)("div",{className:me()("".concat(M,"-main-left ").concat(Q)),onClick:g,children:[(0,s.jsx)(nt,(0,d.Z)({},e)),(0,s.jsx)("div",{className:"".concat(M,"-logo ").concat(Q).trim(),id:"logo",children:fe},"logo")]}),(0,s.jsx)("div",{style:{flex:1},className:"".concat(M,"-menu ").concat(Q).trim(),children:xe}),(b||Y||e.avatarProps)&&(0,s.jsx)($t,(0,d.Z)((0,d.Z)({rightContentRender:b},e),{},{prefixCls:M}))]})}))},ta=function(e){var n,t,o;return(0,l.Z)({},e.componentCls,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({position:"relative",background:"transparent",display:"flex",alignItems:"center",marginBlock:0,marginInline:16,height:((n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56,boxSizing:"border-box","> a":{height:"100%"}},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16}),"&-collapsed-button",{minHeight:"22px",color:(t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorHeaderTitle,fontSize:"18px",marginInlineEnd:"16px"}),"&-logo",{position:"relative",marginInlineEnd:"16px",a:{display:"flex",alignItems:"center",height:"100%",minHeight:"22px",fontSize:"20px"},img:{height:"28px"},h1:{height:"32px",marginBlock:0,marginInline:0,marginInlineStart:8,fontWeight:"600",color:((o=e.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorHeaderTitle)||e.colorTextHeading,fontSize:"18px",lineHeight:"32px"},"&-mix":{display:"flex",alignItems:"center"}}),"&-logo-mobile",{minWidth:"24px",marginInlineEnd:0}))};function ra(r){return(0,Ne.Xj)("ProLayoutGlobalHeader",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[ta(n)]})}var oa=function(e,n){return e===!1?null:e?e(n,null):n},aa=function(e){var n=e.isMobile,t=e.logo,o=e.collapsed,i=e.onCollapse,a=e.rightContentRender,c=e.menuHeaderRender,u=e.onMenuHeaderClick,f=e.className,g=e.style,x=e.layout,b=e.children,R=e.splitMenus,V=e.menuData,w=e.prefixCls,O=(0,p.useContext)(He.ZP.ConfigContext),Y=O.getPrefixCls,K=O.direction,q="".concat(w||Y("pro"),"-global-header"),L=ra(q),X=L.wrapSSR,M=L.hashId,B=me()(f,q,M);if(x==="mix"&&!n&&R){var ce=(V||[]).map(function(Ce){return(0,d.Z)((0,d.Z)({},Ce),{},{children:void 0,routes:void 0})}),Q=Zn(ce);return(0,s.jsx)(Vt,(0,d.Z)((0,d.Z)({mode:"horizontal"},e),{},{splitMenus:!1,menuData:Q}))}var z=me()("".concat(q,"-logo"),M,(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(q,"-logo-rtl"),K==="rtl"),"".concat(q,"-logo-mix"),x==="mix"),"".concat(q,"-logo-mobile"),n)),fe=(0,s.jsx)("span",{className:z,children:(0,s.jsx)("a",{children:et(t)})},"logo");return X((0,s.jsxs)("div",{className:B,style:(0,d.Z)({},g),children:[n&&(0,s.jsx)("span",{className:"".concat(q,"-collapsed-button ").concat(M).trim(),onClick:function(){i==null||i(!o)},children:(0,s.jsx)(po,{})}),n&&oa(c,fe),x==="mix"&&!n&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(nt,(0,d.Z)({},e)),(0,s.jsx)("div",{className:z,onClick:u,children:tt((0,d.Z)((0,d.Z)({},e),{},{collapsed:!1}),"headerTitleRender")})]}),(0,s.jsx)("div",{style:{flex:1},children:b}),(a||e.actionsRender||e.avatarProps)&&(0,s.jsx)($t,(0,d.Z)({rightContentRender:a},e))]}))},ia=function(e){var n,t,o,i;return(0,l.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,l.Z)({},"".concat(e.antCls,"-layout-header").concat(e.componentCls),{height:((n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56,lineHeight:"".concat(((t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56,"px"),zIndex:19,width:"100%",paddingBlock:0,paddingInline:0,borderBlockEnd:"1px solid ".concat(e.colorSplit),backgroundColor:((o=e.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorBgHeader)||"rgba(255, 255, 255, 0.4)",WebkitBackdropFilter:"blur(8px)",backdropFilter:"blur(8px)",transition:"background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)","&-fixed-header":{position:"fixed",insetBlockStart:0,width:"100%",zIndex:100,insetInlineEnd:0},"&-fixed-header-scroll":{backgroundColor:((i=e.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorBgScrollHeader)||"rgba(255, 255, 255, 0.8)"},"&-header-actions":{display:"flex",alignItems:"center",fontSize:"16",cursor:"pointer","& &-item":{paddingBlock:0,paddingInline:8,"&:hover":{color:e.colorText}}},"&-header-realDark":{boxShadow:"0 2px 8px 0 rgba(0, 0, 0, 65%)"},"&-header-actions-header-action":{transition:"width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"}}))};function la(r){return(0,Ne.Xj)("ProLayoutHeader",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[ia(n)]})}function sa(r,e){var n=e.stylish,t=e.proLayoutCollapsedWidth;return(0,Ne.Xj)("ProLayoutHeaderStylish",function(o){var i=(0,d.Z)((0,d.Z)({},o),{},{componentCls:".".concat(r),proLayoutCollapsedWidth:t});return n?[(0,l.Z)({},"div".concat(o.proComponentsCls,"-layout"),(0,l.Z)({},"".concat(i.componentCls),n==null?void 0:n(i)))]:[]})}var zt=en.Z.Header,ca=function(e){var n,t,o,i=e.isMobile,a=e.fixedHeader,c=e.className,u=e.style,f=e.collapsed,g=e.prefixCls,x=e.onCollapse,b=e.layout,R=e.headerRender,V=e.headerContentRender,w=(0,p.useContext)(ie.L_),O=w.token,Y=(0,p.useContext)(He.ZP.ConfigContext),K=(0,p.useState)(!1),q=(0,j.Z)(K,2),L=q[0],X=q[1],M=a||b==="mix",B=(0,p.useCallback)(function(){var T=b==="top",de=Zn(e.menuData||[]),ge=(0,s.jsx)(aa,(0,d.Z)((0,d.Z)({onCollapse:x},e),{},{menuData:de,children:V&&V(e,null)}));return T&&!i&&(ge=(0,s.jsx)(Vt,(0,d.Z)((0,d.Z)({mode:"horizontal",onCollapse:x},e),{},{menuData:de}))),R&&typeof R=="function"?R(e,ge):ge},[V,R,i,b,x,e]);(0,p.useEffect)(function(){var T,de=(Y==null||(T=Y.getTargetContainer)===null||T===void 0?void 0:T.call(Y))||document.body,ge=function(){var he,Re=de.scrollTop;return Re>(((he=O.layout)===null||he===void 0||(he=he.header)===null||he===void 0?void 0:he.heightLayoutHeader)||56)&&!L?(X(!0),!0):(L&&X(!1),!1)};if(M&&typeof window!="undefined")return de.addEventListener("scroll",ge,{passive:!0}),function(){de.removeEventListener("scroll",ge)}},[(n=O.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader,M,L]);var ce=b==="top",Q="".concat(g,"-layout-header"),z=la(Q),fe=z.wrapSSR,Ce=z.hashId,E=sa("".concat(Q,".").concat(Q,"-stylish"),{proLayoutCollapsedWidth:64,stylish:e.stylish}),xe=me()(c,Ce,Q,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(Q,"-fixed-header"),M),"".concat(Q,"-fixed-header-scroll"),L),"".concat(Q,"-mix"),b==="mix"),"".concat(Q,"-fixed-header-action"),!f),"".concat(Q,"-top-menu"),ce),"".concat(Q,"-header"),!0),"".concat(Q,"-stylish"),!!e.stylish));return b==="side"&&!i?null:E.wrapSSR(fe((0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(He.ZP,{theme:{hashed:(0,ie.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"}}},children:[M&&(0,s.jsx)(zt,{style:(0,d.Z)({height:((t=O.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56,lineHeight:"".concat(((o=O.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.heightLayoutHeader)||56,"px"),backgroundColor:"transparent",zIndex:19},u)}),(0,s.jsx)(zt,{className:xe,style:u,children:B()})]})})))},ua=m(83832),da=m(85265),ma=m(11568),Kt=new ma.E4("antBadgeLoadingCircle",{"0%":{display:"none",opacity:0,overflow:"hidden"},"80%":{overflow:"hidden"},"100%":{display:"unset",opacity:1}}),pa=function(e){var n,t,o,i,a,c,u,f,g,x,b,R;return(0,l.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-layout-sider").concat(e.componentCls),{background:((n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorMenuBackground)||"transparent"}),e.componentCls,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({position:"relative",boxSizing:"border-box","&-menu":{position:"relative",zIndex:10,minHeight:"100%"}},"& ".concat(e.antCls,"-layout-sider-children"),{position:"relative",display:"flex",flexDirection:"column",height:"100%",paddingInline:(t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.paddingInlineLayoutMenu,paddingBlock:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.paddingBlockLayoutMenu,borderInlineEnd:"1px solid ".concat(e.colorSplit),marginInlineEnd:-1}),"".concat(e.antCls,"-menu"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:e.fontSizeSM,paddingBottom:4}),"".concat(e.antCls,"-menu-item:not(").concat(e.antCls,"-menu-item-selected):hover"),{color:(i=e.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.colorTextMenuItemHover})),"&-logo",{position:"relative",display:"flex",alignItems:"center",justifyContent:"space-between",paddingInline:12,paddingBlock:16,color:(a=e.layout)===null||a===void 0||(a=a.sider)===null||a===void 0?void 0:a.colorTextMenu,cursor:"pointer",borderBlockEnd:"1px solid ".concat((c=e.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorMenuItemDivider),"> a":{display:"flex",alignItems:"center",justifyContent:"center",minHeight:22,fontSize:22,"> img":{display:"inline-block",height:22,verticalAlign:"middle"},"> h1":{display:"inline-block",height:22,marginBlock:0,marginInlineEnd:0,marginInlineStart:6,color:(u=e.layout)===null||u===void 0||(u=u.sider)===null||u===void 0?void 0:u.colorTextMenuTitle,animationName:Kt,animationDuration:".4s",animationTimingFunction:"ease",fontWeight:600,fontSize:16,lineHeight:"22px",verticalAlign:"middle"}},"&-collapsed":(0,l.Z)({flexDirection:"column-reverse",margin:0,padding:12},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginBlockEnd:8,fontSize:16,transition:"font-size 0.2s ease-in-out,color 0.2s ease-in-out"})}),"&-actions",{display:"flex",alignItems:"center",justifyContent:"space-between",marginBlock:4,marginInline:0,color:(f=e.layout)===null||f===void 0||(f=f.sider)===null||f===void 0?void 0:f.colorTextMenu,"&-collapsed":{flexDirection:"column-reverse",paddingBlock:0,paddingInline:8,fontSize:16,transition:"font-size 0.3s ease-in-out"},"&-list":{color:(g=e.layout)===null||g===void 0||(g=g.sider)===null||g===void 0?void 0:g.colorTextMenuSecondary,"&-collapsed":{marginBlockEnd:8,animationName:"none"},"&-item":{paddingInline:6,paddingBlock:6,lineHeight:"16px",fontSize:16,cursor:"pointer",borderRadius:e.borderRadius,"&:hover":{background:e.colorBgTextHover}}},"&-avatar":{fontSize:14,paddingInline:8,paddingBlock:8,display:"flex",alignItems:"center",gap:e.marginXS,borderRadius:e.borderRadius,"& *":{cursor:"pointer"},"&:hover":{background:e.colorBgTextHover}}}),"&-hide-menu-collapsed",{insetInlineStart:"-".concat(e.proLayoutCollapsedWidth-12,"px"),position:"absolute"}),"&-extra",{marginBlockEnd:16,marginBlock:0,marginInline:16,"&-no-logo":{marginBlockStart:16}}),"&-links",{width:"100%",ul:{height:"auto"}}),"&-link-menu",{border:"none",boxShadow:"none",background:"transparent"}),"&-footer",{color:(x=e.layout)===null||x===void 0||(x=x.sider)===null||x===void 0?void 0:x.colorTextMenuSecondary,paddingBlockEnd:16,fontSize:e.fontSize,animationName:Kt,animationDuration:".4s",animationTimingFunction:"ease"})),"".concat(e.componentCls).concat(e.componentCls,"-fixed"),{position:"fixed",insetBlockStart:0,insetInlineStart:0,zIndex:"100",height:"100%","&-mix":{height:"calc(100% - ".concat(((b=e.layout)===null||b===void 0||(b=b.header)===null||b===void 0?void 0:b.heightLayoutHeader)||56,"px)"),insetBlockStart:"".concat(((R=e.layout)===null||R===void 0||(R=R.header)===null||R===void 0?void 0:R.heightLayoutHeader)||56,"px")}}))};function fa(r,e){var n=e.proLayoutCollapsedWidth;return(0,Ne.Xj)("ProLayoutSiderMenu",function(t){var o=(0,d.Z)((0,d.Z)({},t),{},{componentCls:".".concat(r),proLayoutCollapsedWidth:n});return[pa(o)]})}var kt=function(e){var n,t=e.isMobile,o=e.siderWidth,i=e.collapsed,a=e.onCollapse,c=e.style,u=e.className,f=e.hide,g=e.prefixCls,x=e.getContainer,b=(0,p.useContext)(ie.L_),R=b.token;(0,p.useEffect)(function(){t===!0&&(a==null||a(!0))},[t]);var V=(0,yt.Z)(e,["className","style"]),w=p.useContext(He.ZP.ConfigContext),O=w.direction,Y=fa("".concat(g,"-sider"),{proLayoutCollapsedWidth:64}),K=Y.wrapSSR,q=Y.hashId,L=me()("".concat(g,"-sider"),u,q);if(f)return null;var X=(0,S.X)(!i,function(){return a==null?void 0:a(!0)});return K(t?(0,s.jsx)(da.Z,(0,d.Z)((0,d.Z)({placement:O==="rtl"?"right":"left",className:me()("".concat(g,"-drawer-sider"),u)},X),{},{style:(0,d.Z)({padding:0,height:"100vh"},c),onClose:function(){a==null||a(!0)},maskClosable:!0,closable:!1,getContainer:x||!1,width:o,styles:{body:{height:"100vh",padding:0,display:"flex",flexDirection:"row",backgroundColor:(n=R.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorMenuBackground}},children:(0,s.jsx)(Ut,(0,d.Z)((0,d.Z)({},V),{},{isMobile:!0,className:L,collapsed:t?!1:i,splitMenus:!1,originCollapsed:i}))})):(0,s.jsx)(Ut,(0,d.Z)((0,d.Z)({className:L,originCollapsed:i},V),{},{style:c})))},Gt=m(76509),rt=m(16305),va=function(e,n,t){if(t){var o=(0,wn.Z)(t.keys()).find(function(a){try{return a.startsWith("http")?!1:(0,rt.EQ)(a)(e)}catch(c){return console.log("key",a,c),!1}});if(o)return t.get(o)}if(n){var i=Object.keys(n).find(function(a){try{return a!=null&&a.startsWith("http")?!1:(0,rt.EQ)(a)(e)}catch(c){return console.log("key",a,c),!1}});if(i)return n[i]}return{path:""}},ot=function(e,n){var t=e.pathname,o=t===void 0?"/":t,i=e.breadcrumb,a=e.breadcrumbMap,c=e.formatMessage,u=e.title,f=e.menu,g=f===void 0?{locale:!1}:f,x=n?"":u||"",b=va(o,i,a);if(!b)return{title:x,id:"",pageName:x};var R=b.name;return g.locale!==!1&&b.locale&&c&&(R=c({id:b.locale||"",defaultMessage:b.name})),R?n||!u?{title:R,id:b.locale||"",pageName:R}:{title:"".concat(R," - ").concat(u),id:b.locale||"",pageName:R}:{title:x,id:b.locale||"",pageName:x}},Ci=function(e,n){return ot(e,n).title},ga={"app.setting.pagestyle":"Page style setting","app.setting.pagestyle.dark":"Dark Menu style","app.setting.pagestyle.light":"Light Menu style","app.setting.pagestyle.realdark":"Dark style (Beta)","app.setting.content-width":"Content Width","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.themecolor":"Theme Color","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blue (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.sidermenutype":"SideMenu Type","app.setting.sidermenutype-sub":"Classic","app.setting.sidermenutype-group":"Grouping","app.setting.navigationmode":"Navigation Mode","app.setting.regionalsettings":"Regional Settings","app.setting.regionalsettings.header":"Header","app.setting.regionalsettings.menu":"Menu","app.setting.regionalsettings.footer":"Footer","app.setting.regionalsettings.menuHeader":"Menu Header","app.setting.sidemenu":"Side Menu Layout","app.setting.topmenu":"Top Menu Layout","app.setting.mixmenu":"Mix Menu Layout","app.setting.splitMenus":"Split Menus","app.setting.fixedheader":"Fixed Header","app.setting.fixedsidebar":"Fixed Sidebar","app.setting.fixedsidebar.hint":"Works on Side Menu Layout","app.setting.hideheader":"Hidden Header when scrolling","app.setting.hideheader.hint":"Works when Hidden Header is enabled","app.setting.othersettings":"Other Settings","app.setting.weakmode":"Weak Mode","app.setting.copy":"Copy Setting","app.setting.loading":"Loading theme","app.setting.copyinfo":"copy success\uFF0Cplease replace defaultSettings in src/models/setting.js","app.setting.production.hint":"Setting panel shows in development environment only, please manually modify"},ha=(0,d.Z)({},ga),ya={"app.setting.pagestyle":"Impostazioni di stile","app.setting.pagestyle.dark":"Tema scuro","app.setting.pagestyle.light":"Tema chiaro","app.setting.content-width":"Largezza contenuto","app.setting.content-width.fixed":"Fissa","app.setting.content-width.fluid":"Fluida","app.setting.themecolor":"Colore del tema","app.setting.themecolor.dust":"Rosso polvere","app.setting.themecolor.volcano":"Vulcano","app.setting.themecolor.sunset":"Arancione tramonto","app.setting.themecolor.cyan":"Ciano","app.setting.themecolor.green":"Verde polare","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Blu cielo mattutino","app.setting.themecolor.geekblue":"Blu geek","app.setting.themecolor.purple":"Viola dorato","app.setting.navigationmode":"Modalit\xE0 di navigazione","app.setting.sidemenu":"Menu laterale","app.setting.topmenu":"Menu in testata","app.setting.mixmenu":"Menu misto","app.setting.splitMenus":"Menu divisi","app.setting.fixedheader":"Testata fissa","app.setting.fixedsidebar":"Menu laterale fisso","app.setting.fixedsidebar.hint":"Solo se selezionato Menu laterale","app.setting.hideheader":"Nascondi testata durante lo scorrimento","app.setting.hideheader.hint":"Solo se abilitato Nascondi testata durante lo scorrimento","app.setting.othersettings":"Altre impostazioni","app.setting.weakmode":"Inverti colori","app.setting.copy":"Copia impostazioni","app.setting.loading":"Carico tema...","app.setting.copyinfo":"Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js","app.setting.production.hint":"Questo pannello \xE8 visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente"},Ca=(0,d.Z)({},ya),xa={"app.setting.pagestyle":"\uC2A4\uD0C0\uC77C \uC124\uC815","app.setting.pagestyle.dark":"\uB2E4\uD06C \uBAA8\uB4DC","app.setting.pagestyle.light":"\uB77C\uC774\uD2B8 \uBAA8\uB4DC","app.setting.content-width":"\uCEE8\uD150\uCE20 \uB108\uBE44","app.setting.content-width.fixed":"\uACE0\uC815","app.setting.content-width.fluid":"\uD750\uB984","app.setting.themecolor":"\uD14C\uB9C8 \uC0C9\uC0C1","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"\uB124\uBE44\uAC8C\uC774\uC158 \uBAA8\uB4DC","app.setting.regionalsettings":"\uC601\uC5ED\uBCC4 \uC124\uC815","app.setting.regionalsettings.header":"\uD5E4\uB354","app.setting.regionalsettings.menu":"\uBA54\uB274","app.setting.regionalsettings.footer":"\uBC14\uB2E5\uAE00","app.setting.regionalsettings.menuHeader":"\uBA54\uB274 \uD5E4\uB354","app.setting.sidemenu":"\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58","app.setting.topmenu":"\uBA54\uB274 \uC0C1\uB2E8 \uBC30\uCE58","app.setting.mixmenu":"\uD63C\uD569\uD615 \uBC30\uCE58","app.setting.splitMenus":"\uBA54\uB274 \uBD84\uB9AC","app.setting.fixedheader":"\uD5E4\uB354 \uACE0\uC815","app.setting.fixedsidebar":"\uC0AC\uC774\uB4DC\uBC14 \uACE0\uC815","app.setting.fixedsidebar.hint":"'\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58'\uB97C \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.hideheader":"\uC2A4\uD06C\uB864 \uC911 \uD5E4\uB354 \uAC10\uCD94\uAE30","app.setting.hideheader.hint":"'\uD5E4\uB354 \uAC10\uCD94\uAE30 \uC635\uC158'\uC744 \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.othersettings":"\uB2E4\uB978 \uC124\uC815","app.setting.weakmode":"\uACE0\uB300\uBE44 \uBAA8\uB4DC","app.setting.copy":"\uC124\uC815\uAC12 \uBCF5\uC0AC","app.setting.loading":"\uD14C\uB9C8 \uB85C\uB529 \uC911","app.setting.copyinfo":"\uBCF5\uC0AC \uC131\uACF5. src/models/settings.js\uC5D0 \uC788\uB294 defaultSettings\uB97C \uAD50\uCCB4\uD574 \uC8FC\uC138\uC694.","app.setting.production.hint":"\uC124\uC815 \uD310\uB12C\uC740 \uAC1C\uBC1C \uD658\uACBD\uC5D0\uC11C\uB9CC \uBCF4\uC5EC\uC9D1\uB2C8\uB2E4. \uC9C1\uC811 \uC218\uB3D9\uC73C\uB85C \uBCC0\uACBD\uBC14\uB78D\uB2C8\uB2E4."},ba=(0,d.Z)({},xa),Sa={"app.setting.pagestyle":"\u6574\u4F53\u98CE\u683C\u8BBE\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98CE\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.content-width":"\u5185\u5BB9\u533A\u57DF\u5BBD\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BBD","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u9898\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6781\u5149\u7EFF","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8BA4\uFF09","app.setting.themecolor.daybreak":"\u62C2\u6653","app.setting.themecolor.geekblue":"\u6781\u5BA2\u84DD","app.setting.themecolor.purple":"\u9171\u7D2B","app.setting.navigationmode":"\u5BFC\u822A\u6A21\u5F0F","app.setting.sidermenutype":"\u4FA7\u8FB9\u83DC\u5355\u7C7B\u578B","app.setting.sidermenutype-sub":"\u7ECF\u5178\u6A21\u5F0F","app.setting.sidermenutype-group":"\u5206\u7EC4\u6A21\u5F0F","app.setting.regionalsettings":"\u5185\u5BB9\u533A\u57DF","app.setting.regionalsettings.header":"\u9876\u680F","app.setting.regionalsettings.menu":"\u83DC\u5355","app.setting.regionalsettings.footer":"\u9875\u811A","app.setting.regionalsettings.menuHeader":"\u83DC\u5355\u5934","app.setting.sidemenu":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40","app.setting.topmenu":"\u9876\u90E8\u83DC\u5355\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u5355\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u4FA7\u8FB9\u83DC\u5355","app.setting.fixedsidebar.hint":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40\u65F6\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u65F6\u9690\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u65F6\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8BBE\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8D1D\u8BBE\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F7D\u4E3B\u9898","app.setting.copyinfo":"\u62F7\u8D1D\u6210\u529F\uFF0C\u8BF7\u5230 src/defaultSettings.js \u4E2D\u66FF\u6362\u9ED8\u8BA4\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u680F\u53EA\u5728\u5F00\u53D1\u73AF\u5883\u7528\u4E8E\u9884\u89C8\uFF0C\u751F\u4EA7\u73AF\u5883\u4E0D\u4F1A\u5C55\u73B0\uFF0C\u8BF7\u62F7\u8D1D\u540E\u624B\u52A8\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},_a=(0,d.Z)({},Sa),Pa={"app.setting.pagestyle":"\u6574\u9AD4\u98A8\u683C\u8A2D\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u55AE\u98A8\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98A8\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u55AE\u98A8\u683C","app.setting.content-width":"\u5167\u5BB9\u5340\u57DF\u5BEC\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BEC","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u984C\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6975\u5149\u7DA0","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8A8D\uFF09","app.setting.themecolor.daybreak":"\u62C2\u66C9\u85CD","app.setting.themecolor.geekblue":"\u6975\u5BA2\u85CD","app.setting.themecolor.purple":"\u91AC\u7D2B","app.setting.navigationmode":"\u5C0E\u822A\u6A21\u5F0F","app.setting.sidemenu":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40","app.setting.topmenu":"\u9802\u90E8\u83DC\u55AE\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u55AE\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u5074\u908A\u83DC\u55AE","app.setting.fixedsidebar.hint":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40\u6642\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u6642\u96B1\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u6642\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8A2D\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8C9D\u8A2D\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F09\u4E3B\u984C","app.setting.copyinfo":"\u62F7\u8C9D\u6210\u529F\uFF0C\u8ACB\u5230 src/defaultSettings.js \u4E2D\u66FF\u63DB\u9ED8\u8A8D\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u6B04\u53EA\u5728\u958B\u767C\u74B0\u5883\u7528\u65BC\u9810\u89BD\uFF0C\u751F\u7522\u74B0\u5883\u4E0D\u6703\u5C55\u73FE\uFF0C\u8ACB\u62F7\u8C9D\u5F8C\u624B\u52D5\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},Ea=(0,d.Z)({},Pa),Xt={"zh-CN":_a,"zh-TW":Ea,"en-US":ha,"it-IT":Ca,"ko-KR":ba},Ma=function(){if(!(0,N.j)())return"zh-CN";var e=window.localStorage.getItem("umi_locale");return e||window.g_locale||navigator.language},Ia=function(){var e=Ma();return Xt[e]||Xt["zh-CN"]},Ln=m(67159),rn=m(34155),Da=function(){var e;return typeof rn=="undefined"?Ln.Z:((e=rn)===null||rn===void 0||(rn={ALLUSERSPROFILE:"C:\\ProgramData",APPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133655684414351508",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_26212_KVVCMVWIBAPLZQEB",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"\u5C0F\u4E38\u728A\u5B50",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",CUDA_PATH:"D:\\CUDA",CUDA_PATH_V11_0:"D:\\CUDA",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_6444_1262719628:"1",EFC_6444_1592913036:"1",EFC_6444_2283032206:"1",EFC_6444_2775293581:"1",EFC_6444_3789132940:"1",ELINK_INSTALL_PATH:"C:\\Program Files (x86)\\Ecloud\\CloudComputer\\drivers\\CMSS",FPS_BROWSER_APP_PROFILE_STRING:"Internet Explorer",FPS_BROWSER_USER_PROFILE_STRING:"Default",GIT_ASKPASS:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",HOME:"C:\\Users\\\<USER>\u82CF",HOMEDRIVE:"C:",HOMEPATH:"\\Users\\\u82CF\u82CF",INIT_CWD:"D:\\project\\web_app_0527v2\\web",JAVA_HOME:"D:\\Program Files\\javajdk",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Local",LOGONSERVER:"\\\\\u5C0F\u4E38\u728A\u5B50",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"production",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NODE_PATH:"D:\\\u524D\u7AEF\\nodejs\\node_modules",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_cache:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\npm-cache",npm_config_globalconfig:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\\<USER>\u82CF\\.npm-init.js",npm_config_local_prefix:"D:\\project\\web_app_0527v2\\web",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_noproxy:"",npm_config_npm_version:"10.7.0",npm_config_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_userconfig:"C:\\Users\\\<USER>\u82CF\\.npmrc",npm_config_user_agent:"npm/10.7.0 node/v20.15.1 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build:bj",npm_lifecycle_script:"cross-env REACT_APP_ENV=bj UMI_ENV=bj max build",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_engines_node:">=12.0.0",npm_package_json:"D:\\project\\web_app_0527v2\\web\\package.json",npm_package_name:"ant-design-pro",npm_package_version:"6.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\\<USER>\u5FDA\u5AC3\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"16",NVCUDASAMPLES11_0_ROOT:"D:\\CUDA",NVCUDASAMPLES_ROOT:"D:\\CUDA",NVTOOLSEXT_PATH:"C:\\Program Files\\NVIDIA Corporation\\NvToolsExt\\",OneDrive:"C:\\Users\\\<USER>\u82CF\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"D:\\project\\web_app_0527v2\\web\\node_modules\\.bin;D:\\project\\web_app_0527v2\\node_modules\\.bin;D:\\project\\node_modules\\.bin;D:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;D:\\Program Files\\javajdk\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;D:\\CUDA\\bin;D:\\CUDA\\libnvvp;D:\\Anaconda;D:\\Anaconda\\Library\\mingw-w64\\bin;D:\\Anaconda\\Library\\usr\\bin;D:\\Anaconda\\Library\\bin;D:\\Anaconda\\Scripts;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\\u524D\u7AEF\\nodejs\\node_global;D;\\\u524D\u7AEF\\mongoDB\\server\\bin;C;\\Program Files\\NVIDIA Corporation\\Nsight Compute 2020.1.1\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;D:\\\u524D\u7AEF\\\u9879\u76EE\\2\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\\\u5FAE\u4FE1web\u5F00\u53D1\u8005\u5DE5\u5177\\dll;C:\\Program File;\\MySQL\\MySQL Server 8.0\\bin;C:\\Program Files (x86)\\PuTTY\\;D:\\Program Files\\TortoiseGit\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\Program\\Git\\cmd;D:\\Program Files\\javajdk\\bin;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\;C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Microsoft\\WindowsApps;D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;;D:\\\u524D\u7AEF\\nodejs\\node_global;C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;;C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm;D:\\Program\\Microsoft VS Code\\bin;C:\\MinGW\\bin;D:\\Program\\cursor\\resources\\app\\bin",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD",PROCESSOR_LEVEL:"25",PROCESSOR_REVISION:"5000",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\\<USER>\u82CF\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;",REACT_APP_ENV:"bj",REACT_APP_WEB_SERVER_SOCKET_PORT:"http://*************:9044/",SESSIONNAME:"Console",SystemDrive:"C:",SystemRoot:"C:\\WINDOWS",TEMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.100.2",TMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",UMI_APP_BACKEND_URL:"http://localhost:8001",UMI_DIR:"D:\\project\\web_app_0527v2\\web\\node_modules\\umi",UMI_ENV:"bj",UMI_PRESETS:"D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\max\\dist\\preset.js",USERDOMAIN:"\u5C0F\u4E38\u728A\u5B50",USERDOMAIN_ROAMINGPROFILE:"\u5C0F\u4E38\u728A\u5B50",USERNAME:"\u82CF\u82CF",USERPROFILE:"C:\\Users\\\<USER>\u82CF",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",VSCODE_GIT_ASKPASS_MAIN:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"D:\\Program\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-d0bcb91465-sock",VSCODE_INJECTION:"1",WebStorm:"D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;",windir:"C:\\WINDOWS",ZXCLOUD_INSTALL_PATH:"C:\\Program Files (x86)\\uSmartView",ZXVE_CLIENT_AGENT_UUID:"3577B2A6-F8E2-4987-9B98-4748E6A96F38",ZXVE_IRAI:"C:\\Program Files (x86)\\uSmartView\\client\\uSmartView.exe",ZXVE_NEW_UDS:"0x00000001"})===null||rn===void 0?void 0:rn.ANTD_VERSION)||Ln.Z},Ra=function(e){var n,t,o,i,a,c,u,f,g,x,b,R,V,w,O,Y,K,q,L,X,M,B,ce,Q,z,fe,Ce,E,xe,T,de,ge;return(n=Da())!==null&&n!==void 0&&n.startsWith("5")?{}:(0,l.Z)((0,l.Z)((0,l.Z)({},e.componentCls,(0,l.Z)((0,l.Z)({width:"100%",height:"100%"},"".concat(e.proComponentsCls,"-base-menu"),(M={color:(t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorTextMenu},(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)(M,"".concat(e.antCls,"-menu-sub"),{backgroundColor:"transparent!important",color:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorTextMenu}),"& ".concat(e.antCls,"-layout"),{backgroundColor:"transparent",width:"100%"}),"".concat(e.antCls,"-menu-submenu-expand-icon, ").concat(e.antCls,"-menu-submenu-arrow"),{color:"inherit"}),"&".concat(e.antCls,"-menu"),(0,l.Z)((0,l.Z)({color:(i=e.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.colorTextMenu},"".concat(e.antCls,"-menu-item"),{"*":{transition:"none !important"}}),"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-inline"),(0,l.Z)({},"".concat(e.antCls,"-menu-selected::after,").concat(e.antCls,"-menu-item-selected::after"),{display:"none"})),"".concat(e.antCls,"-menu-sub ").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"}),"&".concat(e.antCls,"-menu-light"),(0,l.Z)({},"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(a=e.layout)===null||a===void 0||(a=a.sider)===null||a===void 0?void 0:a.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(c=e.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorTextMenuActive}))),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:(u=e.layout)===null||u===void 0||(u=u.sider)===null||u===void 0?void 0:u.colorBgMenuItemSelected,borderRadius:e.borderRadius}),"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(f=e.layout)===null||f===void 0||(f=f.sider)===null||f===void 0?void 0:f.colorTextMenuActive,borderRadius:e.borderRadius,backgroundColor:"".concat((g=e.layout)===null||g===void 0||(g=g.header)===null||g===void 0?void 0:g.colorBgMenuItemHover," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(x=e.layout)===null||x===void 0||(x=x.sider)===null||x===void 0?void 0:x.colorTextMenuActive}))),"".concat(e.antCls,"-menu-item-selected"),{color:(b=e.layout)===null||b===void 0||(b=b.sider)===null||b===void 0?void 0:b.colorTextMenuSelected}),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)(M,"".concat(e.antCls,"-menu-submenu-selected"),{color:(R=e.layout)===null||R===void 0||(R=R.sider)===null||R===void 0?void 0:R.colorTextMenuSelected}),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-inline) ").concat(e.antCls,"-menu-submenu-open"),{color:(V=e.layout)===null||V===void 0||(V=V.sider)===null||V===void 0?void 0:V.colorTextMenuSelected}),"&".concat(e.antCls,"-menu-vertical"),(0,l.Z)({},"".concat(e.antCls,"-menu-submenu-selected"),{borderRadius:e.borderRadius,color:(w=e.layout)===null||w===void 0||(w=w.sider)===null||w===void 0?void 0:w.colorTextMenuSelected})),"".concat(e.antCls,"-menu-submenu:hover > ").concat(e.antCls,"-menu-submenu-title > ").concat(e.antCls,"-menu-submenu-arrow"),{color:(O=e.layout)===null||O===void 0||(O=O.sider)===null||O===void 0?void 0:O.colorTextMenuActive}),"&".concat(e.antCls,"-menu-horizontal"),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"".concat(e.antCls,`-menu-item:hover,
          `).concat(e.antCls,`-menu-submenu:hover,
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-active"),{borderRadius:4,transition:"none",color:(Y=e.layout)===null||Y===void 0||(Y=Y.header)===null||Y===void 0?void 0:Y.colorTextMenuActive,backgroundColor:"".concat((K=e.layout)===null||K===void 0||(K=K.header)===null||K===void 0?void 0:K.colorBgMenuItemHover," !important")}),"".concat(e.antCls,`-menu-item-open,
          `).concat(e.antCls,`-menu-submenu-open,
          `).concat(e.antCls,`-menu-item-selected,
          `).concat(e.antCls,"-menu-submenu-selected"),(0,l.Z)({backgroundColor:(q=e.layout)===null||q===void 0||(q=q.header)===null||q===void 0?void 0:q.colorBgMenuItemSelected,borderRadius:e.borderRadius,transition:"none",color:"".concat((L=e.layout)===null||L===void 0||(L=L.header)===null||L===void 0?void 0:L.colorTextMenuSelected," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:"".concat((X=e.layout)===null||X===void 0||(X=X.header)===null||X===void 0?void 0:X.colorTextMenuSelected," !important")})),"> ".concat(e.antCls,"-menu-item, > ").concat(e.antCls,"-menu-submenu"),{paddingInline:16,marginInline:4}),"> ".concat(e.antCls,"-menu-item::after, > ").concat(e.antCls,"-menu-submenu::after"),{display:"none"})))),"".concat(e.proComponentsCls,"-top-nav-header-base-menu"),(0,l.Z)((0,l.Z)({},"&".concat(e.antCls,"-menu"),(0,l.Z)({color:(B=e.layout)===null||B===void 0||(B=B.header)===null||B===void 0?void 0:B.colorTextMenu},"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-light"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(ce=e.layout)===null||ce===void 0||(ce=ce.header)===null||ce===void 0?void 0:ce.colorTextMenuActive,borderRadius:e.borderRadius,transition:"none",backgroundColor:(Q=e.layout)===null||Q===void 0||(Q=Q.header)===null||Q===void 0?void 0:Q.colorBgMenuItemSelected},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(z=e.layout)===null||z===void 0||(z=z.header)===null||z===void 0?void 0:z.colorTextMenuActive})),"".concat(e.antCls,"-menu-item-selected"),{color:(fe=e.layout)===null||fe===void 0||(fe=fe.header)===null||fe===void 0?void 0:fe.colorTextMenuSelected,borderRadius:e.borderRadius,backgroundColor:(Ce=e.layout)===null||Ce===void 0||(Ce=Ce.header)===null||Ce===void 0?void 0:Ce.colorBgMenuItemSelected})))),"".concat(e.antCls,"-menu-sub").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,"-menu-submenu-popup"),(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"},"".concat(e.antCls,"-menu"),(0,l.Z)({background:"transparent !important",backgroundColor:"transparent !important"},"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"})),"".concat(e.antCls,"-menu-item-selected"),{color:(E=e.layout)===null||E===void 0||(E=E.sider)===null||E===void 0?void 0:E.colorTextMenuSelected}),"".concat(e.antCls,"-menu-submenu-selected"),{color:(xe=e.layout)===null||xe===void 0||(xe=xe.sider)===null||xe===void 0?void 0:xe.colorTextMenuSelected}),"".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:"rgba(0, 0, 0, 0.04)",borderRadius:e.borderRadius,color:(T=e.layout)===null||T===void 0||(T=T.sider)===null||T===void 0?void 0:T.colorTextMenuSelected}),"".concat(e.antCls,`-menu-item:hover, 
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-title:hover"),(0,l.Z)({color:(de=e.layout)===null||de===void 0||(de=de.sider)===null||de===void 0?void 0:de.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(ge=e.layout)===null||ge===void 0||(ge=ge.sider)===null||ge===void 0?void 0:ge.colorTextMenuActive}))))},Oa=function(e){var n,t,o,i;return(0,l.Z)((0,l.Z)({},"".concat(e.antCls,"-layout"),{backgroundColor:"transparent !important"}),e.componentCls,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"& ".concat(e.antCls,"-layout"),{display:"flex",backgroundColor:"transparent",width:"100%"}),"".concat(e.componentCls,"-content"),{display:"flex",flexDirection:"column",width:"100%",backgroundColor:((n=e.layout)===null||n===void 0||(n=n.pageContainer)===null||n===void 0?void 0:n.colorBgPageContainer)||"transparent",position:"relative",paddingBlock:(t=e.layout)===null||t===void 0||(t=t.pageContainer)===null||t===void 0?void 0:t.paddingBlockPageContainerContent,paddingInline:(o=e.layout)===null||o===void 0||(o=o.pageContainer)===null||o===void 0?void 0:o.paddingInlinePageContainerContent,"&-has-page-container":{padding:0}}),"".concat(e.componentCls,"-container"),{width:"100%",display:"flex",flexDirection:"column",minWidth:0,minHeight:0,backgroundColor:"transparent"}),"".concat(e.componentCls,"-bg-list"),{pointerEvents:"none",position:"fixed",overflow:"hidden",insetBlockStart:0,insetInlineStart:0,zIndex:0,height:"100%",width:"100%",background:(i=e.layout)===null||i===void 0?void 0:i.bgLayout}))};function Ta(r){return(0,Ne.Xj)("ProLayout",function(e){var n=(0,d.Z)((0,d.Z)({},e),{},{componentCls:".".concat(r)});return[Oa(n),Ra(n)]})}function Aa(r){if(!r||r==="/")return["/"];var e=r.split("/").filter(function(n){return n});return e.map(function(n,t){return"/".concat(e.slice(0,t+1).join("/"))})}var on=m(34155),Na=function(){var e;return typeof on=="undefined"?Ln.Z:((e=on)===null||on===void 0||(on={ALLUSERSPROFILE:"C:\\ProgramData",APPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133655684414351508",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_26212_KVVCMVWIBAPLZQEB",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"\u5C0F\u4E38\u728A\u5B50",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",CUDA_PATH:"D:\\CUDA",CUDA_PATH_V11_0:"D:\\CUDA",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_6444_1262719628:"1",EFC_6444_1592913036:"1",EFC_6444_2283032206:"1",EFC_6444_2775293581:"1",EFC_6444_3789132940:"1",ELINK_INSTALL_PATH:"C:\\Program Files (x86)\\Ecloud\\CloudComputer\\drivers\\CMSS",FPS_BROWSER_APP_PROFILE_STRING:"Internet Explorer",FPS_BROWSER_USER_PROFILE_STRING:"Default",GIT_ASKPASS:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",HOME:"C:\\Users\\\<USER>\u82CF",HOMEDRIVE:"C:",HOMEPATH:"\\Users\\\u82CF\u82CF",INIT_CWD:"D:\\project\\web_app_0527v2\\web",JAVA_HOME:"D:\\Program Files\\javajdk",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Local",LOGONSERVER:"\\\\\u5C0F\u4E38\u728A\u5B50",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"production",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NODE_PATH:"D:\\\u524D\u7AEF\\nodejs\\node_modules",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_cache:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\npm-cache",npm_config_globalconfig:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\\<USER>\u82CF\\.npm-init.js",npm_config_local_prefix:"D:\\project\\web_app_0527v2\\web",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_noproxy:"",npm_config_npm_version:"10.7.0",npm_config_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_userconfig:"C:\\Users\\\<USER>\u82CF\\.npmrc",npm_config_user_agent:"npm/10.7.0 node/v20.15.1 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build:bj",npm_lifecycle_script:"cross-env REACT_APP_ENV=bj UMI_ENV=bj max build",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_engines_node:">=12.0.0",npm_package_json:"D:\\project\\web_app_0527v2\\web\\package.json",npm_package_name:"ant-design-pro",npm_package_version:"6.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\\<USER>\u5FDA\u5AC3\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"16",NVCUDASAMPLES11_0_ROOT:"D:\\CUDA",NVCUDASAMPLES_ROOT:"D:\\CUDA",NVTOOLSEXT_PATH:"C:\\Program Files\\NVIDIA Corporation\\NvToolsExt\\",OneDrive:"C:\\Users\\\<USER>\u82CF\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"D:\\project\\web_app_0527v2\\web\\node_modules\\.bin;D:\\project\\web_app_0527v2\\node_modules\\.bin;D:\\project\\node_modules\\.bin;D:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;D:\\Program Files\\javajdk\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;D:\\CUDA\\bin;D:\\CUDA\\libnvvp;D:\\Anaconda;D:\\Anaconda\\Library\\mingw-w64\\bin;D:\\Anaconda\\Library\\usr\\bin;D:\\Anaconda\\Library\\bin;D:\\Anaconda\\Scripts;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\\u524D\u7AEF\\nodejs\\node_global;D;\\\u524D\u7AEF\\mongoDB\\server\\bin;C;\\Program Files\\NVIDIA Corporation\\Nsight Compute 2020.1.1\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;D:\\\u524D\u7AEF\\\u9879\u76EE\\2\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\\\u5FAE\u4FE1web\u5F00\u53D1\u8005\u5DE5\u5177\\dll;C:\\Program File;\\MySQL\\MySQL Server 8.0\\bin;C:\\Program Files (x86)\\PuTTY\\;D:\\Program Files\\TortoiseGit\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\Program\\Git\\cmd;D:\\Program Files\\javajdk\\bin;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\;C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Microsoft\\WindowsApps;D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;;D:\\\u524D\u7AEF\\nodejs\\node_global;C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;;C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm;D:\\Program\\Microsoft VS Code\\bin;C:\\MinGW\\bin;D:\\Program\\cursor\\resources\\app\\bin",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD",PROCESSOR_LEVEL:"25",PROCESSOR_REVISION:"5000",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\\<USER>\u82CF\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;",REACT_APP_ENV:"bj",REACT_APP_WEB_SERVER_SOCKET_PORT:"http://*************:9044/",SESSIONNAME:"Console",SystemDrive:"C:",SystemRoot:"C:\\WINDOWS",TEMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.100.2",TMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",UMI_APP_BACKEND_URL:"http://localhost:8001",UMI_DIR:"D:\\project\\web_app_0527v2\\web\\node_modules\\umi",UMI_ENV:"bj",UMI_PRESETS:"D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\max\\dist\\preset.js",USERDOMAIN:"\u5C0F\u4E38\u728A\u5B50",USERDOMAIN_ROAMINGPROFILE:"\u5C0F\u4E38\u728A\u5B50",USERNAME:"\u82CF\u82CF",USERPROFILE:"C:\\Users\\\<USER>\u82CF",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",VSCODE_GIT_ASKPASS_MAIN:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"D:\\Program\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-d0bcb91465-sock",VSCODE_INJECTION:"1",WebStorm:"D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;",windir:"C:\\WINDOWS",ZXCLOUD_INSTALL_PATH:"C:\\Program Files (x86)\\uSmartView",ZXVE_CLIENT_AGENT_UUID:"3577B2A6-F8E2-4987-9B98-4748E6A96F38",ZXVE_IRAI:"C:\\Program Files (x86)\\uSmartView\\client\\uSmartView.exe",ZXVE_NEW_UDS:"0x00000001"})===null||on===void 0?void 0:on.ANTD_VERSION)||Ln.Z},Za=function(e,n,t){var o=e,i=o.breadcrumbName,a=o.title,c=o.path,u=t.findIndex(function(f){return f.linkPath===e.path})===t.length-1;return u?(0,s.jsx)("span",{children:a||i}):(0,s.jsx)("span",{onClick:c?function(){return location.href=c}:void 0,children:a||i})},wa=function(e,n){var t=n.formatMessage,o=n.menu;return e.locale&&t&&(o==null?void 0:o.locale)!==!1?t({id:e.locale,defaultMessage:e.name}):e.name},ja=function(e,n){var t=e.get(n);if(!t){var o=Array.from(e.keys())||[],i=o.find(function(a){try{return a!=null&&a.startsWith("http")?!1:(0,rt.EQ)(a.replace("?",""))(n)}catch(c){return console.log("path",a,c),!1}});i&&(t=e.get(i))}return t||{path:""}},La=function(e){var n=e.location,t=e.breadcrumbMap;return{location:n,breadcrumbMap:t}},Ba=function(e,n,t){var o=Aa(e==null?void 0:e.pathname),i=o.map(function(a){var c=ja(n,a),u=wa(c,t),f=c.hideInBreadcrumb;return u&&!f?{linkPath:a,breadcrumbName:u,title:u,component:c.component}:{linkPath:"",breadcrumbName:"",title:""}}).filter(function(a){return a&&a.linkPath});return i},Wa=function(e){var n=La(e),t=n.location,o=n.breadcrumbMap;return t&&t.pathname&&o?Ba(t,o,e):[]},Fa=function(e,n){var t=e.breadcrumbRender,o=e.itemRender,i=n.breadcrumbProps||{},a=i.minLength,c=a===void 0?2:a,u=Wa(e),f=function(b){for(var R=o||Za,V=arguments.length,w=new Array(V>1?V-1:0),O=1;O<V;O++)w[O-1]=arguments[O];return R==null?void 0:R.apply(void 0,[(0,d.Z)((0,d.Z)({},b),{},{path:b.linkPath||b.path})].concat(w))},g=u;return t&&(g=t(g||[])||void 0),(g&&g.length<c||t===!1)&&(g=void 0),(0,y.n)(Na(),"5.3.0")>-1?{items:g,itemRender:f}:{routes:g,itemRender:f}};function Ha(r){return(0,wn.Z)(r).reduce(function(e,n){var t=(0,j.Z)(n,2),o=t[0],i=t[1];return e[o]=i,e},{})}var Ua=function r(e,n,t,o){var i=Dr(e,(n==null?void 0:n.locale)||!1,t,!0),a=i.menuData,c=i.breadcrumb;return o?r(o(a),n,t,void 0):{breadcrumb:Ha(c),breadcrumbMap:c,menuData:a}},$a=m(51812),Va=function(e){var n=(0,p.useState)({}),t=(0,j.Z)(n,2),o=t[0],i=t[1];return(0,p.useEffect)(function(){i((0,$a.Y)({layout:(0,Xn.Z)(e.layout)!=="object"?e.layout:void 0,navTheme:e.navTheme,menuRender:e.menuRender,footerRender:e.footerRender,menuHeaderRender:e.menuHeaderRender,headerRender:e.headerRender,fixSiderbar:e.fixSiderbar}))},[e.layout,e.navTheme,e.menuRender,e.footerRender,e.menuHeaderRender,e.headerRender,e.fixSiderbar]),o},za=["id","defaultMessage"],Ka=["fixSiderbar","navTheme","layout"],Jt=0,ka=function(e,n){var t;return e.headerRender===!1||e.pure?null:(0,s.jsx)(ca,(0,d.Z)((0,d.Z)({matchMenuKeys:n},e),{},{stylish:(t=e.stylish)===null||t===void 0?void 0:t.header}))},Ga=function(e){return e.footerRender===!1||e.pure?null:e.footerRender?e.footerRender((0,d.Z)({},e),(0,s.jsx)(lo,{})):null},Xa=function(e,n){var t,o=e.layout,i=e.isMobile,a=e.selectedKeys,c=e.openKeys,u=e.splitMenus,f=e.suppressSiderWhenMenuEmpty,g=e.menuRender;if(e.menuRender===!1||e.pure)return null;var x=e.menuData;if(u&&(c!==!1||o==="mix")&&!i){var b=a||n,R=(0,j.Z)(b,1),V=R[0];if(V){var w;x=((w=e.menuData)===null||w===void 0||(w=w.find(function(q){return q.key===V}))===null||w===void 0?void 0:w.children)||[]}else x=[]}var O=Zn(x||[]);if(O&&(O==null?void 0:O.length)<1&&(u||f))return null;if(o==="top"&&!i){var Y;return(0,s.jsx)(kt,(0,d.Z)((0,d.Z)({matchMenuKeys:n},e),{},{hide:!0,stylish:(Y=e.stylish)===null||Y===void 0?void 0:Y.sider}))}var K=(0,s.jsx)(kt,(0,d.Z)((0,d.Z)({matchMenuKeys:n},e),{},{menuData:O,stylish:(t=e.stylish)===null||t===void 0?void 0:t.sider}));return g?g(e,K):K},Ja=function(e,n){var t=n.pageTitleRender,o=ot(e);if(t===!1)return{title:n.title||"",id:"",pageName:""};if(t){var i=t(e,o.title,o);if(typeof i=="string")return ot((0,d.Z)((0,d.Z)({},o),{},{title:i}));(0,Ct.ZP)(typeof i=="string","pro-layout: renderPageTitle return value should be a string")}return o},Qa=function(e,n,t){return e?n?64:t:0},Ya=function(e){var n,t,o,i,a,c,u,f,g,x,b,R,V,w,O=e||{},Y=O.children,K=O.onCollapse,q=O.location,L=q===void 0?{pathname:"/"}:q,X=O.contentStyle,M=O.route,B=O.defaultCollapsed,ce=O.style,Q=O.siderWidth,z=O.menu,fe=O.siderMenuType,Ce=O.isChildrenLayout,E=O.menuDataRender,xe=O.actionRef,T=O.bgLayoutImgList,de=O.formatMessage,ge=O.loading,Pe=(0,p.useMemo)(function(){return Q||(e.layout==="mix"?215:256)},[e.layout,Q]),he=(0,p.useContext)(He.ZP.ConfigContext),Re=(n=e.prefixCls)!==null&&n!==void 0?n:he.getPrefixCls("pro"),Ee=(0,ne.Z)(!1,{value:z==null?void 0:z.loading,onChange:z==null?void 0:z.onLoadingChange}),Me=(0,j.Z)(Ee,2),Be=Me[0],Ze=Me[1],je=(0,p.useState)(function(){return Jt+=1,"pro-layout-".concat(Jt)}),$e=(0,j.Z)(je,1),ke=$e[0],Ge=(0,p.useCallback)(function(Te){var Je=Te.id,Un=Te.defaultMessage,Mn=(0,J.Z)(Te,za);if(de)return de((0,d.Z)({id:Je,defaultMessage:Un},Mn));var In=Ia();return In[Je]?In[Je]:Un},[de]),Xe=(0,jr.ZP)([ke,z==null?void 0:z.params],function(){var Te=(0,$.Z)((0,G.Z)().mark(function Je(Un){var Mn,In,dr,mr;return(0,G.Z)().wrap(function(cn){for(;;)switch(cn.prev=cn.next){case 0:return In=(0,j.Z)(Un,2),dr=In[1],Ze(!0),cn.next=4,z==null||(Mn=z.request)===null||Mn===void 0?void 0:Mn.call(z,dr||{},(M==null?void 0:M.children)||(M==null?void 0:M.routes)||[]);case 4:return mr=cn.sent,Ze(!1),cn.abrupt("return",mr);case 7:case"end":return cn.stop()}},Je)}));return function(Je){return Te.apply(this,arguments)}}(),{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),bn=Xe.data,Bn=Xe.mutate,Oe=Xe.isLoading;(0,p.useEffect)(function(){Ze(Oe)},[Oe]);var Ue=(0,Lr.kY)(),We=Ue.cache;(0,p.useEffect)(function(){return function(){We instanceof Map&&We.delete(ke)}},[]);var Wn=(0,p.useMemo)(function(){return Ua(bn||(M==null?void 0:M.children)||(M==null?void 0:M.routes)||[],z,Ge,E)},[Ge,z,E,bn,M==null?void 0:M.children,M==null?void 0:M.routes]),at=Wn||{},ei=at.breadcrumb,Qt=at.breadcrumbMap,Yt=at.menuData,Sn=Yt===void 0?[]:Yt;xe&&z!==null&&z!==void 0&&z.request&&(xe.current={reload:function(){Bn()}});var _n=(0,p.useMemo)(function(){return Zr(L.pathname||"/",Sn||[],!0)},[L.pathname,Sn]),it=(0,p.useMemo)(function(){return Array.from(new Set(_n.map(function(Te){return Te.key||Te.path||""})))},[_n]),qt=_n[_n.length-1]||{},er=Va(qt),Fn=(0,d.Z)((0,d.Z)({},e),er),ni=Fn.fixSiderbar,xi=Fn.navTheme,Pn=Fn.layout,ti=(0,J.Z)(Fn,Ka),an=re(),ln=(0,p.useMemo)(function(){return(an==="sm"||an==="xs")&&!e.disableMobile},[an,e.disableMobile]),ri=Pn!=="top"&&!ln,oi=(0,ne.Z)(function(){return B!==void 0?B:!!(ln||an==="md")},{value:e.collapsed,onChange:K}),nr=(0,j.Z)(oi,2),En=nr[0],tr=nr[1],sn=(0,yt.Z)((0,d.Z)((0,d.Z)((0,d.Z)({prefixCls:Re},e),{},{siderWidth:Pe},er),{},{formatMessage:Ge,breadcrumb:ei,menu:(0,d.Z)((0,d.Z)({},z),{},{type:fe||(z==null?void 0:z.type),loading:Be}),layout:Pn}),["className","style","breadcrumbRender"]),lt=Ja((0,d.Z)((0,d.Z)({pathname:L.pathname},sn),{},{breadcrumbMap:Qt}),e),ai=Fa((0,d.Z)((0,d.Z)({},sn),{},{breadcrumbRender:e.breadcrumbRender,breadcrumbMap:Qt}),e),Hn=Xa((0,d.Z)((0,d.Z)({},sn),{},{menuData:Sn,onCollapse:tr,isMobile:ln,collapsed:En}),it),st=ka((0,d.Z)((0,d.Z)({},sn),{},{children:null,hasSiderMenu:!!Hn,menuData:Sn,isMobile:ln,collapsed:En,onCollapse:tr}),it),rr=Ga((0,d.Z)({isMobile:ln,collapsed:En},sn)),ii=(0,p.useContext)(Gt.X),li=ii.isChildrenLayout,ct=Ce!==void 0?Ce:li,Ve="".concat(Re,"-layout"),or=Ta(Ve),si=or.wrapSSR,ut=or.hashId,ci=me()(e.className,ut,"ant-design-pro",Ve,(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},"screen-".concat(an),an),"".concat(Ve,"-top-menu"),Pn==="top"),"".concat(Ve,"-is-children"),ct),"".concat(Ve,"-fix-siderbar"),ni),"".concat(Ve,"-").concat(Pn),Pn)),ui=Qa(!!ri,En,Pe),ar={position:"relative"};(ct||X&&X.minHeight)&&(ar.minHeight=0),(0,p.useEffect)(function(){var Te;(Te=e.onPageChange)===null||Te===void 0||Te.call(e,e.location)},[L.pathname,(t=L.pathname)===null||t===void 0?void 0:t.search]);var di=(0,p.useState)(!1),ir=(0,j.Z)(di,2),lr=ir[0],mi=ir[1],pi=(0,p.useState)(0),sr=(0,j.Z)(pi,2),cr=sr[0],fi=sr[1];h(lt,e.title||!1);var vi=(0,p.useContext)(ie.L_),se=vi.token,ur=(0,p.useMemo)(function(){return T&&T.length>0?T==null?void 0:T.map(function(Te,Je){return(0,s.jsx)("img",{src:Te.src,style:(0,d.Z)({position:"absolute"},Te)},Je)}):null},[T]);return si((0,s.jsx)(Gt.X.Provider,{value:(0,d.Z)((0,d.Z)({},sn),{},{breadcrumb:ai,menuData:Sn,isMobile:ln,collapsed:En,hasPageContainer:cr,setHasPageContainer:fi,isChildrenLayout:!0,title:lt.pageName,hasSiderMenu:!!Hn,hasHeader:!!st,siderWidth:ui,hasFooter:!!rr,hasFooterToolbar:lr,setHasFooterToolbar:mi,pageTitleInfo:lt,matchMenus:_n,matchMenuKeys:it,currentMenu:qt}),children:e.pure?(0,s.jsx)(s.Fragment,{children:Y}):(0,s.jsxs)("div",{className:ci,children:[ur||(o=se.layout)!==null&&o!==void 0&&o.bgLayout?(0,s.jsx)("div",{className:me()("".concat(Ve,"-bg-list"),ut),children:ur}):null,(0,s.jsxs)(en.Z,{style:(0,d.Z)({minHeight:"100%",flexDirection:Hn?"row":void 0},ce),children:[(0,s.jsx)(He.ZP,{theme:{hashed:(0,ie.nu)(),token:{controlHeightLG:((i=se.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.menuHeight)||(se==null?void 0:se.controlHeightLG)},components:{Menu:H({colorItemBg:((a=se.layout)===null||a===void 0||(a=a.sider)===null||a===void 0?void 0:a.colorMenuBackground)||"transparent",colorSubItemBg:((c=se.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorMenuBackground)||"transparent",radiusItem:se.borderRadius,colorItemBgSelected:((u=se.layout)===null||u===void 0||(u=u.sider)===null||u===void 0?void 0:u.colorBgMenuItemSelected)||(se==null?void 0:se.colorBgTextHover),colorItemBgHover:((f=se.layout)===null||f===void 0||(f=f.sider)===null||f===void 0?void 0:f.colorBgMenuItemHover)||(se==null?void 0:se.colorBgTextHover),colorItemBgActive:((g=se.layout)===null||g===void 0||(g=g.sider)===null||g===void 0?void 0:g.colorBgMenuItemActive)||(se==null?void 0:se.colorBgTextActive),colorItemBgSelectedHorizontal:((x=se.layout)===null||x===void 0||(x=x.sider)===null||x===void 0?void 0:x.colorBgMenuItemSelected)||(se==null?void 0:se.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:((b=se.layout)===null||b===void 0||(b=b.sider)===null||b===void 0?void 0:b.colorTextMenu)||(se==null?void 0:se.colorTextSecondary),colorItemTextHover:((R=se.layout)===null||R===void 0||(R=R.sider)===null||R===void 0?void 0:R.colorTextMenuItemHover)||"rgba(0, 0, 0, 0.85)",colorItemTextSelected:((V=se.layout)===null||V===void 0||(V=V.sider)===null||V===void 0?void 0:V.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:se==null?void 0:se.colorBgElevated,subMenuItemBg:se==null?void 0:se.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:se==null?void 0:se.colorBgElevated})}},children:Hn}),(0,s.jsxs)("div",{style:ar,className:"".concat(Ve,"-container ").concat(ut).trim(),children:[st,(0,s.jsx)(Wr,(0,d.Z)((0,d.Z)({hasPageContainer:cr,isChildrenLayout:ct},ti),{},{hasHeader:!!st,prefixCls:Ve,style:X,children:ge?(0,s.jsx)(ua.S,{}):Y})),rr,lr&&(0,s.jsx)("div",{className:"".concat(Ve,"-has-footer"),style:{height:64,marginBlockStart:(w=se.layout)===null||w===void 0||(w=w.pageContainer)===null||w===void 0?void 0:w.paddingBlockPageContainerContent}})]})]})]})}))},qa=function(e){var n=e.colorPrimary,t=e.navTheme!==void 0?{dark:e.navTheme==="realDark"}:{};return(0,s.jsx)(He.ZP,{theme:n?{token:{colorPrimary:n}}:void 0,children:(0,s.jsx)(ie._Y,(0,d.Z)((0,d.Z)({},t),{},{token:e.token,prefixCls:e.prefixCls,children:(0,s.jsx)(Ya,(0,d.Z)((0,d.Z)({logo:(0,s.jsx)(Fr,{})},wt),{},{location:(0,N.j)()?window.location:void 0},e))}))})}},90743:function(pe,oe){var m;function l(h){"@babel/helpers - typeof";return l=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(y){return typeof y}:function(y){return y&&typeof Symbol=="function"&&y.constructor===Symbol&&y!==Symbol.prototype?"symbol":typeof y},l(h)}m={value:!0},oe.Bo=m=m=m=m=m=m=void 0;function G(h){for(var y=[],S=0;S<h.length;){var H=h[S];if(H==="*"||H==="+"||H==="?"){y.push({type:"MODIFIER",index:S,value:h[S++]});continue}if(H==="\\"){y.push({type:"ESCAPED_CHAR",index:S++,value:h[S++]});continue}if(H==="{"){y.push({type:"OPEN",index:S,value:h[S++]});continue}if(H==="}"){y.push({type:"CLOSE",index:S,value:h[S++]});continue}if(H===":"){for(var ee="",F=S+1;F<h.length;){var ae=h.charCodeAt(F);if(ae>=48&&ae<=57||ae>=65&&ae<=90||ae>=97&&ae<=122||ae===95){ee+=h[F++];continue}break}if(!ee)throw new TypeError("Missing parameter name at "+S);y.push({type:"NAME",index:S,value:ee}),S=F;continue}if(H==="("){var le=1,te="",F=S+1;if(h[F]==="?")throw new TypeError('Pattern cannot start with "?" at '+F);for(;F<h.length;){if(h[F]==="\\"){te+=h[F++]+h[F++];continue}if(h[F]===")"){if(le--,le===0){F++;break}}else if(h[F]==="("&&(le++,h[F+1]!=="?"))throw new TypeError("Capturing groups are not allowed at "+F);te+=h[F++]}if(le)throw new TypeError("Unbalanced pattern at "+S);if(!te)throw new TypeError("Missing pattern at "+S);y.push({type:"PATTERN",index:S,value:te}),S=F;continue}y.push({type:"CHAR",index:S,value:h[S++]})}return y.push({type:"END",index:S,value:""}),y}function $(h,y){y===void 0&&(y={});for(var S=G(h),H=y.prefixes,ee=H===void 0?"./":H,F="[^"+ne(y.delimiter||"/#?")+"]+?",ae=[],le=0,te=0,_="",C=function(De){if(te<S.length&&S[te].type===De)return S[te++].value},v=function(De){var Ae=C(De);if(Ae!==void 0)return Ae;var Fe=S[te],Qe=Fe.type,ze=Fe.index;throw new TypeError("Unexpected "+Qe+" at "+ze+", expected "+De)},A=function(){for(var De="",Ae;Ae=C("CHAR")||C("ESCAPED_CHAR");)De+=Ae;return De};te<S.length;){var D=C("CHAR"),P=C("NAME"),k=C("PATTERN");if(P||k){var I=D||"";ee.indexOf(I)===-1&&(_+=I,I=""),_&&(ae.push(_),_=""),ae.push({name:P||le++,prefix:I,suffix:"",pattern:k||F,modifier:C("MODIFIER")||""});continue}var ue=D||C("ESCAPED_CHAR");if(ue){_+=ue;continue}_&&(ae.push(_),_="");var ve=C("OPEN");if(ve){var I=A(),be=C("NAME")||"",ye=C("PATTERN")||"",Ie=A();v("CLOSE"),ae.push({name:be||(ye?le++:""),pattern:be&&!ye?F:ye,prefix:I,suffix:Ie,modifier:C("MODIFIER")||""});continue}v("END")}return ae}m=$;function J(h,y){return j($(h,y),y)}m=J;function j(h,y){y===void 0&&(y={});var S=p(y),H=y.encode,ee=H===void 0?function(te){return te}:H,F=y.validate,ae=F===void 0?!0:F,le=h.map(function(te){if(l(te)==="object")return new RegExp("^(?:"+te.pattern+")$",S)});return function(te){for(var _="",C=0;C<h.length;C++){var v=h[C];if(typeof v=="string"){_+=v;continue}var A=te?te[v.name]:void 0,D=v.modifier==="?"||v.modifier==="*",P=v.modifier==="*"||v.modifier==="+";if(Array.isArray(A)){if(!P)throw new TypeError('Expected "'+v.name+'" to not repeat, but got an array');if(A.length===0){if(D)continue;throw new TypeError('Expected "'+v.name+'" to not be empty')}for(var k=0;k<A.length;k++){var I=ee(A[k],v);if(ae&&!le[C].test(I))throw new TypeError('Expected all "'+v.name+'" to match "'+v.pattern+'", but got "'+I+'"');_+=v.prefix+I+v.suffix}continue}if(typeof A=="string"||typeof A=="number"){var I=ee(String(A),v);if(ae&&!le[C].test(I))throw new TypeError('Expected "'+v.name+'" to match "'+v.pattern+'", but got "'+I+'"');_+=v.prefix+I+v.suffix;continue}if(!D){var ue=P?"an array":"a string";throw new TypeError('Expected "'+v.name+'" to be '+ue)}}return _}}m=j;function d(h,y){var S=[],H=N(h,S,y);return ie(H,S,y)}m=d;function ie(h,y,S){S===void 0&&(S={});var H=S.decode,ee=H===void 0?function(F){return F}:H;return function(F){var ae=h.exec(F);if(!ae)return!1;for(var le=ae[0],te=ae.index,_=Object.create(null),C=function(D){if(ae[D]===void 0)return"continue";var P=y[D-1];P.modifier==="*"||P.modifier==="+"?_[P.name]=ae[D].split(P.prefix+P.suffix).map(function(k){return ee(k,P)}):_[P.name]=ee(ae[D],P)},v=1;v<ae.length;v++)C(v);return{path:le,index:te,params:_}}}m=ie;function ne(h){return h.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function p(h){return h&&h.sensitive?"":"i"}function U(h,y){if(!y)return h;var S=h.source.match(/\((?!\?)/g);if(S)for(var H=0;H<S.length;H++)y.push({name:H,prefix:"",suffix:"",modifier:"",pattern:""});return h}function W(h,y,S){var H=h.map(function(ee){return N(ee,y,S).source});return new RegExp("(?:"+H.join("|")+")",p(S))}function Z(h,y,S){return re($(h,S),y,S)}function re(h,y,S){S===void 0&&(S={});for(var H=S.strict,ee=H===void 0?!1:H,F=S.start,ae=F===void 0?!0:F,le=S.end,te=le===void 0?!0:le,_=S.encode,C=_===void 0?function(Se){return Se}:_,v="["+ne(S.endsWith||"")+"]|$",A="["+ne(S.delimiter||"/#?")+"]",D=ae?"^":"",P=0,k=h;P<k.length;P++){var I=k[P];if(typeof I=="string")D+=ne(C(I));else{var ue=ne(C(I.prefix)),ve=ne(C(I.suffix));if(I.pattern)if(y&&y.push(I),ue||ve)if(I.modifier==="+"||I.modifier==="*"){var be=I.modifier==="*"?"?":"";D+="(?:"+ue+"((?:"+I.pattern+")(?:"+ve+ue+"(?:"+I.pattern+"))*)"+ve+")"+be}else D+="(?:"+ue+"("+I.pattern+")"+ve+")"+I.modifier;else D+="("+I.pattern+")"+I.modifier;else D+="(?:"+ue+ve+")"+I.modifier}}if(te)ee||(D+=A+"?"),D+=S.endsWith?"(?="+v+")":"$";else{var ye=h[h.length-1],Ie=typeof ye=="string"?A.indexOf(ye[ye.length-1])>-1:ye===void 0;ee||(D+="(?:"+A+"(?="+v+"))?"),Ie||(D+="(?="+A+"|"+v+")")}return new RegExp(D,p(S))}m=re;function N(h,y,S){return h instanceof RegExp?U(h,y):Array.isArray(h)?W(h,y,S):Z(h,y,S)}oe.Bo=N},73177:function(pe,oe,m){"use strict";m.d(oe,{X:function(){return d},b:function(){return j}});var l=m(67159),G=m(51812),$=m(1977),J=m(34155),j=function(){var ne;return typeof J=="undefined"?l.Z:((ne=J)===null||J===void 0||(J={ALLUSERSPROFILE:"C:\\ProgramData",APPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133655684414351508",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_26212_KVVCMVWIBAPLZQEB",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"\u5C0F\u4E38\u728A\u5B50",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",CUDA_PATH:"D:\\CUDA",CUDA_PATH_V11_0:"D:\\CUDA",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_6444_1262719628:"1",EFC_6444_1592913036:"1",EFC_6444_2283032206:"1",EFC_6444_2775293581:"1",EFC_6444_3789132940:"1",ELINK_INSTALL_PATH:"C:\\Program Files (x86)\\Ecloud\\CloudComputer\\drivers\\CMSS",FPS_BROWSER_APP_PROFILE_STRING:"Internet Explorer",FPS_BROWSER_USER_PROFILE_STRING:"Default",GIT_ASKPASS:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",HOME:"C:\\Users\\\<USER>\u82CF",HOMEDRIVE:"C:",HOMEPATH:"\\Users\\\u82CF\u82CF",INIT_CWD:"D:\\project\\web_app_0527v2\\web",JAVA_HOME:"D:\\Program Files\\javajdk",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\\<USER>\u82CF\\AppData\\Local",LOGONSERVER:"\\\\\u5C0F\u4E38\u728A\u5B50",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"production",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NODE_PATH:"D:\\\u524D\u7AEF\\nodejs\\node_modules",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_cache:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\npm-cache",npm_config_globalconfig:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\\<USER>\u82CF\\.npm-init.js",npm_config_local_prefix:"D:\\project\\web_app_0527v2\\web",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_noproxy:"",npm_config_npm_version:"10.7.0",npm_config_prefix:"C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm",npm_config_userconfig:"C:\\Users\\\<USER>\u82CF\\.npmrc",npm_config_user_agent:"npm/10.7.0 node/v20.15.1 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build:bj",npm_lifecycle_script:"cross-env REACT_APP_ENV=bj UMI_ENV=bj max build",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_engines_node:">=12.0.0",npm_package_json:"D:\\project\\web_app_0527v2\\web\\package.json",npm_package_name:"ant-design-pro",npm_package_version:"6.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\\<USER>\u5FDA\u5AC3\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"16",NVCUDASAMPLES11_0_ROOT:"D:\\CUDA",NVCUDASAMPLES_ROOT:"D:\\CUDA",NVTOOLSEXT_PATH:"C:\\Program Files\\NVIDIA Corporation\\NvToolsExt\\",OneDrive:"C:\\Users\\\<USER>\u82CF\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"D:\\project\\web_app_0527v2\\web\\node_modules\\.bin;D:\\project\\web_app_0527v2\\node_modules\\.bin;D:\\project\\node_modules\\.bin;D:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;D:\\Program Files\\javajdk\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;D:\\CUDA\\bin;D:\\CUDA\\libnvvp;D:\\Anaconda;D:\\Anaconda\\Library\\mingw-w64\\bin;D:\\Anaconda\\Library\\usr\\bin;D:\\Anaconda\\Library\\bin;D:\\Anaconda\\Scripts;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\\u524D\u7AEF\\nodejs\\node_global;D;\\\u524D\u7AEF\\mongoDB\\server\\bin;C;\\Program Files\\NVIDIA Corporation\\Nsight Compute 2020.1.1\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;D:\\\u524D\u7AEF\\\u9879\u76EE\\2\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\\\u5FAE\u4FE1web\u5F00\u53D1\u8005\u5DE5\u5177\\dll;C:\\Program File;\\MySQL\\MySQL Server 8.0\\bin;C:\\Program Files (x86)\\PuTTY\\;D:\\Program Files\\TortoiseGit\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\Program\\Git\\cmd;D:\\Program Files\\javajdk\\bin;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\;C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Microsoft\\WindowsApps;D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;;D:\\\u524D\u7AEF\\nodejs\\node_global;C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;;C:\\Users\\\<USER>\u82CF\\AppData\\Roaming\\npm;D:\\Program\\Microsoft VS Code\\bin;C:\\MinGW\\bin;D:\\Program\\cursor\\resources\\app\\bin",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD",PROCESSOR_LEVEL:"25",PROCESSOR_REVISION:"5000",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\\<USER>\u82CF\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.0.1\\bin;",REACT_APP_ENV:"bj",REACT_APP_WEB_SERVER_SOCKET_PORT:"http://*************:9044/",SESSIONNAME:"Console",SystemDrive:"C:",SystemRoot:"C:\\WINDOWS",TEMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.100.2",TMP:"C:\\Users\\\<USER>\u82CF\\AppData\\Local\\Temp",UMI_APP_BACKEND_URL:"http://localhost:8001",UMI_DIR:"D:\\project\\web_app_0527v2\\web\\node_modules\\umi",UMI_ENV:"bj",UMI_PRESETS:"D:\\project\\web_app_0527v2\\web\\node_modules\\@umijs\\max\\dist\\preset.js",USERDOMAIN:"\u5C0F\u4E38\u728A\u5B50",USERDOMAIN_ROAMINGPROFILE:"\u5C0F\u4E38\u728A\u5B50",USERNAME:"\u82CF\u82CF",USERPROFILE:"C:\\Users\\\<USER>\u82CF",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",VSCODE_GIT_ASKPASS_MAIN:"d:\\Program\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"D:\\Program\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-d0bcb91465-sock",VSCODE_INJECTION:"1",WebStorm:"D:\\\u524D\u7AEF\\webstorm\\WebStorm 2021.2.3\\bin;",windir:"C:\\WINDOWS",ZXCLOUD_INSTALL_PATH:"C:\\Program Files (x86)\\uSmartView",ZXVE_CLIENT_AGENT_UUID:"3577B2A6-F8E2-4987-9B98-4748E6A96F38",ZXVE_IRAI:"C:\\Program Files (x86)\\uSmartView\\client\\uSmartView.exe",ZXVE_NEW_UDS:"0x00000001"})===null||J===void 0?void 0:J.ANTD_VERSION)||l.Z},d=function(ne,p){var U=(0,$.n)(j(),"4.23.0")>-1?{open:ne,onOpenChange:p}:{visible:ne,onVisibleChange:p};return(0,G.Y)(U)}},78164:function(pe,oe,m){"use strict";m.d(oe,{S:function(){return U}});var l=m(15671),G=m(43144),$=m(97326),J=m(60136),j=m(29388),d=m(4942),ie=m(29905),ne=m(67294),p=m(85893),U=function(W){(0,J.Z)(re,W);var Z=(0,j.Z)(re);function re(){var N;(0,l.Z)(this,re);for(var h=arguments.length,y=new Array(h),S=0;S<h;S++)y[S]=arguments[S];return N=Z.call.apply(Z,[this].concat(y)),(0,d.Z)((0,$.Z)(N),"state",{hasError:!1,errorInfo:""}),N}return(0,G.Z)(re,[{key:"componentDidCatch",value:function(h,y){console.log(h,y)}},{key:"render",value:function(){return this.state.hasError?(0,p.jsx)(ie.ZP,{status:"error",title:"Something went wrong.",extra:this.state.errorInfo}):this.props.children}}],[{key:"getDerivedStateFromError",value:function(h){return{hasError:!0,errorInfo:h.message}}}]),re}(ne.Component)},10178:function(pe,oe,m){"use strict";m.d(oe,{D:function(){return j}});var l=m(74165),G=m(15861),$=m(67294),J=m(48171);function j(d,ie){var ne=(0,J.J)(d),p=(0,$.useRef)(),U=(0,$.useCallback)(function(){p.current&&(clearTimeout(p.current),p.current=null)},[]),W=(0,$.useCallback)((0,G.Z)((0,l.Z)().mark(function Z(){var re,N,h,y=arguments;return(0,l.Z)().wrap(function(H){for(;;)switch(H.prev=H.next){case 0:for(re=y.length,N=new Array(re),h=0;h<re;h++)N[h]=y[h];if(!(ie===0||ie===void 0)){H.next=3;break}return H.abrupt("return",ne.apply(void 0,N));case 3:return U(),H.abrupt("return",new Promise(function(ee){p.current=setTimeout((0,G.Z)((0,l.Z)().mark(function F(){return(0,l.Z)().wrap(function(le){for(;;)switch(le.prev=le.next){case 0:return le.t0=ee,le.next=3,ne.apply(void 0,N);case 3:return le.t1=le.sent,(0,le.t0)(le.t1),le.abrupt("return");case 6:case"end":return le.stop()}},F)})),ie)}));case 5:case"end":return H.stop()}},Z)})),[ne,U,ie]);return(0,$.useEffect)(function(){return U},[U]),{run:W,cancel:U}}},48171:function(pe,oe,m){"use strict";m.d(oe,{J:function(){return $}});var l=m(74902),G=m(67294),$=function(j){var d=(0,G.useRef)(null);return d.current=j,(0,G.useCallback)(function(){for(var ie,ne=arguments.length,p=new Array(ne),U=0;U<ne;U++)p[U]=arguments[U];return(ie=d.current)===null||ie===void 0?void 0:ie.call.apply(ie,[d].concat((0,l.Z)(p)))},[])}},51812:function(pe,oe,m){"use strict";m.d(oe,{Y:function(){return l}});var l=function($){var J={};if(Object.keys($||{}).forEach(function(j){$[j]!==void 0&&(J[j]=$[j])}),!(Object.keys(J).length<1))return J}},81643:function(pe,oe,m){"use strict";m.d(oe,{Z:function(){return l}});const l=G=>G?typeof G=="function"?G():G:null},57838:function(pe,oe,m){"use strict";m.d(oe,{Z:function(){return G}});var l=m(67294);function G(){const[,$]=l.useReducer(J=>J+1,0);return $}},74443:function(pe,oe,m){"use strict";m.d(oe,{c4:function(){return J}});var l=m(67294),G=m(29691),$=m(85849);const J=["xxl","xl","lg","md","sm","xs"],j=p=>({xs:`(max-width: ${p.screenXSMax}px)`,sm:`(min-width: ${p.screenSM}px)`,md:`(min-width: ${p.screenMD}px)`,lg:`(min-width: ${p.screenLG}px)`,xl:`(min-width: ${p.screenXL}px)`,xxl:`(min-width: ${p.screenXXL}px)`}),d=p=>{const U=p,W=[].concat(J).reverse();return W.forEach((Z,re)=>{const N=Z.toUpperCase(),h=`screen${N}Min`,y=`screen${N}`;if(!(U[h]<=U[y]))throw new Error(`${h}<=${y} fails : !(${U[h]}<=${U[y]})`);if(re<W.length-1){const S=`screen${N}Max`;if(!(U[y]<=U[S]))throw new Error(`${y}<=${S} fails : !(${U[y]}<=${U[S]})`);const ee=`screen${W[re+1].toUpperCase()}Min`;if(!(U[S]<=U[ee]))throw new Error(`${S}<=${ee} fails : !(${U[S]}<=${U[ee]})`)}}),p},ie=(p,U)=>{if(U){for(const W of J)if(p[W]&&(U==null?void 0:U[W])!==void 0)return U[W]}},ne=()=>{const[,p]=(0,G.ZP)(),U=j(d(p));return l.useMemo(()=>{const W=new Map;let Z=-1,re={};return{responsiveMap:U,matchHandlers:{},dispatch(N){return re=N,W.forEach(h=>h(re)),W.size>=1},subscribe(N){return W.size||this.register(),Z+=1,W.set(Z,N),N(re),Z},unsubscribe(N){W.delete(N),W.size||this.unregister()},register(){Object.entries(U).forEach(([N,h])=>{const y=({matches:H})=>{this.dispatch(Object.assign(Object.assign({},re),{[N]:H}))},S=window.matchMedia(h);(0,$.x)(S,y),this.matchHandlers[h]={mql:S,listener:y},y(S)})},unregister(){Object.values(U).forEach(N=>{const h=this.matchHandlers[N];(0,$.h)(h==null?void 0:h.mql,h==null?void 0:h.listener)}),W.clear()}}},[p])};oe.ZP=ne},25378:function(pe,oe,m){"use strict";var l=m(67294),G=m(8410),$=m(57838),J=m(74443);function j(d=!0,ie={}){const ne=(0,l.useRef)(ie),p=(0,$.Z)(),U=(0,J.ZP)();return(0,G.Z)(()=>{const W=U.subscribe(Z=>{ne.current=Z,d&&p()});return()=>U.unsubscribe(W)},[]),ne.current}oe.Z=j},26058:function(pe,oe,m){"use strict";m.d(oe,{Z:function(){return le}});var l=m(74902),G=m(67294),$=m(93967),J=m.n($),j=m(98423),d=m(53124),ie=m(82401),ne=m(50344),p=m(70985);function U(te,_,C){return typeof C=="boolean"?C:te.length?!0:(0,ne.Z)(_).some(A=>A.type===p.Z)}var W=m(24793),Z=function(te,_){var C={};for(var v in te)Object.prototype.hasOwnProperty.call(te,v)&&_.indexOf(v)<0&&(C[v]=te[v]);if(te!=null&&typeof Object.getOwnPropertySymbols=="function")for(var A=0,v=Object.getOwnPropertySymbols(te);A<v.length;A++)_.indexOf(v[A])<0&&Object.prototype.propertyIsEnumerable.call(te,v[A])&&(C[v[A]]=te[v[A]]);return C};function re({suffixCls:te,tagName:_,displayName:C}){return v=>G.forwardRef((D,P)=>G.createElement(v,Object.assign({ref:P,suffixCls:te,tagName:_},D)))}const N=G.forwardRef((te,_)=>{const{prefixCls:C,suffixCls:v,className:A,tagName:D}=te,P=Z(te,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:k}=G.useContext(d.E_),I=k("layout",C),[ue,ve,be]=(0,W.ZP)(I),ye=v?`${I}-${v}`:I;return ue(G.createElement(D,Object.assign({className:J()(C||ye,A,ve,be),ref:_},P)))}),h=G.forwardRef((te,_)=>{const{direction:C}=G.useContext(d.E_),[v,A]=G.useState([]),{prefixCls:D,className:P,rootClassName:k,children:I,hasSider:ue,tagName:ve,style:be}=te,ye=Z(te,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),Ie=(0,j.Z)(ye,["suffixCls"]),{getPrefixCls:Se,className:De,style:Ae}=(0,d.dj)("layout"),Fe=Se("layout",D),Qe=U(v,I,ue),[ze,un,dn]=(0,W.ZP)(Fe),mn=J()(Fe,{[`${Fe}-has-sider`]:Qe,[`${Fe}-rtl`]:C==="rtl"},De,P,k,un,dn),pn=G.useMemo(()=>({siderHook:{addSider:Ye=>{A(Ke=>[].concat((0,l.Z)(Ke),[Ye]))},removeSider:Ye=>{A(Ke=>Ke.filter(fn=>fn!==Ye))}}}),[]);return ze(G.createElement(ie.V.Provider,{value:pn},G.createElement(ve,Object.assign({ref:_,className:mn,style:Object.assign(Object.assign({},Ae),be)},Ie),I)))}),y=re({tagName:"div",displayName:"Layout"})(h),S=re({suffixCls:"header",tagName:"header",displayName:"Header"})(N),H=re({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(N),ee=re({suffixCls:"content",tagName:"main",displayName:"Content"})(N);var F=y;const ae=F;ae.Header=S,ae.Footer=H,ae.Content=ee,ae.Sider=p.Z,ae._InternalSiderContext=p.D;var le=ae},66330:function(pe,oe,m){"use strict";m.d(oe,{aV:function(){return p}});var l=m(67294),G=m(93967),$=m.n(G),J=m(92419),j=m(81643),d=m(53124),ie=m(20136),ne=function(Z,re){var N={};for(var h in Z)Object.prototype.hasOwnProperty.call(Z,h)&&re.indexOf(h)<0&&(N[h]=Z[h]);if(Z!=null&&typeof Object.getOwnPropertySymbols=="function")for(var y=0,h=Object.getOwnPropertySymbols(Z);y<h.length;y++)re.indexOf(h[y])<0&&Object.prototype.propertyIsEnumerable.call(Z,h[y])&&(N[h[y]]=Z[h[y]]);return N};const p=({title:Z,content:re,prefixCls:N})=>!Z&&!re?null:l.createElement(l.Fragment,null,Z&&l.createElement("div",{className:`${N}-title`},Z),re&&l.createElement("div",{className:`${N}-inner-content`},re)),U=Z=>{const{hashId:re,prefixCls:N,className:h,style:y,placement:S="top",title:H,content:ee,children:F}=Z,ae=(0,j.Z)(H),le=(0,j.Z)(ee),te=$()(re,N,`${N}-pure`,`${N}-placement-${S}`,h);return l.createElement("div",{className:te,style:y},l.createElement("div",{className:`${N}-arrow`}),l.createElement(J.G,Object.assign({},Z,{className:re,prefixCls:N}),F||l.createElement(p,{prefixCls:N,title:ae,content:le})))},W=Z=>{const{prefixCls:re,className:N}=Z,h=ne(Z,["prefixCls","className"]),{getPrefixCls:y}=l.useContext(d.E_),S=y("popover",re),[H,ee,F]=(0,ie.Z)(S);return H(l.createElement(U,Object.assign({},h,{prefixCls:S,hashId:ee,className:$()(N,F)})))};oe.ZP=W},55241:function(pe,oe,m){"use strict";var l=m(67294),G=m(93967),$=m.n(G),J=m(21770),j=m(15105),d=m(81643),ie=m(33603),ne=m(96159),p=m(83062),U=m(66330),W=m(53124),Z=m(20136),re=function(y,S){var H={};for(var ee in y)Object.prototype.hasOwnProperty.call(y,ee)&&S.indexOf(ee)<0&&(H[ee]=y[ee]);if(y!=null&&typeof Object.getOwnPropertySymbols=="function")for(var F=0,ee=Object.getOwnPropertySymbols(y);F<ee.length;F++)S.indexOf(ee[F])<0&&Object.prototype.propertyIsEnumerable.call(y,ee[F])&&(H[ee[F]]=y[ee[F]]);return H};const h=l.forwardRef((y,S)=>{var H,ee;const{prefixCls:F,title:ae,content:le,overlayClassName:te,placement:_="top",trigger:C="hover",children:v,mouseEnterDelay:A=.1,mouseLeaveDelay:D=.1,onOpenChange:P,overlayStyle:k={},styles:I,classNames:ue}=y,ve=re(y,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:be,className:ye,style:Ie,classNames:Se,styles:De}=(0,W.dj)("popover"),Ae=be("popover",F),[Fe,Qe,ze]=(0,Z.Z)(Ae),un=be(),dn=$()(te,Qe,ze,ye,Se.root,ue==null?void 0:ue.root),mn=$()(Se.body,ue==null?void 0:ue.body),[pn,Ye]=(0,J.Z)(!1,{value:(H=y.open)!==null&&H!==void 0?H:y.visible,defaultValue:(ee=y.defaultOpen)!==null&&ee!==void 0?ee:y.defaultVisible}),Ke=(Le,nn)=>{Ye(Le,!0),P==null||P(Le,nn)},fn=Le=>{Le.keyCode===j.Z.ESC&&Ke(!1,Le)},Dn=Le=>{Ke(Le)},Rn=(0,d.Z)(ae),On=(0,d.Z)(le);return Fe(l.createElement(p.Z,Object.assign({placement:_,trigger:C,mouseEnterDelay:A,mouseLeaveDelay:D},ve,{prefixCls:Ae,classNames:{root:dn,body:mn},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},De.root),Ie),k),I==null?void 0:I.root),body:Object.assign(Object.assign({},De.body),I==null?void 0:I.body)},ref:S,open:pn,onOpenChange:Dn,overlay:Rn||On?l.createElement(U.aV,{prefixCls:Ae,title:Rn,content:On}):null,transitionName:(0,ie.m)(un,"zoom-big",ve.transitionName),"data-popover-inject":!0}),(0,ne.Tm)(v,{onKeyDown:Le=>{var nn,vn;l.isValidElement(v)&&((vn=v==null?void 0:(nn=v.props).onKeyDown)===null||vn===void 0||vn.call(nn,Le)),fn(Le)}})))});h._InternalPanelDoNotUseOrYouWillBeFired=U.ZP,oe.Z=h},20136:function(pe,oe,m){"use strict";var l=m(14747),G=m(50438),$=m(97414),J=m(79511),j=m(8796),d=m(83559),ie=m(83262);const ne=W=>{const{componentCls:Z,popoverColor:re,titleMinWidth:N,fontWeightStrong:h,innerPadding:y,boxShadowSecondary:S,colorTextHeading:H,borderRadiusLG:ee,zIndexPopup:F,titleMarginBottom:ae,colorBgElevated:le,popoverBg:te,titleBorderBottom:_,innerContentPadding:C,titlePadding:v}=W;return[{[Z]:Object.assign(Object.assign({},(0,l.Wf)(W)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:F,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":le,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${Z}-content`]:{position:"relative"},[`${Z}-inner`]:{backgroundColor:te,backgroundClip:"padding-box",borderRadius:ee,boxShadow:S,padding:y},[`${Z}-title`]:{minWidth:N,marginBottom:ae,color:H,fontWeight:h,borderBottom:_,padding:v},[`${Z}-inner-content`]:{color:re,padding:C}})},(0,$.ZP)(W,"var(--antd-arrow-background-color)"),{[`${Z}-pure`]:{position:"relative",maxWidth:"none",margin:W.sizePopupArrow,display:"inline-block",[`${Z}-content`]:{display:"inline-block"}}}]},p=W=>{const{componentCls:Z}=W;return{[Z]:j.i.map(re=>{const N=W[`${re}6`];return{[`&${Z}-${re}`]:{"--antd-arrow-background-color":N,[`${Z}-inner`]:{backgroundColor:N},[`${Z}-arrow`]:{background:"transparent"}}}})}},U=W=>{const{lineWidth:Z,controlHeight:re,fontHeight:N,padding:h,wireframe:y,zIndexPopupBase:S,borderRadiusLG:H,marginXS:ee,lineType:F,colorSplit:ae,paddingSM:le}=W,te=re-N,_=te/2,C=te/2-Z,v=h;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:S+30},(0,J.w)(W)),(0,$.wZ)({contentRadius:H,limitVerticalRadius:!0})),{innerPadding:y?0:12,titleMarginBottom:y?0:ee,titlePadding:y?`${_}px ${v}px ${C}px`:0,titleBorderBottom:y?`${Z}px ${F} ${ae}`:"none",innerContentPadding:y?`${le}px ${v}px`:0})};oe.Z=(0,d.I$)("Popover",W=>{const{colorBgElevated:Z,colorText:re}=W,N=(0,ie.IX)(W,{popoverBg:Z,popoverColor:re});return[ne(N),p(N),(0,G._y)(N,"zoom-big")]},U,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})},16305:function(pe,oe){"use strict";var m;m={value:!0},m=void 0,m=Z,m=re,oe.EQ=y,m=S,m=ae;const l="/",G=_=>_,$=/^[$_\p{ID_Start}]$/u,J=/^[$\u200c\u200d\p{ID_Continue}]$/u,j="https://git.new/pathToRegexpError",d={"{":"{","}":"}","(":"(",")":")","[":"[","]":"]","+":"+","?":"?","!":"!"};function ie(_){return _.replace(/[{}()\[\]+?!:*]/g,"\\$&")}function ne(_){return _.replace(/[.+*?^${}()[\]|/\\]/g,"\\$&")}function*p(_){const C=[..._];let v=0;function A(){let D="";if($.test(C[++v]))for(D+=C[v];J.test(C[++v]);)D+=C[v];else if(C[v]==='"'){let P=v;for(;v<C.length;){if(C[++v]==='"'){v++,P=0;break}C[v]==="\\"?D+=C[++v]:D+=C[v]}if(P)throw new TypeError(`Unterminated quote at ${P}: ${j}`)}if(!D)throw new TypeError(`Missing parameter name at ${v}: ${j}`);return D}for(;v<C.length;){const D=C[v],P=d[D];if(P)yield{type:P,index:v++,value:D};else if(D==="\\")yield{type:"ESCAPED",index:v++,value:C[v++]};else if(D===":"){const k=A();yield{type:"PARAM",index:v,value:k}}else if(D==="*"){const k=A();yield{type:"WILDCARD",index:v,value:k}}else yield{type:"CHAR",index:v,value:C[v++]}}return{type:"END",index:v,value:""}}class U{constructor(C){this.tokens=C}peek(){if(!this._peek){const C=this.tokens.next();this._peek=C.value}return this._peek}tryConsume(C){const v=this.peek();if(v.type===C)return this._peek=void 0,v.value}consume(C){const v=this.tryConsume(C);if(v!==void 0)return v;const{type:A,index:D}=this.peek();throw new TypeError(`Unexpected ${A} at ${D}, expected ${C}: ${j}`)}text(){let C="",v;for(;v=this.tryConsume("CHAR")||this.tryConsume("ESCAPED");)C+=v;return C}}class W{constructor(C){this.tokens=C}}m=W;function Z(_,C={}){const{encodePath:v=G}=C,A=new U(p(_));function D(k){const I=[];for(;;){const ue=A.text();ue&&I.push({type:"text",value:v(ue)});const ve=A.tryConsume("PARAM");if(ve){I.push({type:"param",name:ve});continue}const be=A.tryConsume("WILDCARD");if(be){I.push({type:"wildcard",name:be});continue}if(A.tryConsume("{")){I.push({type:"group",tokens:D("}")});continue}return A.consume(k),I}}const P=D("END");return new W(P)}function re(_,C={}){const{encode:v=encodeURIComponent,delimiter:A=l}=C,D=_ instanceof W?_:Z(_,C),P=N(D.tokens,A,v);return function(I={}){const[ue,...ve]=P(I);if(ve.length)throw new TypeError(`Missing parameters: ${ve.join(", ")}`);return ue}}function N(_,C,v){const A=_.map(D=>h(D,C,v));return D=>{const P=[""];for(const k of A){const[I,...ue]=k(D);P[0]+=I,P.push(...ue)}return P}}function h(_,C,v){if(_.type==="text")return()=>[_.value];if(_.type==="group"){const D=N(_.tokens,C,v);return P=>{const[k,...I]=D(P);return I.length?[""]:[k]}}const A=v||G;return _.type==="wildcard"&&v!==!1?D=>{const P=D[_.name];if(P==null)return["",_.name];if(!Array.isArray(P)||P.length===0)throw new TypeError(`Expected "${_.name}" to be a non-empty array`);return[P.map((k,I)=>{if(typeof k!="string")throw new TypeError(`Expected "${_.name}/${I}" to be a string`);return A(k)}).join(C)]}:D=>{const P=D[_.name];if(P==null)return["",_.name];if(typeof P!="string")throw new TypeError(`Expected "${_.name}" to be a string`);return[A(P)]}}function y(_,C={}){const{decode:v=decodeURIComponent,delimiter:A=l}=C,{regexp:D,keys:P}=S(_,C),k=P.map(I=>v===!1?G:I.type==="param"?v:ue=>ue.split(A).map(v));return function(ue){const ve=D.exec(ue);if(!ve)return!1;const be=ve[0],ye=Object.create(null);for(let Ie=1;Ie<ve.length;Ie++){if(ve[Ie]===void 0)continue;const Se=P[Ie-1],De=k[Ie-1];ye[Se.name]=De(ve[Ie])}return{path:be,params:ye}}}function S(_,C={}){const{delimiter:v=l,end:A=!0,sensitive:D=!1,trailing:P=!0}=C,k=[],I=[],ue=D?"":"i",be=(Array.isArray(_)?_:[_]).map(Se=>Se instanceof W?Se:Z(Se,C));for(const{tokens:Se}of be)for(const De of H(Se,0,[])){const Ae=ee(De,v,k);I.push(Ae)}let ye=`^(?:${I.join("|")})`;return P&&(ye+=`(?:${ne(v)}$)?`),ye+=A?"$":`(?=${ne(v)}|$)`,{regexp:new RegExp(ye,ue),keys:k}}function*H(_,C,v){if(C===_.length)return yield v;const A=_[C];if(A.type==="group"){const D=v.slice();for(const P of H(A.tokens,0,D))yield*mt(H(_,C+1,P))}else v.push(A);yield*mt(H(_,C+1,v))}function ee(_,C,v){let A="",D="",P=!0;for(let k=0;k<_.length;k++){const I=_[k];if(I.type==="text"){A+=ne(I.value),D+=I.value,P||(P=I.value.includes(C));continue}if(I.type==="param"||I.type==="wildcard"){if(!P&&!D)throw new TypeError(`Missing text after "${I.name}": ${j}`);I.type==="param"?A+=`(${F(C,P?"":D)}+)`:A+="([\\s\\S]+)",v.push(I),D="",P=!1;continue}}return A}function F(_,C){return C.length<2?_.length<2?`[^${ne(_+C)}]`:`(?:(?!${ne(_)})[^${ne(C)}])`:_.length<2?`(?:(?!${ne(C)})[^${ne(_)}])`:`(?:(?!${ne(C)}|${ne(_)})[\\s\\S])`}function ae(_){return _.tokens.map(function C(v,A,D){if(v.type==="text")return ie(v.value);if(v.type==="group")return`{${v.tokens.map(C).join("")}}`;const k=le(v.name)&&te(D[A+1])?v.name:JSON.stringify(v.name);if(v.type==="param")return`:${k}`;if(v.type==="wildcard")return`*${k}`;throw new TypeError(`Unexpected token: ${v}`)}).join("")}function le(_){const[C,...v]=_;return $.test(C)?v.every(A=>J.test(A)):!1}function te(_){return(_==null?void 0:_.type)!=="text"?!0:!J.test(_.value[0])}},64599:function(pe,oe,m){var l=m(96263);function G($,J){var j=typeof Symbol!="undefined"&&$[Symbol.iterator]||$["@@iterator"];if(!j){if(Array.isArray($)||(j=l($))||J&&$&&typeof $.length=="number"){j&&($=j);var d=0,ie=function(){};return{s:ie,n:function(){return d>=$.length?{done:!0}:{done:!1,value:$[d++]}},e:function(Z){throw Z},f:ie}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var ne=!0,p=!1,U;return{s:function(){j=j.call($)},n:function(){var Z=j.next();return ne=Z.done,Z},e:function(Z){p=!0,U=Z},f:function(){try{!ne&&j.return!=null&&j.return()}finally{if(p)throw U}}}}pe.exports=G,pe.exports.__esModule=!0,pe.exports.default=pe.exports}}]);
}());