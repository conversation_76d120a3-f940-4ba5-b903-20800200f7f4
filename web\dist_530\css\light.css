
.ant-pro-sider{
    border-right:1px solid #D4D4D7;
  }
  .ant-menu-item-selected{
    color: #191813 !important;
  }
  
  .ant-tabs .ant-tabs-ink-bar{
    color: #87A9FF;
  }
.ant-pro-card{
    border-radius: 0px;
    background-color: #EDEEF1;
    color:#282A2C;
    box-shadow: 0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12);
  }
.ant-pro-card .ant-pro-card-title,.ant-pro-table-list-toolbar-title{
    color:#282A2C;
  }
  
.ant-pro-layout .ant-pro-sider-actions{
    border-top: 1px solid #D4D4D7;
  }

  .ant-tabs-card >.ant-tabs-nav .ant-tabs-tab-active{
    background: #076eff;
  }
  
  .ant-tabs .ant-tabs-tab{
    color:#282A2C;
  }
  .ant-card{
    background: #fafafa;
  }
  .ant-layout-sider .ant-layout-sider-trigger{
    color:rgba(0, 0, 0, 0.8);
    background: #fff;
    border-right: 1px solid #D4D4D7;
    height:49px;
  }
  .ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline{
    background: #F3F3F6;
  }
a{
  color:#282a2c;
}
.ant-menu-light .ant-menu-item-selected{
  background-color:#C6c6c9;
}
.ant-card-body .ant-list{
  background-color: #fff;
}