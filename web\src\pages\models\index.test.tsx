import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import Models from './index';

// Mock antd components
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  Table: ({ columns, dataSource }: any) => (
    <div data-testid="models-table">
      {dataSource?.map((item: any) => (
        <div key={item.key} data-testid="model-row">
          {item.modelName}
        </div>
      ))}
    </div>
  ),
  Card: ({ children }: any) => <div data-testid="models-card">{children}</div>,
  Button: ({ children, ...props }: any) => (
    <button {...props} data-testid="models-button">
      {children}
    </button>
  ),
  Space: ({ children }: any) => <div data-testid="models-space">{children}</div>,
  Typography: {
    Title: ({ children }: any) => <h1 data-testid="models-title">{children}</h1>,
    Text: ({ children }: any) => <span data-testid="models-text">{children}</span>,
  },
  Tag: ({ children }: any) => <span data-testid="models-tag">{children}</span>,
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('Models Page', () => {
  test('renders models page with title', () => {
    renderWithRouter(<Models />);

    expect(screen.getByTestId('models-title')).toHaveTextContent('Models');
  });

  test('renders models table with mock data', () => {
    renderWithRouter(<Models />);

    expect(screen.getByTestId('models-table')).toBeInTheDocument();
    expect(screen.getAllByTestId('model-row')).toHaveLength(5);
  });

  test('renders action buttons', () => {
    renderWithRouter(<Models />);

    const buttons = screen.getAllByTestId('models-button');
    expect(buttons).toHaveLength(2);
    expect(buttons[0]).toHaveTextContent('New Model');
    expect(buttons[1]).toHaveTextContent('Import Model');
  });

  test('displays correct description text', () => {
    renderWithRouter(<Models />);

    const description = screen.getByText(
      'Manage your machine learning models, evaluate performance, and deploy with ease.'
    );
    expect(description).toBeInTheDocument();
  });
});
