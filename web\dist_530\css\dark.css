.ant-pro-sider{
  border-right:1px solid #393B3C;
}
.ant-menu-item-selected{
  /* color: #191813 !important; */
  color:#87A9FF !important;
}


.ant-pro-card,.ant-card{
  border-radius: 0px;
  background-color: #242629;
  color:#C7C8C7;
  box-shadow: 0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12);
}
.ant-table-wrapper .ant-table,.ant-table-wrapper .ant-table-thead >tr>th{
  border-radius: 0px;
  background: #242629;
  color:#C7C8C7;
  box-shadow: 0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12);

}

.ant-table-wrapper .ant-table-tbody .ant-table-row >.ant-table-cell-row-hover{
  background: #2F3133;
}
.ant-pro-card .ant-pro-card-title,.ant-pro-table-list-toolbar-title{
  color:#C7C8C7;
}

.ant-pro-layout .ant-pro-sider-actions{
  border-top: 1px solid #393B3C;
}


/* 右侧背景色 */
.ant-pro-layout .ant-pro-layout-content,.ant-menu-light{
  background-color: #1a1c1e;
}
/* 菜单栏背景色 */
.ant-pro-layout .ant-layout-sider.ant-pro-sider{
  background: #0c0e11;
}
.ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline{
  background: #0c0e11;
}

/* title颜色 */
.ant-pro-layout .ant-pro-sider-logo >a >h1{
  color: #E6E6E9;
}
/* 菜单栏文字颜色 */
.ant-pro-base-menu-inline,.ant-menu-light .ant-menu-item,.ant-menu-inline .ant-menu-submenu-title{
  color: #C7C8C7;
}

.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-submenu-title:hover,.ant-table-wrapper .ant-table-thead >tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before{
  background-color: #2F3133;
}
.ant-tabs .ant-tabs-ink-bar,.ant-btn-variant-solid{
  background: #87A9FF;
}
.ant-tabs-card >.ant-tabs-nav .ant-tabs-tab-active{
  /* background: #87A9FF; */
  color: #87A9FF;
  background:#2C2C2C;
}

.ant-tabs-card.ant-tabs-top >.ant-tabs-nav .ant-tabs-tab-active{
  border-bottom: 0;
}

.ant-tabs .ant-tabs-tab,.ant-card .ant-card-head,.ant-page-header .ant-page-header-heading-title,.ant-list .ant-list-item .ant-list-item-meta .ant-list-item-meta-title,.ant-list .ant-list-item,
.ant-menu-light .ant-menu-submenu-selected >.ant-menu-submenu-title,.ant-menu-light .ant-menu-submenu-title,.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item,.ant-typography,
.ant-typography.ant-typography-secondary,h4:where(.css-s3flbz).ant-typography,h2:where(.css-s3flbz).ant-typography,h5:where(.css-s3flbz).ant-typography,
a:where(.css-s3flbz).ant-typography,.ant-form-item .ant-form-item-label >label,.ant-form-item .ant-form-item-extra,.ant-input-group .ant-input-group-addon,
h4:where(.css-dev-only-do-not-override-yyyr4f).ant-typography,.ant-progress .ant-progress-text,h4:where(.css-yyyr4f).ant-typography,h5:where(.css-yyyr4f).ant-typography{
  color:#C7C8C7;
}
h2:where(.css-dev-only-do-not-override-s3flbz).ant-typography,h5:where(.css-dev-only-do-not-override-s3flbz).ant-typography,h4:where(.css-dev-only-do-not-override-s3flbz).ant-typography{
  color:#C7C8C7;

}
.ant-tabs-top >.ant-tabs-nav::before,.ant-tabs-card >.ant-tabs-nav .ant-tabs-tab,.ant-card-bordered,.ant-card .ant-card-head{
  border: 1px solid #393B3C;
  border-radius:0;
}

.ant-table-wrapper .ant-table-tbody >tr >td,.ant-table-wrapper .ant-table-thead >tr>th{
  border-bottom: 1px solid #393B3C;
}
.ant-btn-variant-outlined,.ant-input-outlined,.ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector,.ant-pagination .ant-pagination-item-active{
  border-color: #2F3133;
  background: #2F3133;
  color:#E6E6E9;
}
.ant-pagination .ant-pagination-item-active a,.ant-pagination .ant-pagination-disabled .ant-pagination-item-link,.ant-list .ant-list-item .ant-list-item-action>li ,.ant-list .ant-list-item .ant-list-item-meta .ant-list-item-meta-description
,.ant-layout-content,.ant-breadcrumb,.ant-breadcrumb li:last-child,.ant-breadcrumb a,.ant-breadcrumb .ant-breadcrumb-separator,.ant-card .ant-card-meta-title>a,
.ant-card .ant-card-meta-description,.acss-1alra7o>span,.ant-radio-wrapper,.ant-btn-variant-text,.ant-select-outlined.ant-select-disabled:not(.ant-select-customize-input) .ant-select-selector,
.ant-form,.ant-checkbox-wrapper{
  color:#E6E6E9;
}

.ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover,.ant-input-outlined:hover,.ant-input-outlined:focus-within{
  border-color: #242629;
  background: #242629;
  color:#C7C8C7;
  box-shadow:0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12);
}

.ant-list-split .ant-list-item{
  border-block-end: 1px solid #393B3C;
}
.ant-btn-color-dangerous.ant-btn-variant-outlined{
  background: #2F3133;
}
/* .ant-menu-light .ant-menu-submenu-selected >.ant-menu-submenu-title{
  background-color: #C0C0C0;
} */
.ant-layout-sider .ant-layout-sider-trigger{
  background: #2C2C2C;
  border-right: 1px solid #393B3C;
  height:49px;
}
a,.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn{
  color:#87A9FF;
}
.ant-menu-light .ant-menu-item-selected{
  /* background-color: #87A9FF; #6c7073*/
  background-color: #2C2C2C;
  color:#87A9FF;
}
.ant-tag,.ant-picker-outlined{
  color:#e6e6e9;
  border: 1px solid #c0c0c0;
  background: #2F3133;
}
.ant-divider{
  border-block-start: 1px solid #393B3C;
}
.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-group-title,.ant-input-outlined[disabled]{
  color:#fff;
}
/* .ant-card-body .ant-list{
  background-color: #1a1c1e;
  box-shadow: none;
} */
:where(.css-dev-only-do-not-override-yyyr4f).ant-progress .ant-progress-inner{
  background-color: #e6e6e9;
}