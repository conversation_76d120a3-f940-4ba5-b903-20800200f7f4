"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7647],{86390:function(Le,B,r){r.r(B),r.d(B,{default:function(){return be}});var L=r(15009),j=r.n(L),K=r(99289),W=r.n(K),k=r(5574),G=r.n(k),l=r(67294),U=r(19735),V=r(17012),X=r(62208),Q=r(29950),J=r(1558),Y=r(93967),x=r.n(Y),q=r(29372),_=r(64217),ee=r(42550),oe=r(96159),ne=r(53124),N=r(11568),te=r(14747),re=r(83559);const b=(e,o,n,t,a)=>({background:e,border:`${(0,N.bf)(t.lineWidth)} ${t.lineType} ${o}`,[`${a}-icon`]:{color:n}}),se=e=>{const{componentCls:o,motionDurationSlow:n,marginXS:t,marginSM:a,fontSize:c,fontSizeLG:f,lineHeight:g,borderRadiusLG:d,motionEaseInOutCirc:u,withDescriptionIconSize:v,colorText:h,colorTextHeading:p,withDescriptionPadding:y,defaultPadding:i}=e;return{[o]:Object.assign(Object.assign({},(0,te.Wf)(e)),{position:"relative",display:"flex",alignItems:"center",padding:i,wordWrap:"break-word",borderRadius:d,[`&${o}-rtl`]:{direction:"rtl"},[`${o}-content`]:{flex:1,minWidth:0},[`${o}-icon`]:{marginInlineEnd:t,lineHeight:0},"&-description":{display:"none",fontSize:c,lineHeight:g},"&-message":{color:p},[`&${o}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${n} ${u}, opacity ${n} ${u},
        padding-top ${n} ${u}, padding-bottom ${n} ${u},
        margin-bottom ${n} ${u}`},[`&${o}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${o}-with-description`]:{alignItems:"flex-start",padding:y,[`${o}-icon`]:{marginInlineEnd:a,fontSize:v,lineHeight:0},[`${o}-message`]:{display:"block",marginBottom:t,color:p,fontSize:f},[`${o}-description`]:{display:"block",color:h}},[`${o}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},ae=e=>{const{componentCls:o,colorSuccess:n,colorSuccessBorder:t,colorSuccessBg:a,colorWarning:c,colorWarningBorder:f,colorWarningBg:g,colorError:d,colorErrorBorder:u,colorErrorBg:v,colorInfo:h,colorInfoBorder:p,colorInfoBg:y}=e;return{[o]:{"&-success":b(a,t,n,e,o),"&-info":b(y,p,h,e,o),"&-warning":b(g,f,c,e,o),"&-error":Object.assign(Object.assign({},b(v,u,d,e,o)),{[`${o}-description > pre`]:{margin:0,padding:0}})}}},le=e=>{const{componentCls:o,iconCls:n,motionDurationMid:t,marginXS:a,fontSizeIcon:c,colorIcon:f,colorIconHover:g}=e;return{[o]:{"&-action":{marginInlineStart:a},[`${o}-close-icon`]:{marginInlineStart:a,padding:0,overflow:"hidden",fontSize:c,lineHeight:(0,N.bf)(c),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${n}-close`]:{color:f,transition:`color ${t}`,"&:hover":{color:g}}},"&-close-text":{color:f,transition:`color ${t}`,"&:hover":{color:g}}}}},ie=e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`});var ce=(0,re.I$)("Alert",e=>[se(e),ae(e),le(e)],ie),D=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(e);a<t.length;a++)o.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(e,t[a])&&(n[t[a]]=e[t[a]]);return n};const de={success:U.Z,info:J.Z,error:V.Z,warning:Q.Z},ue=e=>{const{icon:o,prefixCls:n,type:t}=e,a=de[t]||null;return o?(0,oe.wm)(o,l.createElement("span",{className:`${n}-icon`},o),()=>({className:x()(`${n}-icon`,o.props.className)})):l.createElement(a,{className:`${n}-icon`})},fe=e=>{const{isClosable:o,prefixCls:n,closeIcon:t,handleClose:a,ariaProps:c}=e,f=t===!0||t===void 0?l.createElement(X.Z,null):t;return o?l.createElement("button",Object.assign({type:"button",onClick:a,className:`${n}-close-icon`,tabIndex:0},c),f):null};var O=l.forwardRef((e,o)=>{const{description:n,prefixCls:t,message:a,banner:c,className:f,rootClassName:g,style:d,onMouseEnter:u,onMouseLeave:v,onClick:h,afterClose:p,showIcon:y,closable:i,closeText:I,closeIcon:s,action:T,id:xe}=e,Be=D(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[w,je]=l.useState(!1),z=l.useRef(null);l.useImperativeHandle(o,()=>({nativeElement:z.current}));const{getPrefixCls:Ne,direction:De,closable:S,closeIcon:M,className:Oe,style:Fe}=(0,ne.dj)("alert"),m=Ne("alert",t),[Pe,Ae,Te]=ce(m),we=C=>{var E;je(!0),(E=e.onClose)===null||E===void 0||E.call(e,C)},Z=l.useMemo(()=>e.type!==void 0?e.type:c?"warning":"info",[e.type,c]),ze=l.useMemo(()=>typeof i=="object"&&i.closeIcon||I?!0:typeof i=="boolean"?i:s!==!1&&s!==null&&s!==void 0?!0:!!S,[I,s,i,S]),H=c&&y===void 0?!0:y,Me=x()(m,`${m}-${Z}`,{[`${m}-with-description`]:!!n,[`${m}-no-icon`]:!H,[`${m}-banner`]:!!c,[`${m}-rtl`]:De==="rtl"},Oe,f,g,Te,Ae),Ze=(0,_.Z)(Be,{aria:!0,data:!0}),He=l.useMemo(()=>typeof i=="object"&&i.closeIcon?i.closeIcon:I||(s!==void 0?s:typeof S=="object"&&S.closeIcon?S.closeIcon:M),[s,i,I,M]),Re=l.useMemo(()=>{const C=i!=null?i:S;if(typeof C=="object"){const{closeIcon:E}=C;return D(C,["closeIcon"])}return{}},[i,S]);return Pe(l.createElement(q.ZP,{visible:!w,motionName:`${m}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:C=>({maxHeight:C.offsetHeight}),onLeaveEnd:p},({className:C,style:E},R)=>l.createElement("div",Object.assign({id:xe,ref:(0,ee.sQ)(z,R),"data-show":!w,className:x()(Me,C),style:Object.assign(Object.assign(Object.assign({},Fe),d),E),onMouseEnter:u,onMouseLeave:v,onClick:h,role:"alert"},Ze),H?l.createElement(ue,{description:n,icon:e.icon,prefixCls:m,type:Z}):null,l.createElement("div",{className:`${m}-content`},a?l.createElement("div",{className:`${m}-message`},a):null,n?l.createElement("div",{className:`${m}-description`},n):null),T?l.createElement("div",{className:`${m}-action`},T):null,l.createElement(fe,{isClosable:ze,prefixCls:m,closeIcon:He,handleClose:we,ariaProps:Re}))))}),ge=r(15671),me=r(43144),F=r(61120),pe=r(78814),ve=r(82963);function he(e,o,n){return o=(0,F.Z)(o),(0,ve.Z)(e,(0,pe.Z)()?Reflect.construct(o,n||[],(0,F.Z)(e).constructor):o.apply(e,n))}var ye=r(60136),Ce=function(e){function o(){var n;return(0,ge.Z)(this,o),n=he(this,o,arguments),n.state={error:void 0,info:{componentStack:""}},n}return(0,ye.Z)(o,e),(0,me.Z)(o,[{key:"componentDidCatch",value:function(t,a){this.setState({error:t,info:a})}},{key:"render",value:function(){const{message:t,description:a,id:c,children:f}=this.props,{error:g,info:d}=this.state,u=(d==null?void 0:d.componentStack)||null,v=typeof t=="undefined"?(g||"").toString():t,h=typeof a=="undefined"?u:a;return g?l.createElement(O,{id:c,type:"error",message:v,description:l.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},h)}):f}}])}(l.Component);const P=O;P.ErrorBoundary=Ce;var $e=P,Se=r(74330),A=r(84226),Ie=r(18883),$=r(85893),Ee=function(){var o=(0,l.useState)(null),n=G()(o,2),t=n[0],a=n[1];return(0,l.useEffect)(function(){var c=function(){var f=W()(j()().mark(function g(){var d,u,v,h,p,y,i;return j()().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(s.prev=0,console.log("=== \u5904\u7406Keycloak\u56DE\u8C03\u5F00\u59CB ==="),console.log("\u5F53\u524DURL:",window.location.href),d=new URLSearchParams(window.location.hash.substring(1)),u=d.get("code"),v=d.get("state"),h=d.get("session_state"),p=d.get("error"),y=d.get("error_description"),console.log("\u56DE\u8C03\u53C2\u6570:",{code:u?"\u5B58\u5728":"\u4E0D\u5B58\u5728",state:v,sessionState:h,error:p,errorDescription:y}),!p){s.next=12;break}throw new Error("Keycloak\u9519\u8BEF: ".concat(p," - ").concat(y));case 12:if(!u){s.next=33;break}return console.log("\u68C0\u6D4B\u5230\u6388\u6743\u7801\uFF0C\u5F00\u59CB\u521D\u59CB\u5316Keycloak"),s.prev=14,s.next=17,Ie.Z.init({onLoad:"login-required",checkLoginIframe:!1,redirectUri:window.location.origin+"/auth/callback",silentCheckSsoRedirectUri:window.location.origin+"/silent-check-sso.html",pkceMethod:"S256"});case 17:if(i=s.sent,console.log("Keycloak\u521D\u59CB\u5316\u7ED3\u679C:",i),!i){s.next=24;break}console.log("Keycloak\u8BA4\u8BC1\u6210\u529F\uFF0C\u91CD\u5B9A\u5411\u5230\u4E3B\u9875"),window.location.replace("/Console/projects"),s.next=25;break;case 24:throw new Error("Keycloak\u8BA4\u8BC1\u5931\u8D25");case 25:s.next=31;break;case 27:throw s.prev=27,s.t0=s.catch(14),console.error("Keycloak\u521D\u59CB\u5316\u5931\u8D25:",s.t0),new Error("Keycloak\u521D\u59CB\u5316\u5931\u8D25: ".concat(s.t0.message));case 31:s.next=36;break;case 33:console.log("\u6CA1\u6709\u6388\u6743\u7801\uFF0C\u91CD\u5B9A\u5411\u5230\u767B\u5F55\u9875\u9762"),a("\u65E0\u6548\u7684\u56DE\u8C03\u8BBF\u95EE\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"),setTimeout(function(){A.history.replace("/user/login")},2e3);case 36:s.next=43;break;case 38:s.prev=38,s.t1=s.catch(0),console.error("\u56DE\u8C03\u5904\u7406\u5931\u8D25:",s.t1),a("\u56DE\u8C03\u5904\u7406\u5931\u8D25: ".concat(s.t1.message||"\u672A\u77E5\u9519\u8BEF")),setTimeout(function(){A.history.replace("/user/login")},3e3);case 43:case"end":return s.stop()}},g,null,[[0,38],[14,27]])}));return function(){return f.apply(this,arguments)}}();c()},[]),(0,$.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",flexDirection:"column",padding:"20px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white"},children:t?(0,$.jsx)($e,{message:"\u8BA4\u8BC1\u9519\u8BEF",description:t,type:"error",showIcon:!0,style:{marginBottom:16}}):(0,$.jsxs)($.Fragment,{children:[(0,$.jsx)(Se.Z,{size:"large",style:{color:"white"}}),(0,$.jsx)("div",{style:{marginTop:24,fontSize:"18px",fontWeight:500,textAlign:"center"},children:"\u767B\u5F55\u6210\u529F\uFF01\u6B63\u5728\u8DF3\u8F6C..."}),(0,$.jsx)("div",{style:{marginTop:8,fontSize:"14px",opacity:.8,textAlign:"center"},children:"\u8BF7\u7A0D\u5019\uFF0C\u5373\u5C06\u8FDB\u5165\u7CFB\u7EDF"})]})})},be=Ee}}]);
