"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7244],{49495:function(K,d){var t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};d.Z=t},69753:function(K,d,t){var a=t(1413),v=t(67294),m=t(49495),P=t(91146),y=function(C,p){return v.createElement(P.Z,(0,a.Z)((0,a.Z)({},C),{},{ref:p,icon:m.Z}))},i=v.forwardRef(y);d.Z=i},47389:function(K,d,t){var a=t(1413),v=t(67294),m=t(27363),P=t(91146),y=function(C,p){return v.createElement(P.Z,(0,a.Z)((0,a.Z)({},C),{},{ref:p,icon:m.Z}))},i=v.forwardRef(y);d.Z=i},51042:function(K,d,t){var a=t(1413),v=t(67294),m=t(42110),P=t(91146),y=function(C,p){return v.createElement(P.Z,(0,a.Z)((0,a.Z)({},C),{},{ref:p,icon:m.Z}))},i=v.forwardRef(y);d.Z=i},40110:function(K,d,t){var a=t(1413),v=t(67294),m=t(509),P=t(91146),y=function(C,p){return v.createElement(P.Z,(0,a.Z)((0,a.Z)({},C),{},{ref:p,icon:m.Z}))},i=v.forwardRef(y);d.Z=i},85425:function(K,d,t){var a=t(5574),v=t.n(a),m=t(20057),P=t(85673),y=t(84226),i=t(31622),A=t(67294),C=t(85893),p=function(J){var ne=J.proName,z=J.currentPath,h=J.customItems,fe=(0,m.TH)(),k=(0,m.s0)(),q=(0,y.useIntl)(),ae=(0,A.useState)((0,i.gh)()||"dark"),oe=v()(ae,2),le=oe[0],se=oe[1];(0,A.useEffect)(function(){se((0,i.gh)());var V=new MutationObserver(function(u){u.forEach(function(b){b.attributeName==="data-theme"&&se((0,i.gh)())})});return V.observe(document.body,{attributes:!0}),function(){V.disconnect()}},[]);var ve=function(){if(h)return h;var u=fe.pathname,b=[{path:"/tools",breadcrumbName:"\u5DE5\u5177\u5957\u4EF6"},{path:"/tools/repo",breadcrumbName:"\u4EE3\u7801\u4ED3\u5E93"}];if(u.startsWith("/tools/repo/newProject"))return[].concat(b,[{path:"/tools/repo/newProject",breadcrumbName:"\u65B0\u5EFA\u9879\u76EE"}]);if(u.startsWith("/tools/repo/files/newFile"))return[].concat(b,[{path:"/tools/repo/files",breadcrumbName:"\u6587\u4EF6\u7BA1\u7406"},{path:"/tools/repo/files/newFile",breadcrumbName:"\u65B0\u5EFA\u6587\u4EF6"}]);if(u.startsWith("/tools/repo/files")){var j=[].concat(b,[{path:"/tools/repo/files",breadcrumbName:"\u6587\u4EF6\u7BA1\u7406"}]);if(ne&&j.push({path:"/tools/repo/files",breadcrumbName:ne}),z){var X=z.split("/").filter(Boolean),_="";X.forEach(function(G,ce){_+="/".concat(G),j.push({path:"/tools/repo/files?path=".concat(_),breadcrumbName:G})})}return j}else{if(u.startsWith("/tools/repo/branch"))return[].concat(b,[{path:"/tools/repo/branch",breadcrumbName:"\u5206\u652F\u7BA1\u7406"}]);if(u.startsWith("/tools/repo/commits"))return[].concat(b,[{path:"/tools/repo/commits",breadcrumbName:"\u63D0\u4EA4\u7BA1\u7406"}]);if(u.startsWith("/tools/repo/compare"))return[].concat(b,[{path:"/tools/repo/compare",breadcrumbName:"\u6BD4\u8F83\u4FEE\u8BA2\u7248\u672C"}]);if(u.startsWith("/tools/repo/newTag"))return[].concat(b,[{path:"/tools/repo/tags",breadcrumbName:"\u6807\u7B7E"},{path:"/tools/repo/newTag",breadcrumbName:"\u65B0\u5EFA\u6807\u7B7E"}]);if(u.startsWith("/tools/repo/tags"))return[].concat(b,[{path:"/tools/repo/tags",breadcrumbName:"\u6807\u7B7E"}])}return b};return(0,C.jsx)(P.Z,{separator:"/",style:{fontSize:"14px",marginBottom:"16px",color:le==="light"?"#666":"#aaa"},items:ve(),itemRender:function(u,b,j){var X=j.indexOf(u)===j.length-1;return X?(0,C.jsx)("span",{style:{color:le==="light"?"#333":"#fff"},children:u.breadcrumbName}):(0,C.jsx)("a",{onClick:function(){return u.path?k(u.path):null},style:{color:le==="light"?"#666":"#aaa"},children:u.breadcrumbName})}})};d.Z=p},22307:function(K,d,t){t.r(d),t.d(d,{default:function(){return ge}});var a=t(15009),v=t.n(a),m=t(99289),P=t.n(m),y=t(5574),i=t.n(y),A=t(85425),C=t(7528),p=t(69753),ue=t(47389),J=t(51042),ne=t(40110),z=t(1413),h=t(67294),fe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M839.6 433.8L749 150.5a9.24 9.24 0 00-8.9-6.5h-77.4c-4.1 0-7.6 2.6-8.9 6.5l-91.3 283.3c-.3.9-.5 1.9-.5 2.9 0 5.1 4.2 9.3 9.3 9.3h56.4c4.2 0 7.8-2.8 9-6.8l17.5-61.6h89l17.3 61.5c1.1 4 4.8 6.8 9 6.8h61.2c1 0 1.9-.1 2.8-.4 2.4-.8 4.3-2.4 5.5-4.6 1.1-2.2 1.3-4.7.6-7.1zM663.3 325.5l32.8-116.9h6.3l32.1 116.9h-71.2zm143.5 492.9H677.2v-.4l132.6-188.9c1.1-1.6 1.7-3.4 1.7-5.4v-36.4c0-5.1-4.2-9.3-9.3-9.3h-204c-5.1 0-9.3 4.2-9.3 9.3v43c0 5.1 4.2 9.3 9.3 9.3h122.6v.4L587.7 828.9a9.35 9.35 0 00-1.7 5.4v36.4c0 5.1 4.2 9.3 9.3 9.3h211.4c5.1 0 9.3-4.2 9.3-9.3v-43a9.2 9.2 0 00-9.2-9.3zM416 702h-76V172c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v530h-76c-6.7 0-10.5 7.8-6.3 13l112 141.9a8 8 0 0012.6 0l112-141.9c4.1-5.2.4-13-6.3-13z"}}]},name:"sort-ascending",theme:"outlined"},k=fe,q=t(91146),ae=function(W,S){return h.createElement(q.Z,(0,z.Z)((0,z.Z)({},W),{},{ref:S,icon:k}))},oe=h.forwardRef(ae),le=oe,se=t(83707),ve=function(W,S){return h.createElement(q.Z,(0,z.Z)((0,z.Z)({},W),{},{ref:S,icon:se.Z}))},V=h.forwardRef(ve),u=V,b=t(84017),j=t(71471),X=t(11941),_=t(2453),G=t(40411),ce=t(78957),Ce=t(83062),e=t(83622),s=t(85418),c=t(55102),n=t(4393),l=t(71230),O=t(15746),B=t(84567),H=t(66309),L=t(85265),T=t(34041),R=t(20057),r=t(85893),N=j.Z.Text,he=j.Z.Title,Q=X.Z.TabPane,M=function(W){if(!W)return"\u672A\u77E5\u65F6\u95F4";var S=new Date(W),ee=new Date,F=ee.getTime()-S.getTime(),me=Math.floor(F/1e3),g=Math.floor(me/60),Y=Math.floor(g/60),te=Math.floor(Y/24);return te>30?"".concat(S.getFullYear(),"-").concat((S.getMonth()+1).toString().padStart(2,"0"),"-").concat(S.getDate().toString().padStart(2,"0")):te>0?"".concat(te," \u5929\u524D"):Y>0?"".concat(Y," \u5C0F\u65F6\u524D"):g>0?"".concat(g," \u5206\u949F\u524D"):"\u521A\u521A"},ge=function(){var E=(0,R.TH)(),W=(0,R.s0)(),S=(0,h.useState)("open"),ee=i()(S,2),F=ee[0],me=ee[1],g=(0,h.useState)(!1),Y=i()(g,2),te=Y[0],pe=Y[1],je=(0,h.useState)(null),be=i()(je,2),re=be[0],ie=be[1],Ee=(0,h.useState)([]),xe=i()(Ee,2),w=xe[0],$=xe[1],Oe=(0,h.useState)(!1),U=i()(Oe,2),ye=U[0],de=U[1],De=(0,h.useState)(null),Be=i()(De,2),We=Be[0],Me=Be[1],Ze=function(){var f=P()(v()().mark(function o(Z){var D;return v()().wrap(function(x){for(;;)switch(x.prev=x.next){case 0:return de(!0),x.prev=1,x.next=4,(0,C.GN)({id:Z});case 4:D=x.sent,Array.isArray(D)?(console.log("\u83B7\u53D6\u5230\u7684\u5408\u5E76\u8BF7\u6C42\u5217\u8868:",D),$(D)):$([]),x.next=13;break;case 8:x.prev=8,x.t0=x.catch(1),console.error("\u83B7\u53D6\u5408\u5E76\u8BF7\u6C42\u5217\u8868\u5931\u8D25:",x.t0),_.ZP.error("\u83B7\u53D6\u5408\u5E76\u8BF7\u6C42\u5217\u8868\u5931\u8D25"),$([]);case 13:return x.prev=13,de(!1),x.finish(13);case 16:case"end":return x.stop()}},o,null,[[1,8,13,16]])}));return function(Z){return f.apply(this,arguments)}}();(0,h.useEffect)(function(){if(E.state){var f=E.state,o=f.proId,Z=f.proName,D=f.sourceBranch,I=f.targetBranch;console.log("\u63A5\u6536\u5230\u7684\u53C2\u6570:",o,Z,D,I),o&&(Me(o),Ze(o))}},[E.state]);var Te=function(){var o,Z,D,I,x=(o=E.state)===null||o===void 0?void 0:o.proId,Ae=(Z=E.state)===null||Z===void 0?void 0:Z.proName,Le=(D=E.state)===null||D===void 0?void 0:D.sourceBranch,Ne=(I=E.state)===null||I===void 0?void 0:I.targetBranch;W("/tools/repo/newMergeRequest",{state:{proId:x,proName:Ae,sourceBranch:Le,targetBranch:Ne}})},Re=function(){pe(!0)},Pe=function(){pe(!1),ie(null)},Se=function(o){ie(o===re?null:o)},Ie=function(){re!==null?(_.ZP.success("\u5DF2\u66F4\u65B0\u6240\u6709\u5408\u5E76\u8BF7\u6C42"),pe(!1),ie(null)):_.ZP.warning("\u8BF7\u5148\u9009\u62E9\u4E00\u4E2A\u5408\u5E76\u8BF7\u6C42")};return(0,r.jsxs)(b._z,{header:{title:"",breadcrumb:{}},children:[(0,r.jsx)(A.Z,{customItems:[{path:"/tools",breadcrumbName:"\u5DE5\u5177\u5957\u4EF6"},{path:"/tools/repo",breadcrumbName:"\u4EE3\u7801"},{path:"/tools/repo/mergeRequests",breadcrumbName:"\u5408\u5E76\u8BF7\u6C42"}]}),(0,r.jsxs)("div",{style:{padding:"0",borderRadius:"4px"},children:[(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",padding:"0 16px"},children:[(0,r.jsxs)(X.Z,{activeKey:F,onChange:me,style:{flex:1},children:[(0,r.jsx)(Q,{tab:(0,r.jsxs)("span",{children:["\u5F00\u653E ",(0,r.jsx)(G.Z,{count:w.filter(function(f){return f.state==="opened"}).length,style:{backgroundColor:"#1890ff",marginLeft:4}})]})},"open"),(0,r.jsx)(Q,{tab:(0,r.jsxs)("span",{children:["\u5DF2\u5408\u5E76 ",(0,r.jsx)(G.Z,{count:w.filter(function(f){return f.state==="merged"}).length,style:{backgroundColor:"#52c41a",marginLeft:4}})]})},"merged"),(0,r.jsx)(Q,{tab:(0,r.jsxs)("span",{children:["\u5DF2\u5173\u95ED ",(0,r.jsx)(G.Z,{count:w.filter(function(f){return f.state==="closed"}).length,style:{backgroundColor:"#d9d9d9",marginLeft:4}})]})},"closed"),(0,r.jsx)(Q,{tab:(0,r.jsxs)("span",{children:["\u5168\u90E8 ",(0,r.jsx)(G.Z,{count:w.length,style:{backgroundColor:"#d9d9d9",marginLeft:4}})]})},"all")]}),(0,r.jsxs)(ce.Z,{style:{padding:"8px 0"},children:[(0,r.jsx)(Ce.Z,{title:"\u5BFC\u51FA\u5408\u5E76\u8BF7\u6C42",children:(0,r.jsx)(e.ZP,{icon:(0,r.jsx)(p.Z,{})})}),(0,r.jsx)(e.ZP,{icon:(0,r.jsx)(ue.Z,{}),onClick:Re,children:"\u7F16\u8F91\u5408\u5E76\u8BF7\u6C42"}),(0,r.jsx)(e.ZP,{type:"primary",icon:(0,r.jsx)(J.Z,{}),onClick:Te,style:{background:"#1068bf"},children:"\u65B0\u5EFA\u5408\u5E76\u8BF7\u6C42"})]})]}),(0,r.jsxs)("div",{style:{padding:"16px",display:"flex",justifyContent:"space-between"},children:[(0,r.jsxs)("div",{style:{display:"flex",flex:1},children:[(0,r.jsx)(s.Z,{menu:{items:[{key:"1",label:"\u6700\u8FD1\u641C\u7D22"},{key:"2",label:"\u6211\u7684\u5408\u5E76\u8BF7\u6C42"},{key:"3",label:"\u5206\u914D\u7ED9\u6211\u7684"}]},trigger:["click"],children:(0,r.jsxs)(e.ZP,{style:{borderRight:0,borderTopRightRadius:0,borderBottomRightRadius:0},children:["\u6700\u8FD1\u641C\u7D22 ",(0,r.jsx)(p.Z,{rotate:180,style:{fontSize:12}})]})}),(0,r.jsx)(c.Z,{placeholder:"\u641C\u7D22\u6216\u7B5B\u9009\u7ED3\u679C...",prefix:(0,r.jsx)(ne.Z,{style:{color:"#bfbfbf"}}),style:{borderLeft:0,borderTopLeftRadius:0,borderBottomLeftRadius:0,width:"100%"}})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(s.Z,{menu:{items:[{key:"1",label:"\u521B\u5EFA\u65E5\u671F"},{key:"2",label:"\u6700\u8FD1\u66F4\u65B0"},{key:"3",label:"\u6700\u65E9\u66F4\u65B0"}]},trigger:["click"],children:(0,r.jsxs)(e.ZP,{style:{marginLeft:8},children:["\u521B\u5EFA\u65E5\u671F ",(0,r.jsx)(p.Z,{rotate:180,style:{fontSize:12}})]})}),(0,r.jsx)(e.ZP,{style:{marginLeft:8},children:(0,r.jsx)(le,{})})]})]}),(0,r.jsx)("div",{style:{padding:"0 16px 16px"},children:ye?(0,r.jsx)("div",{style:{textAlign:"center",padding:"20px"},children:(0,r.jsx)("div",{children:"\u52A0\u8F7D\u4E2D..."})}):w.length===0?(0,r.jsx)("div",{style:{textAlign:"center",padding:"20px"},children:(0,r.jsx)("div",{children:"\u6682\u65E0\u5408\u5E76\u8BF7\u6C42"})}):function(){var f=w.filter(function(o){return F==="open"?o.state==="opened":F==="merged"?o.state==="merged":F==="closed"?o.state==="closed":!0});return f.length===0?(0,r.jsx)("div",{style:{textAlign:"center",padding:"20px"},children:(0,r.jsx)("div",{children:"\u5F53\u524D\u72B6\u6001\u4E0B\u6682\u65E0\u5408\u5E76\u8BF7\u6C42"})}):f.map(function(o){var Z;return(0,r.jsx)(n.Z,{style:{marginBottom:16,cursor:"pointer",border:re===o.id?"2px solid #1890ff":"1px solid #f0f0f0"},styles:{body:{padding:"16px"}},onClick:function(){return Se(o.id)},children:(0,r.jsxs)(l.Z,{align:"middle",children:[(0,r.jsx)(O.Z,{span:1,children:(0,r.jsx)(B.Z,{checked:re===o.id,onChange:function(I){I.target.checked?Se(o.id):ie(null),I.stopPropagation()},onClick:function(I){return I.stopPropagation()}})}),(0,r.jsx)(O.Z,{span:23,children:(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(he,{level:5,style:{margin:0},children:o.title}),(0,r.jsxs)(N,{type:"secondary",children:["!",o.iid," \xB7 \u521B\u5EFA\u4E8E ",M(o.created_at)," \u7531 ",((Z=o.author)===null||Z===void 0?void 0:Z.name)||"\u672A\u77E5\u7528\u6237"]}),(0,r.jsxs)(H.Z,{color:"#108ee9",style:{marginLeft:8},children:[o.source_branch," \u2192 ",o.target_branch]})]}),(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[o.merge_status==="cannot_be_merged"&&(0,r.jsx)(Ce.Z,{title:"\u6B64\u5408\u5E76\u8BF7\u6C42\u6709\u51B2\u7A81",children:(0,r.jsx)(u,{style:{color:"#faad14",marginRight:8,fontSize:16}})}),(0,r.jsxs)(N,{type:"secondary",children:["\u66F4\u65B0\u4E8E ",M(o.updated_at)]})]})]})})]})},o.id)})}()})]}),(0,r.jsxs)(L.Z,{title:"SuLei > Workbench_v3 > \u5408\u5E76\u8BF7\u6C42",placement:"right",onClose:Pe,open:te,width:400,mask:!1,maskClosable:!1,extra:(0,r.jsxs)(ce.Z,{children:[(0,r.jsx)(e.ZP,{onClick:Ie,type:"primary",disabled:re===null,children:"\u66F4\u65B0\u5168\u90E8"}),(0,r.jsx)(e.ZP,{onClick:Pe,children:"\u53D6\u6D88"})]}),children:[(0,r.jsxs)("div",{style:{marginBottom:24},children:[(0,r.jsx)("div",{style:{marginBottom:8},children:"\u72B6\u6001"}),(0,r.jsx)(T.Z,{placeholder:"\u9009\u62E9\u72B6\u6001",style:{width:"100%"},options:[{value:"open",label:"\u5F00\u653E"},{value:"merged",label:"\u5DF2\u5408\u5E76"},{value:"closed",label:"\u5DF2\u5173\u95ED"}]})]}),(0,r.jsxs)("div",{style:{marginBottom:24},children:[(0,r.jsx)("div",{style:{marginBottom:8},children:"\u6307\u6D3E\u4EBA"}),(0,r.jsx)(T.Z,{placeholder:"\u9009\u62E9\u6307\u6D3E\u4EBA",style:{width:"100%"},options:[{value:"sulei",label:"SuLei"},{value:"unassigned",label:"\u672A\u6307\u6D3E"}]})]}),(0,r.jsxs)("div",{style:{marginBottom:24},children:[(0,r.jsx)("div",{style:{marginBottom:8},children:"\u91CC\u7A0B\u7891"}),(0,r.jsx)(T.Z,{placeholder:"\u9009\u62E9\u91CC\u7A0B\u7891",style:{width:"100%"},options:[{value:"none",label:"\u65E0\u91CC\u7A0B\u7891"}]})]}),(0,r.jsxs)("div",{style:{marginBottom:24},children:[(0,r.jsx)("div",{style:{marginBottom:8},children:"\u6807\u7B7E"}),(0,r.jsx)(T.Z,{placeholder:"\u9009\u62E9\u6807\u7B7E",style:{width:"100%"},mode:"multiple",options:[{value:"none",label:"\u65E0\u6807\u7B7E"}]})]}),(0,r.jsxs)("div",{style:{marginBottom:24},children:[(0,r.jsx)("div",{style:{marginBottom:8},children:"\u8BA2\u9605"}),(0,r.jsx)(T.Z,{placeholder:"\u9009\u62E9\u8BA2\u9605",style:{width:"100%"},options:[{value:"participating",label:"\u53C2\u4E0E\u7684"},{value:"all",label:"\u6240\u6709\u6D3B\u52A8"},{value:"none",label:"\u4E0D\u8BA2\u9605"}]})]})]})]})}},15746:function(K,d,t){var a=t(21584);d.Z=a.Z},71230:function(K,d,t){var a=t(17621);d.Z=a.Z},66309:function(K,d,t){t.d(d,{Z:function(){return Ce}});var a=t(67294),v=t(93967),m=t.n(v),P=t(98423),y=t(98787),i=t(69760),A=t(96159),C=t(45353),p=t(53124),ue=t(11568),J=t(15063),ne=t(14747),z=t(83262),h=t(83559);const fe=e=>{const{paddingXXS:s,lineWidth:c,tagPaddingHorizontal:n,componentCls:l,calc:O}=e,B=O(n).sub(c).equal(),H=O(s).sub(c).equal();return{[l]:Object.assign(Object.assign({},(0,ne.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:B,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,ue.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${l}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${l}-close-icon`]:{marginInlineStart:H,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${l}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${l}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:B}}),[`${l}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},k=e=>{const{lineWidth:s,fontSizeIcon:c,calc:n}=e,l=e.fontSizeSM;return(0,z.IX)(e,{tagFontSize:l,tagLineHeight:(0,ue.bf)(n(e.lineHeightSM).mul(l).equal()),tagIconSize:n(c).sub(n(s).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},q=e=>({defaultBg:new J.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var ae=(0,h.I$)("Tag",e=>{const s=k(e);return fe(s)},q),oe=function(e,s){var c={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&s.indexOf(n)<0&&(c[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)s.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(c[n[l]]=e[n[l]]);return c},se=a.forwardRef((e,s)=>{const{prefixCls:c,style:n,className:l,checked:O,onChange:B,onClick:H}=e,L=oe(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:T,tag:R}=a.useContext(p.E_),r=E=>{B==null||B(!O),H==null||H(E)},N=T("tag",c),[he,Q,M]=ae(N),ge=m()(N,`${N}-checkable`,{[`${N}-checkable-checked`]:O},R==null?void 0:R.className,l,Q,M);return he(a.createElement("span",Object.assign({},L,{ref:s,style:Object.assign(Object.assign({},n),R==null?void 0:R.style),className:ge,onClick:r})))}),ve=t(98719);const V=e=>(0,ve.Z)(e,(s,{textColor:c,lightBorderColor:n,lightColor:l,darkColor:O})=>({[`${e.componentCls}${e.componentCls}-${s}`]:{color:c,background:l,borderColor:n,"&-inverse":{color:e.colorTextLightSolid,background:O,borderColor:O},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}));var u=(0,h.bk)(["Tag","preset"],e=>{const s=k(e);return V(s)},q);function b(e){return typeof e!="string"?e:e.charAt(0).toUpperCase()+e.slice(1)}const j=(e,s,c)=>{const n=b(c);return{[`${e.componentCls}${e.componentCls}-${s}`]:{color:e[`color${c}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var X=(0,h.bk)(["Tag","status"],e=>{const s=k(e);return[j(s,"success","Success"),j(s,"processing","Info"),j(s,"error","Error"),j(s,"warning","Warning")]},q),_=function(e,s){var c={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&s.indexOf(n)<0&&(c[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)s.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(c[n[l]]=e[n[l]]);return c};const ce=a.forwardRef((e,s)=>{const{prefixCls:c,className:n,rootClassName:l,style:O,children:B,icon:H,color:L,onClose:T,bordered:R=!0,visible:r}=e,N=_(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:he,direction:Q,tag:M}=a.useContext(p.E_),[ge,E]=a.useState(!0),W=(0,P.Z)(N,["closeIcon","closable"]);a.useEffect(()=>{r!==void 0&&E(r)},[r]);const S=(0,y.o2)(L),ee=(0,y.yT)(L),F=S||ee,me=Object.assign(Object.assign({backgroundColor:L&&!F?L:void 0},M==null?void 0:M.style),O),g=he("tag",c),[Y,te,pe]=ae(g),je=m()(g,M==null?void 0:M.className,{[`${g}-${L}`]:F,[`${g}-has-color`]:L&&!F,[`${g}-hidden`]:!ge,[`${g}-rtl`]:Q==="rtl",[`${g}-borderless`]:!R},n,l,te,pe),be=$=>{$.stopPropagation(),T==null||T($),!$.defaultPrevented&&E(!1)},[,re]=(0,i.Z)((0,i.w)(e),(0,i.w)(M),{closable:!1,closeIconRender:$=>{const Oe=a.createElement("span",{className:`${g}-close-icon`,onClick:be},$);return(0,A.wm)($,Oe,U=>({onClick:ye=>{var de;(de=U==null?void 0:U.onClick)===null||de===void 0||de.call(U,ye),be(ye)},className:m()(U==null?void 0:U.className,`${g}-close-icon`)}))}}),ie=typeof N.onClick=="function"||B&&B.type==="a",Ee=H||null,xe=Ee?a.createElement(a.Fragment,null,Ee,B&&a.createElement("span",null,B)):B,w=a.createElement("span",Object.assign({},W,{ref:s,className:je,style:me}),xe,re,S&&a.createElement(u,{key:"preset",prefixCls:g}),ee&&a.createElement(X,{key:"status",prefixCls:g}));return Y(ie?a.createElement(C.Z,{component:"Tag"},w):w)});ce.CheckableTag=se;var Ce=ce}}]);
