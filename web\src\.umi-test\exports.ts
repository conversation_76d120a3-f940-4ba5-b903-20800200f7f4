// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
// defineApp
export { defineApp } from './core/defineApp'
export type { RuntimeConfig } from './core/defineApp'
// plugins
export { Access, useAccess, useAccessMarkedRoutes } from 'D:/project/web_app_0527v2/web/src/.umi-test/plugin-access';
export { addLocale, setLocale, getLocale, getIntl, useIntl, injectIntl, formatMessage, FormattedMessage, getAllLocales, FormattedDate, FormattedDateParts, FormattedDisplayName, FormattedHTMLMessage, FormattedList, FormattedNumber, FormattedNumberParts, FormattedPlural, FormattedRelativeTime, FormattedTime, FormattedTimeParts, IntlProvider, RawIntlProvider, SelectLang } from 'D:/project/web_app_0527v2/web/src/.umi-test/plugin-locale';
export { Provider, useModel } from 'D:/project/web_app_0527v2/web/src/.umi-test/plugin-model';
export { useRequest, UseRequestProvider, request, getRequestInstance } from 'D:/project/web_app_0527v2/web/src/.umi-test/plugin-request';
// plugins types.d.ts
export * from 'D:/project/web_app_0527v2/web/src/.umi-test/plugin-access/types.d';
export * from 'D:/project/web_app_0527v2/web/src/.umi-test/plugin-antd/types.d';
export * from 'D:/project/web_app_0527v2/web/src/.umi-test/plugin-layout/types.d';
export * from 'D:/project/web_app_0527v2/web/src/.umi-test/plugin-request/types.d';
// @umijs/renderer-*
export { createBrowserHistory, createHashHistory, createMemoryHistory, Helmet, HelmetProvider, createSearchParams, generatePath, matchPath, matchRoutes, Navigate, NavLink, Outlet, resolvePath, useLocation, useMatch, useNavigate, useOutlet, useOutletContext, useParams, useResolvedPath, useRoutes, useSearchParams, useAppData, useClientLoaderData, useLoaderData, useRouteProps, useSelectedRoutes, useServerLoaderData, renderClient, __getRoot, Link, useRouteData, __useFetcher, withRouter } from 'D:/project/web_app_0527v2/web/node_modules/@umijs/renderer-react';
export type { History, ClientLoader } from 'D:/project/web_app_0527v2/web/node_modules/@umijs/renderer-react'
// umi/client/client/plugin
export { ApplyPluginsType, PluginManager } from 'D:/project/web_app_0527v2/web/node_modules/umi/client/client/plugin.js';
export { history, createHistory } from './core/history';
export { terminal } from './core/terminal';
// react ssr
export const useServerInsertedHTML: Function = () => {};
// test
export { TestBrowser } from './testBrowser';
